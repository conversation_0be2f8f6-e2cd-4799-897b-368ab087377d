version: '2'
services:
  merchant-contract-job:
    build: .
    image: registry.wosai-inc.com/merchant-contract-job:$tag
    restart: always
    container_name: merchant-contract-job
    environment:
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx3096m -XX:-UseGCOverheadLimit -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/log/heapdump.hprof -Dspring.profiles.active=test
      - logDir=/app/log
    ports:
      - "11157:8080"
    volumes:
      - /app/log/merchant-contract-job:/app/log
      - /app/log/merchant-contract-job/wechat_applet_code:/app/log/wechat_applet_code
      - /opt/data:/opt/data
      - /opt/settings:/opt/settings
    network_mode: bridge
