#自定义公众号白名单
CREATE TABLE `merchant_app_module_white` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `merchantId` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '商户ID',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_uni` (`merchant_id`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


#自定义公众号任务表
CREATE TABLE `custom_appid_apply` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `provider_service_vendor` int(10) NOT NULL COMMENT '交易通道服务提供方 1 拉卡拉 2 威富通 3 万码 4 网联 5 银联',
  `channel_no` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道号',
  `parent_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道号',
  `merchant_sn` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '服务商商户号',
  `provider_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行商户号',
  `weixin_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信商户号',
  `pay_appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'pay_appid',
  `appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'appid',
  `mini_appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '小程序appid',
  `license_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '营业执照商户名',
  `merchant_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户名',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '报名状态 0:提交成功, 1:报备成功，2: 报备失败，3:配置成功,切agent成功 4:配置失败 5:切agent失败 6:回切成功',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0:自定义关注公众号 1:取消关注公众号',
  `username` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作人',
  `request_reason` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '取消原因',
  `extra` text COLLATE utf8mb4_bin,
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `begin_time` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `sn_uni` (`merchant_sn`),
  KEY `status_idx` (`status`),
  KEY `app_idx` (`appid`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


#自定义公众号任务表
CREATE TABLE `lz_apply` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `provider` int(10) NOT NULL COMMENT '交易通道服务提供方 1011 万码 1013 网联 1014 银联 1015 内蒙古银联',
  `channel` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道信息',
  `merchant_sn` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '服务商商户号',
  `provider_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行商户号',
  `weixin_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信商户号',
  `appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'appid',
  `license_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '营业执照商户名',
  `merchant_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户名',
  `merchant_fee` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户费率',
  `merchant_industry` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户行业id',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '报名状态 0:提交成功, 1:报备成功，2: 报备失败，3:配置成功,切agent成功 4:配置失败 5:切agent失败',
  `request_source` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作方：CRM',
  `extra` text COLLATE utf8mb4_bin,
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `sn_uni` (`merchant_sn`),
  KEY `status_idx` (`status`),
  KEY `app_idx` (`appid`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;



#appid渠道关系映射表
CREATE TABLE `channel_appid_map` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `provider_service_vendor` int(10) NOT NULL COMMENT '交易通道服务提供方 1 拉卡拉 2 威富通 3 万码 4 网联 5 银联',
  `channel_no` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道号',
  `channel_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道名',
  `appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'appid',
  `appid_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公众号名',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0:公众号 1:小程序',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;


insert into channel_appid_map values
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wx899c14a139b2ba82', '北京', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wx72534f3638c59073', '上海', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wxb0bd91aa640a954c', '广州', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wx59211250e712e3fd', '深圳', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wx7d44dab5b915035c', '武汉', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wxd08e44e6e6b8a218', '长沙', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wxf9114ca5d9b45bd5', '南京', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wxc6ca2c84581752bb', '杭州', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wxe4ab1729629e9ade', '成都', 0, 0, 1),
(uuid(), 3, '36002013293', '上海喔噻互联网科技有限公司', 'wx457b9713a0878d37', '重庆', 0, 0, 1),
(uuid(), 3, '36002013820', '苏州喔噻互联网科技有限公司', 'wx0fd179d3b11b7b34', '苏州', 0, 0, 1),
(uuid(), 3, '36TB1213208', '苏州喔噻互联网科技有限公司(绿洲)', 'wx0fd179d3b11b7b34', '苏州', 0, 0, 1);


alter table custom_appid_apply add column `old_pay_appid` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信old appid' after `pay_appid`;

ALTER TABLE merchant_provider_trade_params DROP INDEX `idx_provider_merchant_id`;
CREATE INDEX idx_provider_merchant_id ON merchant_provider_trade_params (`provider_merchant_id`);

/**
   客服电话修改时间
 */
CREATE TABLE `weixin_params_update_apply` (
  `id` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `channel_no` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '渠道号',
  `merchant_sn` varchar(36) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '服务商商户号',
  `weixin_merchant_id` varchar(36) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信商户号',
  `merchant_short_name` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信商户简称',
  `service_phone` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客服电话',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `short_name_mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `sn_uni` (`merchant_sn`),
  UNIQUE KEY `weixin_merchant_id_uni` (`weixin_merchant_id`),
  KEY `idx_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

