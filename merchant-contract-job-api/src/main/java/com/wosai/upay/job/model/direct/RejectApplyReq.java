package com.wosai.upay.job.model.direct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/12/20
 */
@Data
@Accessors(chain = true)
public class RejectApplyReq {

    @NotBlank(message = "商户号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;

    @NotBlank(message = "应用标识不能为空")
    @JsonProperty("dev_code")
    private String devCode;

    @NotBlank(message = "驳回原因不能为空")
    private String message;
}
