package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.cua.model.page.NormalPagingResult;
import com.wosai.upay.job.model.dto.request.GroupRouteRuleDetailRepDTO;
import com.wosai.upay.job.model.dto.request.GroupRouteRuleFilterReqDTO;
import com.wosai.upay.job.model.dto.request.GroupRouteRulesDecisionReqDTO;
import com.wosai.upay.job.model.dto.response.GroupRouteRuleDetailRspDTO;
import com.wosai.upay.job.model.dto.response.GroupRouteRulesDecisionRspDTO;
import com.wosai.upay.job.model.dto.response.McRuleGroupRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 进件路由规则决策服务
 *
 * <AUTHOR>
 * @date 2024/3/7 17:05
 */
@JsonRpcService("/rpc/group/rule")
@Validated
public interface GroupRouteRulesDecisionService {

    /**
     * 根据筛选条件分页获取进件路由规则决策
     *
     * @param filterReqDTO 进件路由规则过滤条件
     * @return 节点进件路由规则决策
     */
    NormalPagingResult<GroupRouteRulesDecisionRspDTO> pageGroupRouteRules(@Valid GroupRouteRuleFilterReqDTO filterReqDTO);


    /**
     * 获取所有的顶层节点进件路由规则决策
     *
     * @return 顶层节点进件路由规则决策
     */
    List<GroupRouteRulesDecisionRspDTO> listAllTopNodeGroupRouteRules();

    /**
     * 根据id获取进件路由规则决策
     *
     * @param id 规则决策id
     * @return 进件路由规则决策
     */
    GroupRouteRulesDecisionRspDTO getGroupRouteRulesDecisionById(@NotNull Long id);

    /**
     * 根据id获取进件路由规则决策的详细信息
     *
     * @param id 规则决策id
     * @return 进件路由规则决策的详细信息
     */
    GroupRouteRuleDetailRspDTO getGroupRouteRuleDetailById(@NotNull Long id);


    /**
     * 新增进件路由规则决策
     *
     * @param groupRouteRulesDecisionReqDTO 进件路由规则决策
     * @return effect rows
     */
    Integer insertGroupRouteRule(@Valid GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO);


    /**
     * 更新进件路由规则决策
     *
     * @param groupRouteRulesDecisionReqDTO 进件路由规则决策
     * @return effect rows
     */
    Integer updateGroupRouteRule(@Valid GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO);


    /**
     * 根据id列表修改进件路由规则决策状态
     *
     * @param ids    id列表
     * @param status 状态
     * @return effect rows
     */
    Integer updateGroupRouteRuleStatusByIds(@NotEmpty List<Long> ids, Integer status);


    /**
     * 新增进件路由规则决策detail
     *
     * @param groupRouteRuleDetailReqDTO 进件路由规则决策detail
     * @return effect rows
     */
    Integer insertGroupRouteRuleDetail(@Valid GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO);


    /**
     * 更新进件路由规则决策detail
     *
     * @param groupRouteRuleDetailReqDTO 进件路由规则决策detail
     * @return effect rows
     */
    Integer updateGroupRouteRuleDetail(@Valid GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO);


    /**
     * 根据id删除进件路由规则决策
     *
     * @param id 主键id
     * @return effect rows
     */
    Integer deleteGroupRouteRuleById(@NotNull Long id);


    /**
     * 根据id删除进件路由规则决策细节
     *
     * @param id 主键id
     * @return effect rows
     */
    Integer deleteGroupRouteRuleDetailById(@NotNull Long id);

    /**
     * 查询优先级使用状态
     *
     * @param priority 优先级
     * @return 是否已被占用 0-未被占用 1-已被占用
     */
    Integer getPriorityUseStatus(@NotNull Integer priority);

    /**
     * 根据收单机构标识获取进件报备规则组
     *
     * @param acquirer 收单机构
     * @return 报备规则组列表
     */
    List<McRuleGroupRspDTO> listGroupIdsByAcquirer(@NotBlank String acquirer);

}
