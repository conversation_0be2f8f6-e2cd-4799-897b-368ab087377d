package com.wosai.upay.job.model.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 收单组路由规则决策表表Request DTO对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("收单组路由规则决策表表Request DTO对象")
public class GroupRouteRulesDecisionReqDTO {


    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("父规则决策id 0代表最上层规则决策")
    private Long parentId;

    @ApiModelProperty("进件规则决策名称")
    @Size(max = 200, message = "进件规则决策名称长度不能超过200")
    private String name;

    @ApiModelProperty("进件规则决策分类")
    @Size(max = 100, message = "进件规则决策分类长度不能超过100")
    private String classification;

    @ApiModelProperty("优先级 嵌套的子规则优先级为null")
    @Max(value = 65534, message = "优先级不能大于65534")
    private Integer priority;

    @ApiModelProperty("子规则连接类型(嵌套子规则) AND 或者是 OR")
    private String ruleConnectionType;

    @ApiModelProperty("规则详情连接类型 AND 或者是 OR")
    private String ruleDetailConnectionType;

    @ApiModelProperty("选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择,描述满足规则时，是选择,不可选择还是只可选择对应的group_id")
    private String chooseType;

    @ApiModelProperty("入网收单组策略类型 1-一主无备 2-一主一备 3-一主多备 4-一主一备+每日额度 5-负载比例")
    private Integer groupCombinedStrategyType;

    @ApiModelProperty("收单组策略列表")
    private List<GroupCombinedStrategyRepDTO> groupCombinedStrategyRepDTOList;

    @ApiModelProperty("备注")
    @Size(max = 500, message = "备注长度不能超过500")
    private String remark;

    @ApiModelProperty("有效状态 0-失效 1-生效")
    @NotNull(message = "有效状态不能为空")
    private Integer validStatus;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("子规则决策节点")
    private List<GroupRouteRulesDecisionReqDTO> childrenRulesDecisionNodeList;

    @ApiModelProperty("规则决策节点对应的规则detail")
    private List<GroupRouteRuleDetailRepDTO> groupRouteRuleDetailRepDTOList;
}

