package com.wosai.upay.job.model.onlinePayment;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Data
@Accessors(chain = true)
public class OnlinePaymentOpenResp {

    /**
     * 状态  1:开通中 2:开通成功 3:开通失败
     */
    private Integer status;

    /**
     * 错误信息，开通失败时必传
     */
    private String failMsg;

    /**
     * 是不是关闭之后重新开通
     */
    private boolean reOpen;

}
