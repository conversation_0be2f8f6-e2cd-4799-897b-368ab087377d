package com.wosai.upay.job.model.acquirer;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-16
 */
@Data
@Accessors(chain = true)
public class SyncMchStatusResp {

    /**
     * 同步结果
     */
    private boolean success;

    /**
     * 描述
     */
    private String message;

    /**
     * 子商户号的同步结果
     */
    private List<SyncSubMchIdStatusResp> subMchIdSyncResult;
}
