package com.wosai.upay.job.model.dto;

import com.shouqianba.workflow.bean.CallBackBean;
import lombok.Data;

import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/12/10 15:17
 */
@Data
public class QueryAuthStatusDTO {

    /**
     *批量申请传的附件地址
     */
    private List<String> attachmentUrls;

    /**
     * 审批模板
     */
    private CallBackBean callBackBean;

    /**
     * 审批编号
     */
    private String auditSn;


    /**
     * 审批发起人名称
     */
    private String operatorName;

    /**
     * 审批发起人id
     */
    private String operator;
}
