package com.wosai.upay.job.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 终端重新绑定
 * <AUTHOR>
 * @Date 2023/12/12 上午9:36
 */
@Data
public class RebindTerminal {
    /**
     * 收单机构终端号
     */
    @NotBlank(message = "收单机构终端号必传")
    private String providerTerminalId;

    /**
     * 子商户号,
     * 不填:绑定未与该终端绑定的所有支付源子商户号
     * 填:绑定该终端与支付源子商户号
     */
    private String subMcNo;

    /**
     *强制重新绑定(无视调用频率限制,且当未指定子商户时,为当前绑定该通道下所有子商户)
     */
    private Boolean force = Boolean.FALSE;

    /**
     *商户号
     */
    private String merchantSn;

    /**
     *门店号
     */
    private String StoreSn;

    /**
     *收钱吧终端号
     */
    private String terminalSn;


    /**
     *收单机构
     */
    private Integer provider;








}
