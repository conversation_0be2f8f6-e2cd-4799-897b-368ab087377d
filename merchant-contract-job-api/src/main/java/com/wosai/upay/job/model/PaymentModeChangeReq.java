package com.wosai.upay.job.model;

import com.wosai.upay.job.validation.In;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 切换支付模式
 * <AUTHOR>
 * @date 2024/9/12
 */
@Data
public class PaymentModeChangeReq {
    /**
     * 商家模式
     */
    public static final Integer TARGET_PAYMENT_MODE_MERCHANT = 2;
    /**
     * 微信品牌模式
     */
    public static final Integer TARGET_PAYMENT_MODE_WECHAT = 3;
    /**
     * 支付宝品牌模式
     */
    public static final Integer TARGET_PAYMENT_MODE_ALIPAY = 4;
    /**
     * 微信支付宝品牌模式
     */
    public static final Integer TARGET_PAYMENT_MODE_ALIPAY_WECHAT = 5;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 目标支付模式 2商家模式 3微信品牌模式 4支付宝品牌模式 5微信支付宝品牌模式
     */
    @NotNull(message = "目标支付模式不能为空")
    @In(values = {2,3,4,5}, message = "目标支付模式只能为2、3、4、5")
    private Integer targetPaymentMode;

    /**
     * 提交平台 可以为空
     */
    private String platform;
    /**
     * 操作人 ID 可以为空
     */
    private String operatorId;
    /**
     * 备注 可以为空
     */
    private String remark;
}
