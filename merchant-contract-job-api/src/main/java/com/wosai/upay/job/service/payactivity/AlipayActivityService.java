package com.wosai.upay.job.service.payactivity;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.application.CommonResult;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: 支付宝活动
 * <AUTHOR>
 * @Date: 2021/7/27 5:00 下午
 */
@JsonRpcService("/rpc/alipayActivity")
@Validated
public interface AlipayActivityService {


    //创建task
    CommonResult createTaskApply(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                                 @NotNull(message = "auditId 不能为空") Long auditId, @NotNull(message = "activityType 不能为空") int activityType, Map formBody);

    /**
     * 审批拒绝
     *
     * @param merchantSn   商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody
     * @return
     */
    CommonResult auditReject(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                             @NotNull(message = "auditId 不能为空") Long auditId, @NotNull(message = "activityType 不能为空") int activityType, Map formBody);


    /**
     * 审批通过
     *
     * @param merchantSn   商户号
     * @param auditId      审批ID
     * @param activityType 活动类型
     * @param formBody
     * @return
     */
    CommonResult auditApprove(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                              @NotNull(message = "auditId 不能为空") Long auditId, @NotNull(message = "activityType 不能为空") int activityType, Map formBody);


}
