package com.wosai.upay.job.Constants;

/**
 * 商户信息变更和授权任务相关常量
 *
 */
public class MerchantChangeDataConstant {
    //真实性审核驳回再次提交时的银行卡preId
    public static final String BANK_PRE_ID = "bankPreId";

    public static final String MERCHANT_SN = "merchantSn";
    //控制是否需要重新报备微信子商户号
    public static final String RE_CONTRACT = "reContract";
    //申请来源,审核驳回提交还是独立申请
    public static final String APPLY_SOURCE = "applySource";

    public static final String SUCCESS = "success";
    public static final String MSG = "msg";

    //AuthAndComboTask  extra中, process部分 存储的数据的key
    public static final String FAIL_PROCESS = "fail_process";

    //AuthAndComboTask 状态: 0待处理 1子商户号待授权 2切换参数成功 5成功 6失败
    public static final int AUTH_AND_COMBO_TASK_WAIT_PROCESS = 0;
    public static final int AUTH_AND_COMBO_TASK_WAIT_AUTH = 1;
    public static final int AUTH_AND_COMBO_TASK_SUCCEED = 5;
    public static final int AUTH_AND_COMBO_TASK_FAIL = 6;


    /**
     * MerchantChangeDataService 某些接口返回的通用业务状态
     * 处理中 / 成功 / 失败
     */
    public static final int COMMON_STATUS_PROCESS = 1;
    public static final int COMMON_STATUS_SUCCESS = 2;
    public static final int COMMON_STATUS_FAIL = 3;

    public static final String ERROR_CODE = "error_code";
    public static final String CONTRACT_MEMO = "contract_memo";
    public static final String CONTRACT_CODE = "contract_code";
}
