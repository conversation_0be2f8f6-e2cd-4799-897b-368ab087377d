package com.wosai.upay.job.model.dto.response;

import lombok.Data;

import java.util.Objects;

/**
 * 营业执照变更-回填校验验证结果
 *
 * <AUTHOR>
 * @date 2025/2/19 16:18
 */
@Data
public class LicenseUpdateAmountVerifyResDTO {

    private Boolean canSubmitAmountVerify;

    private Boolean success;

    private String message;

    public static LicenseUpdateAmountVerifyResDTO verifySuccess(Integer retriedNum, Integer maxRetryNum) {
        LicenseUpdateAmountVerifyResDTO licenseUpdateAmountVerifyResDTO = new LicenseUpdateAmountVerifyResDTO();
        licenseUpdateAmountVerifyResDTO.setSuccess(true);
        if (Objects.nonNull(retriedNum) && Objects.nonNull(maxRetryNum)) {
            licenseUpdateAmountVerifyResDTO.setCanSubmitAmountVerify(retriedNum < maxRetryNum);
        } else {
            licenseUpdateAmountVerifyResDTO.setCanSubmitAmountVerify(true);
        }
        return licenseUpdateAmountVerifyResDTO;
    }


    /**
     * 校验失败
     */
    public static LicenseUpdateAmountVerifyResDTO verifyFailed(String msg, Integer retriedNum, Integer maxRetryNum) {
        LicenseUpdateAmountVerifyResDTO licenseUpdateAmountVerifyResDTO = new LicenseUpdateAmountVerifyResDTO();
        if (Objects.nonNull(retriedNum) && Objects.nonNull(maxRetryNum)) {
            licenseUpdateAmountVerifyResDTO.setCanSubmitAmountVerify(retriedNum < maxRetryNum);
        } else {
            licenseUpdateAmountVerifyResDTO.setCanSubmitAmountVerify(true);
        }
        licenseUpdateAmountVerifyResDTO.setSuccess(false);
        licenseUpdateAmountVerifyResDTO.setMessage(msg);
        return licenseUpdateAmountVerifyResDTO;
    }

    /**
     * 超过最大次数
     */
    public static LicenseUpdateAmountVerifyResDTO exceedMaximumNumber() {
        LicenseUpdateAmountVerifyResDTO licenseUpdateAmountVerifyResDTO = new LicenseUpdateAmountVerifyResDTO();
        licenseUpdateAmountVerifyResDTO.setCanSubmitAmountVerify(false);
        licenseUpdateAmountVerifyResDTO.setSuccess(false);
        licenseUpdateAmountVerifyResDTO.setMessage("超过最大验证次数");
        return licenseUpdateAmountVerifyResDTO;
    }


}
