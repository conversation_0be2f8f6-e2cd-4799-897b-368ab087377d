package com.wosai.upay.job.Constants;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/3/28
 * @Description:常量类
 */

public class ConstantsEvent {
    public static final String EVENT_TYPE_TABLE_NAME = "table_name";
    public static final String EVENT_TYPE_OPY_TYPE = "opt_type";
    public static final String EVENT_TYPE_MSG = "msg";
    public static final String EVENT_TYPE_SOURCE = "source";

    // 以下字段废弃，请使用 ContractEvent
    @Deprecated
    public static final int OPT_TYPE_MERCHANT_BASIC_INFORMATION = 0;                     //商户基本信息变更
    @Deprecated
    public static final int OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS = 1;                   //结算账户信息变更
    @Deprecated
    public static final int OPT_TYPE_MERCHANT_FEERATE = 2;               //商户费率信息更新
    @Deprecated
    public static final int OPT_TYPE_ATTACHMENT_UPLOADING = 3;               //附件上传
    @Deprecated
    public static final int OPT_TYPE_NET_IN = 4;               //入网
    @Deprecated
    public static final int OPT_TYPE_NET_CRM_UPDATE = 9; // 重新入网

    public static final String TRUE = "true";
    public static final String FALSE = "false";
    public static final String EVENT_TYPE = "event_type";
    public static final String MODULE = "module";
    public static final String UPDATE = "UPDATE";
    public static final String STATUS_CHANGE = "STATUS_CHANGE";
    public static final String MERCHANT = "MERCHANT";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String MERCHANT_ID = "merchant_id";
    public static final String BEFORE = "before";
    public static final String AFTER = "after";
    public static final String EVENT_UPDATE = "update";
    public static final String EVENT_CREATE = "create";

}
