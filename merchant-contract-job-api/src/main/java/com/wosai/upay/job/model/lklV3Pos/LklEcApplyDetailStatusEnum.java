package com.wosai.upay.job.model.lklV3Pos;

/**
 * <AUTHOR>
 */

public enum LklEcApplyDetailStatusEnum {
    MANUAL_AUDITING(1, "人工审核中"),
    AUDIT_SUCCESS(2, "人工审核通过"),
    AUDIT_FAIL(3, "人工审核失败"),
    SIGN_EXPIRE(4, "签约地址过期"),
    SIGN_SUCCESS(5, "签约成功");

    private final Integer code;
    private final String message;

    LklEcApplyDetailStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据code获取对应的枚举对象
     * @param code 状态码
     * @return 枚举对象或null（如果未找到对应的状态）
     */
    public static LklEcApplyDetailStatusEnum fromCode(Integer code) {
        for (LklEcApplyDetailStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}