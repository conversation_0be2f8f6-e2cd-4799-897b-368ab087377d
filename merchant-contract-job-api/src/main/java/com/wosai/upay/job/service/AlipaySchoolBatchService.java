package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * @Description: 支付宝高校食堂 活动批量报名
 * <AUTHOR>
 * @Date: 2021/11/16 5:05 下午
 */
@JsonRpcService("/rpc/alipaySchoolBatch")
@Validated
public interface AlipaySchoolBatchService {


     Map<String,String> alipaySchool(String merchantSn);

     Map<String, String> updateConfig(String merchantId, String storeSn, String ipRoleId);


}
