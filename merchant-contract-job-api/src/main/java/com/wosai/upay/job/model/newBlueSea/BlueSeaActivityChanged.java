package com.wosai.upay.job.model.newBlueSea;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/12/2 18:20
 */
@NoArgsConstructor
@Data
public class BlueSeaActivityChanged {

    /**
     * 申请单id
     */
    @JSONField(name = "order_id")
    private String orderId;

    /**
     申请单状态，状态机参考（FAIL:报名失败，PASS:报名成功）
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 审核备注，当status为FAIL时会有
     */
    @JSONField(name = "memo")
    private String memo;
}
