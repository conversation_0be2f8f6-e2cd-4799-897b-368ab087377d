package com.wosai.upay.job.model.dto.crm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * crm信息管理，校验返回结果
 *
 * <AUTHOR>
 * @date 2025/2/10 17:57
 */
@Data
public class CheckResultForCrmInfoManageDTO {

    public static final int SUBMIT_CHECK_RESULT_FAIL = 0;

    public static final int SUBMIT_CHECK_RESULT_SUCCESS = 1;

    @JsonProperty("submit_check_result")
    private Integer submitCheckResult;

    @JsonProperty("submit_check_code")
    private Integer submitCheckCode;

    @JsonProperty("submit_check_msg")
    private String submitCheckMsg;

    public static CheckResultForCrmInfoManageDTO fail(String submitCheckMsg) {
        CheckResultForCrmInfoManageDTO checkResultForCrmInfoManageDTO = new CheckResultForCrmInfoManageDTO();
        checkResultForCrmInfoManageDTO.setSubmitCheckResult(SUBMIT_CHECK_RESULT_FAIL);
        checkResultForCrmInfoManageDTO.setSubmitCheckCode(0);
        checkResultForCrmInfoManageDTO.setSubmitCheckMsg(submitCheckMsg);
        return checkResultForCrmInfoManageDTO;
    }

    public static CheckResultForCrmInfoManageDTO success() {
        CheckResultForCrmInfoManageDTO checkResultForCrmInfoManageDTO = new CheckResultForCrmInfoManageDTO();
        checkResultForCrmInfoManageDTO.setSubmitCheckResult(SUBMIT_CHECK_RESULT_SUCCESS);
        checkResultForCrmInfoManageDTO.setSubmitCheckCode(0);
        return checkResultForCrmInfoManageDTO;
    }

    public boolean checkPass() {
        return SUBMIT_CHECK_RESULT_SUCCESS == submitCheckResult;
    }


}
