package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: jerry
 * @date: 2019/4/23 17:02
 * @Description:扫描时长
 */
@Data
@Accessors(chain = true)
public class QueryTime {

    private Long contractEvent;
    private Long multiProviderEvent;
    private Long maxContractEvent;
    private Long contractTask;
    private Long maxContractTask;
    private Long updateBasicTask;
    private Long updateAccountTask;
    private Long updateFeeTask;
    private Long payForTask;
    private Long changeChannel;
    private Long contractTemp;
    private Long queryLkl;
    private Long queryUms;
    private Long queryPsbc;
    private Long queryCcb;
    private Long queryProviderTerminal;
    /**
     * 微信实名升级
     */
    private Long weixinUpgrade;
    private Long weixinMchPool;


    /**
     * 切换收单机构
     */
    private Long acquireChange;

    /** 以上配置均为miliseconds
     * */
    /* ****************************** **/
    /**
     * 以下配置均为单位均为minute
     **/
    private Long weixinApplySubmit;
    private Long weixinApplyQueryNear;
    private Long weixinApplyQueryLong;

    private Long weixinAuthQuery;
    private Long weixinAuthQueryNear;
    private Long weixinAuthQueryLong;

    private Long weixinFeeRateQueryNear;

    /**
     * 处理提交微信
     */
    private Long weixinDirectApplySubmit;

    /**
     * 查询微信申请单状态
     */
    private Long weixinDirectApplyQuery;
    /**
     * 创建支付宝事务
     */
    private Long aliDirectApplyCreate;
    /**
     * 提交申请
     */
    private Long aliDirectApplySubmit;
    /**
     * 确认事务
     */
    private Long aliDirectApplyConfirm;
    /**
     * 查询支付宝申请单状态 1天
     */
    private Long aliDirectApplyQuery;

    /**
     * 微信实名认证 失败延后时间
     */
    private Long weixinAuthDelay;

    /**
     * 配置微信子商户号分钟
     **/
    private Long weixinSubDevConfig;
    /**
     * 微信子商户号告警分钟
     */
    private Long decorateSubDevConfigEnd;
    /**
     * 微信子商户号告警分钟
     */
    private Integer decorateSubDevConfigRange;
    /**
     * 微信点金计划
     **/
    private Long customGold;

    /**
     * 通联商户注册银联开放平台
     */
    private Long unionOpenSubmit;

    /**
     * 通联商户注册银联开放平台 monitor
     */
    private Long unionOpenSubmitRange;

    /**
     * 通联商户查询 银联开放平台注册结果
     */
    private Long unionOpenAudit;

    /**
     * 通联商户查询 银联开放平台注册结果
     */
    private Long unionOpenAuditBefore;

    /**
     * 新蓝海
     */
    private Long newBlueSea;

    /**
     * 新蓝海报名活动时间查询
     */
    private Long newBlueSeaActivityQuery;

    /**
     * 创建蚂蚁店铺
     */
    private Long antShop;

    /**
     * 银行直连
     */
    private Long bankDirectApply;

    /**
     * 线上收款
     */
    private Long openOnlinePaymentApply;

    /**
     * lklv3 失败重试间隔
     */
    private Long lklV3Delay;


    /**
     * 创建直付通商户
     */
    private Long zftMerchant;

    /**
     * 创建阶段付商户
     */
    private Long fitness;

}
