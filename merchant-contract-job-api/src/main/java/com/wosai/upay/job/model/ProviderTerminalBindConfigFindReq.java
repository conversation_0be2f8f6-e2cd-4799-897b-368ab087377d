package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.common.utils.WosaiStringUtils;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/24
 */
@Data
public class ProviderTerminalBindConfigFindReq {

    @JsonProperty("store_sn")
    private String storeSn;

    @JsonProperty("merchant_sn")
    private String merchantSn;

    @JsonProperty("terminal_sn")
    private String terminalSn;

    @JsonProperty("provider_terminal_id")
    private String providerTerminalId;

    @JsonProperty("sub_mch_id")
    private String subMchId;

    @JsonProperty("sub_mch_ids")
    private List<String> subMchIds;

}
