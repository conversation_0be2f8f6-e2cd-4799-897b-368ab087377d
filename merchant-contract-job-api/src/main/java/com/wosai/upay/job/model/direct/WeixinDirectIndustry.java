package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/1/8
 */
@Data
@Accessors(chain = true)
public class WeixinDirectIndustry {

    /**
     * 行业id
     */
    private String industryId;
    /**
     * 是否支持微信直连
     */
    private boolean supportDirect;
    /**
     * 是否必须许可证
     */
    private boolean needLicense;
    /**
     * 需要的许可证名称
     */
    private String licenseName;
    /**
     * 个体商户结算id
     */
    private String individualSettlementId;
    /**
     * 企业执照结算id
     */
    private String enterpriseSettlementId;
    /**
     * 党政机关、事业单位结算id
     */
    private String institutionsSettlementId;
    /**
     * 其他证照类型的结算id
     */
    private String othersSettlementId;
}
