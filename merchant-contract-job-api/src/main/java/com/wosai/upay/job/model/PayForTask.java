package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PayForTask {
    private Long id;

    private Long sub_task_id;

    private String merchant_sn;

    private Integer hash_req;

    private Date create_at;

    private Date update_at;

    private Long version;

    private Integer status;

    private String submit_remit_order_id;

    private String context_param;

    private String request_param;

    private String response;

    private String request_flow_no;

    public static final int PEND_STATUS = 0;
    public static final int PROGRESS_STATUS = 1;
    public static final int SUCCESS_STATUS = 2;
    public static final int FAIL_STATUS = 3;
    //当需要用户手动验证时状态
    public static final int VERIFY_STATUS = 4;

}