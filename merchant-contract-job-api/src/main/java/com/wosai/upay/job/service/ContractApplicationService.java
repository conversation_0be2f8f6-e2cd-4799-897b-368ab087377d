package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.upay.job.model.ParamsSupplyReq;
import com.wosai.upay.job.model.RebindTerminal;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.model.application.ZhimaAppCreateReq;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19/11/27
 */
@JsonRpcService("/rpc/contract_application")
@Validated
public interface ContractApplicationService {

    String KEY_CUSTOM_FIELDS = "customFields";

    /** 报备时候自定义字段的传入 customFields map中映射key*/
    /**
     * 商户名称
     */
    String MERCHANT_NAME = "merchant_name";
    /**
     * 商户简称
     */
    String SHORT_NAME = "short_name";
    /**
     * 商户行业id 收钱吧系统行业id
     */
    String INDUSTRY = "industry";
    /**
     * 支付源行业类别
     */
    String BUSINESS = "business";

    /**
     * 微信子商户号用途 (1:一般用途,2:微信校园食堂活动,3:微信线下教培活动)
     */
    String WX_USE_TYPE = "wxUseType";


    public static final String MERCHANT_ID = "merchant_id";


    @Deprecated
    CommonResult openZhimaCreditDbb(@Valid @NotNull(message = "请求参数不能为空") ZhimaAppCreateReq req);

    @Deprecated
    CommonResult queryZhimaCreditInfo(String serviceId, String version);

    /**
     * @return
     * <AUTHOR>
     * @Description:外部应用 将商户报备于某个规则之下
     * @time 10:44 上午
     **/
    CommonResult contractByRule(@Valid @NotNull(message = "请求参数不能为空") RuleContractRequest ruleContractRequest);

    boolean isWeiXinSuZhou(@NotEmpty(message = "商户ID不能为空") String merchantId);

    /**
     * @param merchantSn
     * @return
     * @Description: 外部调用，给商户号当前正在使用的微信子商户号，开通点金计划
     */
    CommonResult openGold(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * @param mchId
     * @return
     * @Description: 外部调用，微信子商户号 开通点金计划
     */
    CommonResult openGoldByMchId(@NotEmpty(message = "微信子商户号不能为空") String mchId);

    /**
     * 重新报备一个子商户号，并且切换交易参数【【 逻辑已下线，直接返回成功 】】
     *
     * @param: reContract
     * @return:
     * @date: 10:28
     */
    @Deprecated
    CommonResult reContractForEp101(String merchantSn);


    /**
     * 完善交易报EP101处理逻辑【【 逻辑已下线，直接返回成功 】】
     *
     * @param merchantSn 商户号
     * @return
     * @Author: zhmh
     * @Description: 需求见 https://confluence.wosai-inc.com/pages/viewpage.action?pageId=295338284
     * @time: 10:50 2021/1/14
     */
    @Deprecated
    CommonResult reContractForEp101V2(String merchantSn);

    /**
     * 派工
     *
     * @param merchantSn 商户号
     * @return
     * @Author: zhmh
     * @Description: 需求见 https://confluence.wosai-inc.com/pages/viewpage.action?pageId=295338284
     * @time: 10:50 2021/1/14
     */
    CommonResult reContractForEp101V3(String merchantSn);

    /**
     * 缺少参数进行重新报备
     *
     * @return 重新报备结果
     */
    CommonResult supplyParams(ParamsSupplyReq req);

    /**
     * 开通久久折
     *
     * @param merchantSn 商户号
     * @param subAppId   久久折小程序
     * @param rateType   费率类型   0-零费率
     * @return CommonResult
     * @Author: zhmh
     * @Description:
     * @time: 15:06 2020/10/22
     */
    CommonResult openJjz(@NotEmpty(message = "商户号 merchantSn 不能为空") String merchantSn,
                         @NotEmpty(message = "久久折小程序 subAppId 不能为空") String subAppId,
                         @NotNull(message = "费率类型 不能为空 0-零费率") Integer rateType);

    /**
     * 久久折正常费率这里不再使用套餐处理,
     * 只是向微信去报备
     *
     * @param merchantSn 商户号
     * @param subAppId   久久折小程序
     * @return CommonResult
     * @Author: zhmh
     * @Description:
     * @time: 15:06 2020/10/22
     */

    CommonResult jjzNormalRate(
            @NotEmpty(message = "商户号 merchantSn 不能为空") String merchantSn,
            @NotEmpty(message = "久久折小程序 subAppId 不能为空") String subAppId
    );

    /**
     * 申请开通新蓝海
     *
     * @param merchantSn
     * @param auditId
     * @param formBody
     * @return 返回成功仅代表提交申请成功，不代表最后申请成功
     */
    CommonResult applyNewBlueSea(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                                 @NotNull(message = "auditId 不能为空") Long auditId,
                                 Map formBody);

    /**
     * 申请开通支付宝狂欢活动
     *
     * @param merchantSn
     * @param auditId
     * @param formBody
     * @return 返回成功仅代表提交申请成功，不代表最后申请成功
     */
    CommonResult applyAliCarnival(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                                  @NotNull(message = "auditId 不能为空") Long auditId,
                                  Map formBody);

    /**
     * 申请开通支付宝快消活动
     *
     * @param merchantSn 商户号
     * @param auditId    审批Id
     * @param formBody
     * @return 返回成功仅代表提交申请成功，不代表最后申请成功
     */
    CommonResult applyKx(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                         @NotNull(message = "auditId 不能为空") Long auditId,
                         Map formBody);

    /**
     * @param msgApi     店铺保存消息选择"ant.merchant.expand.shop.save.passed",新蓝海活动报名成功请选择"alipay.open.sp.blueseaactivity.changed"
     * @param msgId      任意值
     * @param bizContent 具体返回的参数,店铺保存消推送参见 https://opendocs.alipay.com/apis/014ouh ; 新蓝海报名成功通知参见https://opendocs.alipay.com/apis/01ebii
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 09:55 2020/12/4
     */
    @Deprecated
    CommonResult alipayMessage(String msgApi, String msgId, String bizContent);

    /**
     * 用于支付宝活动申请成功后 终端信息刷新的接口
     *
     * @param merchantSn
     * @param auditId
     * @param formBody
     * @return
     */
    CommonResult refreshTerminal(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                                 @NotNull(message = "auditId 不能为空") Long auditId,
                                 Map formBody
    );

    /**
     * 给商户第一家store创建支付宝shop
     *
     * @param merchantSn   商户号
     * @param businessType 业务方类型1-智慧经营,2-其他待定
     * @return
     * @Author: zhmh
     * @Description: 创建蚂蚁店铺
     * @time: 15:08 2021/2/24
     */
    @Deprecated
    CommonResult createAntShop(@NotEmpty(message = "merchantSn 不能为空") String merchantSn, @NotNull(message = "业务方类型1-扫码点单,2-其他待定") Integer businessType);

    /**
     * 给指定store创建支付宝shop
     *
     * @param merchantSn   商户号
     * @param storeSn      门店号
     * @param businessType 业务方类型1-智慧经营,2-其他待定
     * @return code 210：支付宝直连发起待授权
     * @Author: zhmh
     * @Description: 创建蚂蚁店铺
     * @time: 15:08 2021/2/24
     */
    CommonResult createAntShopForStore(@NotEmpty(message = "merchantSn 不能为空") String merchantSn, @NotEmpty(message = "storeSn 不能为空") String storeSn, @NotNull(message = "业务方类型1-扫码点单,2-其他待定") Integer businessType);

    /**
     * 使用指定多业务参数创建支付宝shop
     *
     * @param merchantSn   商户号
     * @param storeSn      门店号
     * @param businessType 业务方类型1-智慧经营,2-其他待定
     * @param tradeAppId 业务方 不传默认是1，表示间联扫码
     * @return code 210：支付宝直连发起待授权
     * @Author: zhmh
     * @Description: 创建蚂蚁店铺
     * @time: 15:08 2021/2/24
     */
    CommonResult createAntShopByTradeAppId(@NotEmpty(message = "merchantSn 不能为空") String merchantSn,
                                       @NotEmpty(message = "storeSn 不能为空") String storeSn,
                                       @NotNull(message = "业务方类型1-扫码点单,2-其他待定") Integer businessType,
                                       String tradeAppId);


    /**
     * 将拉卡拉商户的银行卡同步到通联商户
     * 【【【【临时使用，过后删除】】】】
     *
     * @param tlMchSn
     * @param lklMchSn
     * @return
     */
    CommonResult copyBankToTl(@NotEmpty(message = "tlMchSn 不能为空") String tlMchSn, @NotEmpty(message = "lklMchSn 不能为空") String lklMchSn);


    /**
     * 向支付源同步商户信息
     *
     * @param merchantSn
     * @param payWay     2-支付宝 3-微信
     */
    CommonResult syncMchInfo2PayWay(String merchantSn, int payWay);


    /**
     * 创建商户,门店,终端级别绑定任务
     *
     * @param merchantSn 商户号
     * @param allLevel   默认只绑定商户级别
     * @return
     */
    void createProviderTerminalTask(String merchantSn, Boolean allLevel);

    /**
     * 指定收单机构创建商户,门店,终端级别绑定任务
     *
     * @param merchantSn 商户号
     * @param allLevel   默认只绑定商户级别
     * @param provider   收单机构 如果为空就是所有
     * @return
     */
    void createAssignProviderTerminalTask(String merchantSn, Boolean allLevel, Integer provider);


    void syncLklStore(String merchantSn);

    void recordAgreementSync(String merchantSn);


    /**
     * 查询拉卡拉增网增任务结果
     *
     * @param deviceFingerprint 设备指纹
     * @return
     */
    CommonResult queryLklTermTaskResult(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint);

    /**
     * 创建指定拉卡拉门店
     *
     * @param deviceFingerprint 设备指纹
     */
    void createLKLStore(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint);

    /**
     * 给商户下指定门店创建对应拉卡拉网点
     *
     * @param merchantSn
     * @param storeSn    指定门店sn，不指定的话同步全部
     */
    void syncLklStore(@NotBlank(message = "merchantSn不可为空") String merchantSn, String storeSn);


    /**
     * 创建指定拉卡拉终端
     *
     * @param deviceFingerprint 设备指纹
     */
    void bindLklTerminal(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint);


    /**
     * 判断商户是否有多业务参数,true-不允许银行卡
     *
     * @param merchantSn 商户号
     * @return
     */
    public Boolean IsMultiBiz(@NotBlank(message = "商户号不能为空") String merchantSn);

    /**
     * @param merchantSn 商户号
     * @return 当前在银行通道时, bank调用该接口, 判断换卡是否需要提交job
     */
    public Boolean isInSubBizForBankAcquire(@NotBlank(message = "商户号不能为空") String merchantSn);


    /**
     * 初始化银行线下导入数据
     *
     * @param merchantSn
     * @param appStatus  状态 1-申请中,2-失败,3-成功
     * @param bankRef    银行 hxb,icbc
     * @see AppInfoModel#STATUS_PENDING,AppInfoModel#STATUS_FAIL,AppInfoModel#STATUS_SUCCESS
     */
    public void initBanKOfflineRecord(@NotBlank(message = "商户号不能为空") String merchantSn,
                                      @NotNull(message = "在app中的状态") Integer appStatus,
                                      @NotBlank(message = "银行 hxb,icbc") String bankRef);


    public void repairMerchantAppConfig(List<String> merchantSnList);


    public void repairMerchantAppConfigAssignAcquire(@NotBlank(message = "商户号不能为空") String merchantSn
            , @NotBlank(message = "收单机构") String acquire);

    /**
     * 取消其他业务套餐
     *
     * @param merchantSnList
     */
    public void cancelOtherPayComb(List<String> merchantSnList);


    /**
     * 配置银行商户子商户号
     *
     * @param merchantSn
     */
    public void configSubMchNo(String merchantSn);


    /**
     * 重新绑定终端号
     *
     * @param rebindTerminal 需求见 https://jira.wosai-inc.com/browse/CUA-7077
     */
    void reBindTerminal(@Valid RebindTerminal rebindTerminal);


    /**
     * 将merchant_config和merchant_provider_param中的交易参数洗成华夏一级服务商模式
     *
     * @param merchantSn
     */
    void configHxParam(String merchantSn);

    /**
     * 部分存量商户259信息不完整，可以通过此接口补充完整
     *
     * @param subMchId 支付宝或者微信子商户号
     */
    void fill259Info(String subMchId);


    /**
     * 删除原有重新生成终端表记录
     *
     * @param
     * @param provider
     */
    void reCreateProviderTerminal(@NotBlank(message = "商户号不为空") String merchantSn,
                                  String storeSn,
                                  String terminalSn,
                                  Integer provider);

    /**
     * app网关转发过来的参数类似
     *
     {
     "role": "super_admin",
     "client_version": "6.0.0",
     "account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "date_end": "*************",
     "id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "sub_appid": "sqb",
     "uc_user_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "cash_store_id": "a0145693-914f-486d-898c-afadebf127df",
     "merchant_id": "e2bc0928-014e-4a98-bbc0-1b8ea78a3a79",
     "msp_account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "store_id": "a0145693-914f-486d-898c-afadebf127df",
     "role_id": "35eb325c-4fad-4d44-8939-d1f0d46bf3c9",
     "merchant_user_id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "token": "********************************************************************************.76lJ8puYtN"
     }
     */

    /**
     * 收钱吧app外卡开通状态
     *
     * @param params
     * @return
     */
    public Boolean getForeignCardStatus(Map params);

    /**
     * 检查商户当前在用银行卡是否和指定收单机构银行卡一致
     *
     * @param merchantSn
     * @param acquirer
     * @return
     */
    boolean checkBankCardConsistence(String merchantSn, String acquirer);


}
