package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class PayWayConfigChange {

    /**
     * 待处理
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 1;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 2;

    /**
     * 业务失败
     */
    public static final int STATUS_BIZ_FAIL = 6;

    /**
     * 系统异常失败
     */
    public static final int STATUS_SYS_FAIL = 7;

    private Long id;

    private String merchant_sn;

    private Integer payway;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String body;

    private String channel;
}