package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
@Data
@Accessors(chain = true)
public class ZjtlcbParams {

    /**
     * 收钱吧商户号
     */
    @NotBlank(message = "merchant_sn 不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 平安银行子商户号
     */
    @NotBlank(message = "provider_mch_id 不能为空")
    @JsonProperty("provider_mch_id")
    private String providerMchId;

    /**
     * 微信子商户号
     */
    @NotBlank(message = "weixin_sub_mch_id 不能为空")
    @JsonProperty("weixin_sub_mch_id")
    private String weixinSubMchId;

    /**
     * 支付宝子商户号
     */
    @NotBlank(message = "alipay_sub_mch_id 不能为空")
    @JsonProperty("alipay_sub_mch_id")
    private String alipaySubMchId;

    /**
     * 费率
     */
    @NotBlank(message = "ali_fee_rate 不能为空")
    @JsonProperty("ali_fee_rate")
    private String aliFeeRate;

    /**
     * 费率
     */
    @NotBlank(message = "wx_fee_rate 不能为空")
    @JsonProperty("wx_fee_rate")
    private String wxFeeRate;
}
