package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.acquirer.JobContractChannel;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 外界获取
 * <AUTHOR>
 * @Date 2021/11/23 6:02 下午
 **/
@JsonRpcService("/rpc/rule")
@Validated
public interface RuleService {

    /**
     *
     * @param channel   contract_rule
     * @return
     */
    JobContractChannel getContractChannel(String channel);

}
