package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ContractRuleCustomDto
 *
 * <AUTHOR>
 * @date 2019-08-08 12:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContractRuleCustomDto extends ContractRuleDto {

    private static final long serialVersionUID = -5037794202038308795L;

    private String depend_on;

    private String depend_on_name;

    private String payway_channel_no;

    private String channel_name;

    /**
     * 收单结构渠道号
     */
    private String rece_org_no;

    /**
     * 结算通道技术服务方号（受理机构编号）
     */
    private String tech_org_no;
}
