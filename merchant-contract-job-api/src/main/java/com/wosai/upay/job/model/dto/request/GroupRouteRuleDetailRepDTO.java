package com.wosai.upay.job.model.dto.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 收单组路由规则详情表表Request DTO对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("收单组路由规则详情")
public class GroupRouteRuleDetailRepDTO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("对应进件规则决策表id")
    private Long ruleDecisionId;

    @ApiModelProperty("商户名称: name\n" +
            "商户类型(1-小微 2-个体户 3-企业商户 4-组织商户): type\n" +
            "区唯一编码: districtCode\n" +
            "省唯一编码: provinceCode\n" +
            "市唯一编码: cityCode\n" +
            "商户行业: industry\n" +
            "所属推广组织path(从一级开始): promotionOrganizationPath\n" +
            "银行账户类型(1-对私 2-对公): bankAccountType\n" +
            "法人类型(0-非法人 1-法人): legalPersonType\n" +
            "结算账户类型(1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他): settlementAccountType\n" +
            "个人证件类型(1-身份证 2-港澳居民来往内地通行证 3-台湾居民来往大陆通行证 4-非中华人民共和国护照 5-中国护照 6-港澳居民居住证 7-台湾居民居住证): personalCertificateType")
    private String objectPropertyType;

    @ApiModelProperty("逻辑操作类型 EQUAL-等于,NOT_EQUAL-不等于,CONTAIN-包含(给定对象属性值包含用户特征属性值),NOT_CONTAIN-不包含(给定对象属性值不包含用户特征属性值),\n" +
            "BE_CONTAINED-被包含(用户特征属性值包含给定对象属性值),NOT_BE_CONTAINED-被不包含(用户特征属性值不包含给定对象属性值）\n" +
            "IN, NOT_IN,START_WITH,NOT_START_WITH(主要用于所属推广组织path匹配)")
    private String logicalOperationType;

    @ApiModelProperty("对象属性值 json字符串数组 例如: [\"1\",\"2\",\"3\"]")
    private List<String> objectPropertyValue;

    @ApiModelProperty("有效状态 0-失效 1-生效")
    @NotNull(message = "有效状态不能为空")
    private Integer validStatus;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("创建人")
    private String createBy;
}

