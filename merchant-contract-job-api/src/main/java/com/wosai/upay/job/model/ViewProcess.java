package com.wosai.upay.job.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 用于crm_app银行业务开通过程视展示
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ViewProcess {

    /**
     * 是否已经完成
     */
    private Boolean finish = Boolean.FALSE;

    /**
     * 描述状态
     */
    private Integer viewStatus;

    /**
     * 描述信息
     */
    private String statusDesc;

    /**
     * 说明备注
     */
    private String remark;

    /**
     * 处理时间
     */
    private String time;

    /**
     * 额外的信息,比如链接需要前端做成click事件
     */
    @JSONField(serialize = false)
    private Boolean extra;

    /**
     * 额外的信息,比如链接需要前端做成click事件
     */
    private String extraMessage;

    /**
     * 微信是否成功
     */
    private Boolean wxStatus;

    /**
     * 支付宝是否成功
     */
    private Boolean aliStatus;

    /**
     * 额外的信息,比如链接需要前端做成click事件
     */
    private String aliMessage;

    /**
     * 支付宝子商户号
     */
    private String aliMch;


    /**
     * 微信子商户号
     */
    private String wxMch;

    /**
     * 银行审核最新处理状态描述
     */
    private String latestBankReviewStatusDesc;
}