package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: jerry
 * @date: 2019/4/23 17:02
 * @Description:扫描限制
 */
@Data
@Accessors(chain = true)
public class QueryLimit {

    private Integer contractEvent;
    private Integer multiProviderEvent;
    private Integer contractTask;
    private Integer updateBasic;
    private Integer updateFee;
    private Integer updateAccount;
    private Integer queryLkl;
    private Integer queryPsbc;
    private Integer queryCcb;
    private Integer payForTask;
    private Integer changeChannel;
    private Integer contractTemp;
    private Integer warnCount;
    private Integer dingCount;
    private Integer queryUms;
    private Integer weixinApplySubmit;
    private Integer weixinApplyQueryNear;
    private Integer weixinApplyQueryLong;
    private Integer providerTerminal;

    private Integer weixinApplyQuery;
    private Integer weixinAuthQuery;
    private Integer weixinAuthQueryNear;
    private Integer weixinAuthQueryLong;

    private Integer weixinFeeRateQueryNear;

    private Integer weixinMchPool;

    /**
     * 微信实名升级
     */
    private Integer weixinUpgrade;

    /**
     * 切换收单机构
     */
    private Integer acquireChange;

    /**
     * 配置微信子商户号
     **/
    private Integer weixinSubDevConfig;
    /**
     * 微信点金计划
     **/
    private Integer customGold;

    /**
     * 通联商户注册银联开放平台
     */
    private Integer unionOpenSubmit;

    /**
     * 通联商户查询 银联开放平台注册结果
     */
    private Integer unionOpenAudit;

    /**
     * 新蓝海
     */
    private Integer newBlueSea;

    /**
     * 微信直连申请提交
     */
    private Integer weixinDirectApplySubmit;

    /**
     * 微信直连申请查询
     */
    private Integer weixinDirectApplyQuery;

    /**
     * 创建支付宝事务
     */
    private Integer aliDirectApplyCreate;
    /**
     * 提交申请
     */
    private Integer aliDirectApplySubmit;
    /**
     * 确认事务
     */
    private Integer aliDirectApplyConfirm;
    /**
     * 查询支付宝申请单状态 1天
     */
    private Integer aliDirectApplyQuery;

    /**
     * 创建蚂蚁店铺
     */
    private Integer antShop;

    /**
     * 银行直连
     */
    private Integer bankDirectApply;
    /**
     * 线上收款
     */
    private Integer openOnlinePaymentApply;

    /**
     * 商户信息变更,查询微信认证状态
     */
    private Integer merchantChangeDataWxStatus;

    /**
     * 创建直付通商户
     */
    private Integer zftMerchant;

    /**
     * 创建阶段付商户
     */
    private Integer fitness;

}
