package com.wosai.upay.job.model.dto.acquirePos;

import java.util.Date;
import java.util.Map;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 签约申请记录表实体对象
 *
 * <AUTHOR>
 */
@Data
public class LklEcApplyDTO {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    /**
     * 整个任务状态
     * 10-进行中,20-成功,30-失败
     */
    private Integer status;

    /**
     * 1-人工审核中,2-人工审核通过,3-人工审核失败,4-签约地址过期,5-签约成功,6-签约失败
     */
    private Integer detailStatus;

    /**
     * 结果
     */
    private String result;
    /**
     * 电子合同申请编号
     */
    @NotBlank(message = "ecApplyId不能为空")
    private String ecApplyId;

    /**
     * 电子合同链接
     */
    private String contractUrl;
    /**
     * 应用标识
     */
    @NotBlank(message = "应用标识不能为空")
    private String devCode;


    private Map feeMap;


    private Long tradeComboId;


}

