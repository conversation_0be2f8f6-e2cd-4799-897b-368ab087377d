package com.wosai.upay.job.model.directparams;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 支付宝2.0直连交易参数
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Data
public class AlipayV2DirectParams extends BaseParams {

    /**
     * WAP 支付参数
     */
    private AlipayV2Params alipay_wap_v2_trade_params;

    /**
     * BSC CSB 参数
     */
    private AlipayV2Params alipay_v2_trade_params;

    /**
     * App 直连交易参数
     */
    private AlipayH5Params alipay_app_v2_trade_params;

    /**
     * h5 直连交易参数
     */
    private AlipayH5Params alipay_h5_v2_trade_params;



    @Data
    public static class AlipayV2Params extends BaseTradeParams {

        private String auth_app_id;

        private String app_auth_token;

        private String category;

        private String mch_id;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(auth_app_id) &&
                    StringUtils.isEmpty(app_auth_token) &&
                    StringUtils.isEmpty(category) &&
                    StringUtils.isEmpty(mch_id);
        }
    }

    @Data
    public static class AlipayH5Params extends BaseTradeParams {

        private String app_id;

        private String private_key;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(app_id) &&
                    StringUtils.isEmpty(private_key);
        }
    }
}
