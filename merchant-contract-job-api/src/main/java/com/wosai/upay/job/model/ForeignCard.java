package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ForeignCard {


    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 0;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 10;

    /**
     * 业务失败
     */
    public static final int STATUS_FAIL = 20;

    private Long id;

    private String merchant_sn;

    private Integer status;

    private String ecApplyId;

    private Date create_at;

    private Date update_at;

    private String result;

    private String form_body;

    private String contractUrl;

    private String extra;

    private String dev_code;

    public Map getFormBody() {
        return JSONObject.parseObject(form_body,Map.class);
    }

}