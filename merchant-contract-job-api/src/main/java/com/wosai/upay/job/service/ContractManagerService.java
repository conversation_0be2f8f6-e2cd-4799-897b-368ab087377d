package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.dto.*;
import com.wosai.upay.job.validation.group.*;
import com.wosai.web.api.ListResult;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 进件通道管理
 *
 * <AUTHOR>
 * @date 2019-07-10 14:32
 */
@Validated
@JsonRpcService("/rpc/contractManager")
public interface ContractManagerService {

    /**
     * 查找报备规则组列表
     *
     * @param pageInfo     分页查询
     * @param ruleGroupDto 规则组入参（查询条件）
     * @return 规则组列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15637">api 文档</a>
     */
    ListResult<RuleGroupDto> findRuleGroupList(PageInfo pageInfo, RuleGroupDto ruleGroupDto);

    /**
     * 添加规则组
     *
     * @param ruleGroupDto 规则组参数
     * @return 规则组
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15646">api 文档</a>
     */
    @Validated(AddRuleGroup.class)
    RuleGroupDto addRuleGroup(@NotNull(message = "规则组参数不能为空") @Valid RuleGroupDto ruleGroupDto);

    /**
     * 更新报备规则组
     *
     * @param ruleGroupDto 规则组参数
     * @return 规则组
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15655">api 文档</a>
     */
    @Validated(UpdateRuleGroup.class)
    RuleGroupDto modifyRuleGroup(@NotNull(message = "规则组参数不能为空") @Valid RuleGroupDto ruleGroupDto);

    /**
     * 启用/禁用规则组
     *
     * @param groupId 规则组唯一标识
     * @param enable  启用/禁用标识
     * @return 规则组
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/18463">api 文档</a>
     */
    RuleGroupDto enableRuleGroup(@NotBlank(message = "规则组唯一标识不能为空") String groupId,
                                 @NotNull(message = "enable 不能为空") @Min(message = "请填写enable的合理值", value = 0) @Max(message = "请填写enable的合理值", value = 1) Integer enable);

    /**
     * 查找规则组详情
     *
     * @param groupId 规则组唯一标识
     * @return 规则组详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15664">api 文档</a>
     */
    RuleGroupDto findRuleGroupDetailByGroupId(@NotBlank(message = "规则组id不能为空") String groupId);

    /**
     * 查找报备规则列表
     *
     * @param pageInfo        分页查询信息
     * @param contractRuleDto 规则入参
     * @return 报备规则列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15673">api 文档</a>
     */
    ListResult<ContractRuleDto> findContractRuleList(PageInfo pageInfo, ContractRuleDto contractRuleDto);

    /**
     * 查找规则详情
     *
     * @param rule 规则唯一标识
     * @return 规则详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15682">api 文档</a>
     */
    ContractRuleCustomDto findContractRuleDetailByRule(@NotBlank(message = "规则唯一标识 rule 不能为空") String rule);

    /**
     * 新增报备规则
     *
     * @param contractRuleDto 报备规则信息
     * @return 报备规则
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15691">api 文档</a>
     */
    @Validated(AddContractRule.class)
    ContractRuleDto addContractRule(@NotNull(message = "报备规则入参不能为空") @Valid ContractRuleDto contractRuleDto);

    /**
     * 更新报备规则
     *
     * @param contractRuleDto 规则信息参数
     * @return 报备规则
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15754">api 文档</a>
     */
    @Validated(UpdateContractRule.class)
    ContractRuleDto modifyContractRule(@NotNull(message = "报备规则不能为空") @Valid ContractRuleDto contractRuleDto);

    /**
     * 启用/禁用报备规则
     *
     * @param rule   规则唯一标识
     * @param enable 0 表示禁用, 1 表示启用
     * @return 报备规则
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17833">api 文档</a>
     */
    ContractRuleDto enableContractRule(@NotBlank(message = "规则唯一标识rule不能为空") String rule,
                                       @NotNull(message = "enable 不能为空") @Min(message = "请填写enable的合理值", value = 0) @Max(message = "请填写enable的合理值", value = 1) Integer enable);

    /**
     * 获取报备渠道列表
     *
     * @param pageInfo         分页信息
     * @param channelCustomDto 报备渠道条件
     * @return 报备渠道列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17743">api 文档</a>
     */
    ListResult<ChannelCustomDto> findChannelList(PageInfo pageInfo, ChannelCustomDto channelCustomDto);

    /**
     * 获取报备渠道详情
     *
     * @param channel 报备渠道唯一标识
     * @return 报备渠道详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17860">api 文档</a>
     */
    ChannelCustomDto findChannelDetailByChannel(@NotBlank(message = "报备渠道唯一标识 channel 不能为空") String channel);

    /**
     * 更新报备渠道
     *
     * @param channelDto 报备渠道参数
     * @return 报备渠道
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17869">api 文档</a>
     */
    @Validated({UpdateChannel.class})
    ChannelDto updateChannel(@NotNull(message = "报备渠道参数不能为空") @Valid ChannelDto channelDto);

    /**
     * 查找收单机构列表
     *
     * @param pageInfo    分页信息
     * @param acquirerDto 收单机构查询条件
     * @return 收单机构列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15763">api 文档</a>
     */
    ListResult<AcquirerDto> findAcquirerList(PageInfo pageInfo, AcquirerDto acquirerDto);

    /**
     * 查找结算通道列表
     *
     * @param pageInfo    分页查询参数
     * @param providerDto 结算通道查询条件
     * @return 结算通道列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/15772">api 文档</a>
     */
    ListResult<ProviderDto> findProviderList(PageInfo pageInfo, ProviderDto providerDto);

    //----------

    /**
     * 刷新 RuleContext
     *
     * @return
     */
    boolean refreshRuleContext();
}