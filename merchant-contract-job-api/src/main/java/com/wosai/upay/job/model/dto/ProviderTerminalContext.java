package com.wosai.upay.job.model.dto;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/3/3 3:47 下午
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ProviderTerminalContext {


    //新增子商户号 绑定终端所需的字段
    /**
     * 子商户户号
     */
    private String subMerchant;
    /**
     * 收单机构Provider
     */
    Integer provider;
    /**
     *
     */
    Integer payWay;



    //新增终端绑定子商户号 所需字段
    /**
     * 收单机构 终端ID
     */
    private String providerTerminalId;


    /**
     * 收钱吧对应门店
     */
    private String sqbStoreSn;

    /**
     * 收钱吧终端号
     */
    private String terminalSn;

    /**
     * 品牌商户号，品牌商户号与subMerchant是同一商户，只有品牌终端绑定才有这个字段
     * 在绑定时需要通过这个字段和 subMerchant找到对应的交易参数
     */
    private String brandMerchantSn;

}
