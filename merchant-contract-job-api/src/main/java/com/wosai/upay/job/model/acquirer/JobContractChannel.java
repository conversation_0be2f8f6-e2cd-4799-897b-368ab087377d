package com.wosai.upay.job.model.acquirer;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 用于对外的 contract
 * <AUTHOR>
 * @Date 2021/12/2 10:31 上午
 **/

@Data
public class JobContractChannel {
    private String channel;

    private String name;

    private String subject;

    private Integer payway;

    private String payway_channel_no;

    private String channel_no;

    private String private_key;

    private String provider;

    /**
     * 结算通道参数
     */
    private Map<String, Object> provider_metadata;

    private String acquirer;

    /**
     * 收单机构参数
     */
    private Map<String, Object> acquirer_metadata;

    /**
     * provider_metadata + acquirer_metadata
     */
    private Map<String, Object> channelParam = new HashMap<>();


}