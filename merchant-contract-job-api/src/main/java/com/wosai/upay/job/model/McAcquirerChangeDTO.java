package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@Data
@Accessors(chain = true)
public class McAcquirerChangeDTO {

    private Integer id;

    private String apply_id;

    private String merchant_sn;

    private String merchant_id;

    private String source_acquirer;

    private String target_acquirer;

    private Integer status;

    private String memo;

    private Date create_at;

    private Date update_at;

    private Boolean immediately;

    private String process;

    private String extra;
}
