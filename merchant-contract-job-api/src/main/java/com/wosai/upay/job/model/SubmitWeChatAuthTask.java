package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;

/**
 * @Description: 提交微信升级认证实体
 * <AUTHOR>
 * @Date 2020/9/8 5:04 下午
 **/
@Data
@Accessors(chain = true)
public class SubmitWeChatAuthTask {

    @JsonProperty("merchant_sn")
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;

    @JsonProperty("source")
    @NotEmpty(message = "业务来源不能为空")
    private String source;

    /**
     * 是否强制按照小微提交
     **/
    @JsonProperty("force_micro")
    private boolean forceMicro = false;

    @JsonProperty("operator")
    @NotEmpty(message = "操作人不能为空")
    private String operator;

    /**
     * 是否强制只走授权流程
     * 微信升级认证：实名 授权两个流程。有时候接口提交申请单实名 微信那边有bug导致 申请单流程走不完
     * 这里是 商户在微信小程序里面走强制提交之后 （实名流程走小程序）
     * 只走我们系统的授权流程
     */
    @JsonProperty("force_auth")
    private boolean forceAuth = false;

}
