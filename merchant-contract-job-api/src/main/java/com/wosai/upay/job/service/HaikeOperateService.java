package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 支付宝的通用service
 * <AUTHOR>
 * @Date 2023/03/11
 **/
@JsonRpcService("/rpc/haike")
@Validated
public interface HaikeOperateService {

    /**
     * 根据子商户号同步海科的终端和商户信息
     * @param payMerchantId
     * @return
     */
    ContractResponse syncTerminalAndMerchantByPayMerchantId(String payMerchantId);

}
