package com.wosai.upay.job.model.payLater;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 开头先享后付传参
 * <AUTHOR>
 * @Date 2023/7/31 下午5:28
 */
@Data
public class PayLaterInfoDTO {


    /**
     * 支付宝账号
     */
    @NotBlank(message = "支付宝号必填")
    private String account;

    /**
     * 商户号
     */
    @NotBlank(message = "商户号必填")
    private String merchantSn;

    /**
     * 门店码使用场景
     */
    @NotNull(message = "门店码使用场景必填")
    private Integer scene;

    /**
     * 跳转链接
     */
    @NotBlank(message = "跳转链接必传")
    private String jumpUrl;

    /**
     * 专属门店码
     */
    public static  Integer UNIQUE = 1;

    /**
     * 所有门店码
     */
    public static  Integer UNIVERSAL = 2;


}
