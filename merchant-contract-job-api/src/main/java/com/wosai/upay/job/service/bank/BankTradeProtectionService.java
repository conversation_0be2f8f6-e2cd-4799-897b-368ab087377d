package com.wosai.upay.job.service.bank;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.dto.request.AppCommonReqDTO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionAuditApplyReqDTO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionConfigReqDTO;
import com.wosai.upay.job.model.dto.response.BankTradeProtectionConfigRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 银行保障交易服务
 */
@JsonRpcService("/rpc/bank/trade/protection")
@Validated
public interface BankTradeProtectionService {

    /**
     * 获取商户银行保障交易配置
     *
     * @param appCommonReqDTO app通用请求DTO
     * @return 银行保障交易配置响应DTO
     */
    BankTradeProtectionConfigRspDTO getTradeProtectionStatus(@Valid AppCommonReqDTO appCommonReqDTO);

    /**
     * 更新商户银行保障交易状态
     *
     * @param bankTradeProtectionConfigReqDTO 银行保障交易配置请求DTO
     * @return 是否更新成功
     */
    Boolean updateTradeProtectionStatus(@Valid BankTradeProtectionConfigReqDTO bankTradeProtectionConfigReqDTO);


    /**
     * 处理银行保障交易审核申请
     * 触发给商户发通知，让商户选择是否需要银行保障交易
     *
     * @param auditApplyReqDTO 银行保障交易审核申请
     */
    void processBankTradeProtectionAuditApplyApply(BankTradeProtectionAuditApplyReqDTO auditApplyReqDTO);
}
