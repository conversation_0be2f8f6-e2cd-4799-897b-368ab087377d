package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.acquirer.ByPassTradeConfig;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * Created by lihebin on 2018/9/6.
 */
@JsonRpcService("/rpc/providerParams")
@Validated
public interface ProviderTradeParamsService {


    /**
     * 分页查询商户银行交易参数
     * @param params
     * @return
     */
    @Deprecated
    List<Map> listMerchantProviderParams(Map params);


    /**************************************************************************************************/

    /**
     * 获取商户交易参数
     * @param merchantSn
     * @param payway
     * @return
     */
    Map getPayTradeParams(String merchantSn, int payway);

    /**
     * 获取备用通道的交易参数配置
     * @param merchantId 商户id
     * @param payways   payway集合
     * @return 配置集合
     */
    List<ByPassTradeConfig> getByPassTradeConfig(String merchantId, List<Integer> payways);



}
