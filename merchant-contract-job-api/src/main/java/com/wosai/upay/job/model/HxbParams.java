package com.wosai.upay.job.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
@Data
public class HxbParams {

    /**
     * 收钱吧商户号
     */
    @NotBlank(message = "merchant_sn 不能为空")
    private String merchant_sn;

    /**
     * 华夏银行子商户号
     */
    @NotBlank(message = "provider_mch_id 不能为空")
    private String provider_mch_id;

    /**
     * 商户终端号
     */
    @NotBlank(message = "provider_term_id 不能为空")
    private String provider_term_id;

    /**
     * 微信子商户号
     */
    @NotBlank(message = "weixin_sub_mch_id 不能为空")
    private String weixin_sub_mch_id;

    /**
     * 支付宝子商户号
     */
    @NotBlank(message = "alipay_sub_mch_id 不能为空")
    private String alipay_sub_mch_id;

    /**
     * 不知道什么东西，交易要用,
     * 在这个需求中这个字段不做校验,因为会自动填充一个固定值https://confluence.wosai-inc.com/pages/viewpage.action?pageId=792199513
     */
    private String develop_app_id;


}
