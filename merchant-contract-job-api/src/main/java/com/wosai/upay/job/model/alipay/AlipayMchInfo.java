package com.wosai.upay.job.model.alipay;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 支付宝子商户信息
 * <AUTHOR>
 * @date 2022-08-02
 */
@Data
@Accessors(chain = true)
public class AlipayMchInfo {


    /**
     * 商户服务电话
     */
    private String service_phone;

    /**
     * 子商户号
     */
    private String sub_merchant_id;

    /**
     * 商户编号
     */
    private String external_id;

    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户简称
     */
    private String alias_name;

    /**
     * 商户经营类目
     */
    private String category_id;

    /**
     * 与银行或机构合作的伙伴标识
     */
    private String source;

    /**
     * 间连商户等级
     */
    private String indirect_level;

    /**
     * 商户证件编号(企业或者个体工商
     * 户提供营业执照，事业单位提供事
     * 证号)
     */
    private String business_license;

    /**
     * 商户证件类型，与商户证件编号 (business_license)同时出现，
     * 取值范围:
     * NATIONAL_LEGAL:营业执照; NATIONAL_LEGAL_MERGE: 营业执照(多证合一); INST_RGST_CTF:事业单位法人证书;
     */
    private String business_license_type;

    /**
     * 商户联系人信息
     */
    private List<Contact_info> contact_info;

    /**
     * 商户地址信息
     */
    private List<Address_info> address_info;


    /**
     * 商户对应银行所开立的结算卡信息
     */
    private List<Bankcard_info> bankcard_info;


    /**
     * 受理商户的固定二维码链接地址，商户所属的银行或 ISV 给商户的二维码 url 值
     */
    private List<String> pay_code_info;

    /**
     * 受理商户的支付宝账号(用于关联
     * 商户生活号、原服务窗，打通口碑
     * 营销活动)
     */
    private List<String> logon_id;

    /**
     * 商户备注信息
     */
    private String memo;

    /**
     * 收单机构(例如银行)的标识
     */
    private String org_pid;

    /**
     * 标准商户类别码
     */
    private String mcc;

    /**
     * 标准商户类别码
     */
    private List<String> service_codes;

    /**
     * service_codes包含PC和APP时必填
     */
    private List<Site_info>  site_info;

    /**
     * 商户状态，取值范围: 00:启用;(缺省值) 01:注销;
     * 注销状态的商户，将无法完成支付 类交易。
     */
    private String merchant_state;

    /**
     * 银联商户编号
     */
    private String up_merchant_id;

    /**
     * 是否强制小微
     */
    private Boolean force_micro;


    @Data
    @Accessors(chain = true)
    public static class Contact_info {

        /**
         * 联系人名字
         */
        private String name;

        /**
         * 电话
         */
        private String phone;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 电子邮箱
         */
        private String email;

        /**
         * tag
         */
        private List<String> tag;

        /**
         * 联系人类型，取值范围: LEGAL_PERSON : 法 人 ; CONTROLLER : 实 际 控 制 人 ; AGENT:代理人;OTHER:其他
         */
        private String type;

        /**
         * 身份证号
         */
        private List<String> id_card_no;
    }


    @Data
    @Accessors(chain = true)
    public static class Address_info {

        /**
         * 城市编码，城市编码是与国家统计局一致
         */
        private String city_code;


        /**
         * 区县编码，区县编码是与国家统计局一致
         */
        private String district_code;

        /**
         * 地址。商户详细经营地址或人员所在地点
         */
        private String address;

        /**
         * 省份编码，省份编码是与国家统计局一致
         */
        private String province_code;

        /**
         * 经度，浮点数
         */
        private String longitude;

        /**
         * 维度，浮点数
         */
        private String latitude;

        /**
         * 地址类型
         */
        private String type;

    }


    @Data
    @Accessors(chain = true)
    public static class Bankcard_info {

        /**
         * 银行卡号
         */
        private String card_no;

        /**
         * 银行卡持卡人姓名
         */
        private String card_name;

        /**
         * 银行开户行名称。填写支行名称
         */
        private String bank_branch_name;

    }

    @Data
    @Accessors(chain = true)
    public static class Site_info {

        /**
         * 网站:01 APP :02 服务窗:03 公众号:04
         * 其他:05 支付宝小程序:06
         */
        private String site_type;

        /**
         * 站点地址
         */
        private String site_url;

        /**
         * 站点名称
         */
        private String site_name;

        /**
         * 测试账号
         */
        private String account;

        /**
         * 测试密码
         */
        private String password;

    }

}
