package com.wosai.upay.job.model.dto.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 收单组路由规则决策表表Response DTO对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("收单组路由规则决策表表Response DTO对象")
public class GroupRouteRulesDecisionRspDTO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 父规则决策id 0代表最上层规则决策
     */
    @ApiModelProperty("父规则决策id 0代表最上层规则决策")
    private Long parentId;
    /**
     * 进件规则决策名称
     */
    @ApiModelProperty("进件规则决策名称")
    private String name;
    /**
     * 进件规则决策分类
     */
    @ApiModelProperty("进件规则决策分类")
    private String classification;
    /**
     * 优先级 嵌套的子规则优先级为null
     */
    @ApiModelProperty("优先级 嵌套的子规则优先级为null")
    private Integer priority;
    /**
     * 子规则连接类型(嵌套子规则) AND 或者是 OR
     */
    @ApiModelProperty("子规则连接类型(嵌套子规则) AND 或者是 OR")
    private String ruleConnectionType;
    /**
     * 规则详情连接类型 AND 或者是 OR
     */
    @ApiModelProperty("规则详情连接类型 AND 或者是 OR")
    private String ruleDetailConnectionType;
    /**
     * 选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择,描述满足规则时，是选择,不可选择还是只可选择对应的group_id
     */
    @ApiModelProperty("选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择,描述满足规则时，是选择,不可选择还是只可选择对应的group_id")
    private String chooseType;
    /**
     * 收单组策略id，对应group_combined_strategy.id
     */
    @ApiModelProperty("收单组策略id")
    private Long groupStrategyId;
    /**
     * 报备通道规则组策略
     */
    @ApiModelProperty("报备通道规则组策略")
    private GroupCombinedStrategyRspDTO groupCombinedStrategyRspDTO;
    /**
     * 报备通道规则组策略明细
     */
    @ApiModelProperty("报备通道规则组策略明细")
    private List<GroupCombinedStrategyDetailRspDTO> groupCombinedStrategyDetailRspDTOList;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 有效状态 0-失效 1-生效
     */
    @ApiModelProperty("有效状态 0-失效 1-生效")
    private Integer validStatus;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;
    /**
     * 子规则决策节点
     */
    @ApiModelProperty("子规则决策节点")
    private List<GroupRouteRulesDecisionRspDTO> childrenRulesDecisionNodeList;
    /**
     * 规则决策节点对应的规则detail
     */
    @ApiModelProperty("规则决策节点对应的规则detail")
    private List<GroupRouteRuleDetailRspDTO> groupRouteRuleDetailRspDTOList;
}

