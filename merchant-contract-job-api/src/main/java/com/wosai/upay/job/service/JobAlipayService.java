package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.alipay.AlipayMchResponse;
import com.wosai.upay.job.model.alipay.UpdateAlipayMchInfoRequest;
import com.wosai.upay.job.model.alipay.UpdateAlipayMchInfoResponse;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 支付宝的通用service
 * <AUTHOR>
 * @Date 2022/08/02
 **/
@JsonRpcService("/rpc/alipay")
@Validated
public interface JobAlipayService {



    /**
     * 根据支付宝子商户号查询支付宝商户名
     *
     * @param subMchId
     * @return
     */
    AlipayMchResponse getAlipayMchInfoBySubMchId(@NotEmpty(message = "支付宝子商户号不能为空") String subMchId);


    /**
     * 根据支付宝子商户号查询支付宝所有信息
     *
     * @param subMchId
     * @return
     */
    AlipayMchResponse getAlipayMerchantNameBySubMchId(@NotEmpty(message = "支付宝子商户号不能为空") String subMchId);


    /**
     * 根据支付宝子商户号修改支付宝信息
     *
     * @param request
     * @return
     */
    UpdateAlipayMchInfoResponse updateAlipayMchInfoBySubMchId(UpdateAlipayMchInfoRequest request);


}
