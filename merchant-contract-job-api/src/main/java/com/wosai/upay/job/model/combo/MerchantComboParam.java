package com.wosai.upay.job.model.combo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/6/8 2:24 下午
 **/
@Data
@Accessors(chain = true)
public class MerchantComboParam {
    private Long id;

    private String provider_params_id;

    private String merchant_sn;

    private Long trade_app_id;

    private Long combo_id;

    private Long combo_detail_id;

    private Integer provider;

    private Integer formal_status;

    private Integer payway;

    private String pay_merchant_id;

    private Integer b2c_in_use;

    private String b2c_fee_rate;

    private Integer c2b_in_use;

    private String c2b_fee_rate;

    private Integer wap_in_use;

    private String wap_fee_rate;

    private Integer mini_in_use;

    private String mini_fee_rate;

    private Integer app_in_use;

    private String app_fee_rate;

    private Integer h5_in_use;

    private String h5_fee_rate;

    private Long ctime;

    private Long mtime;
    private Integer status;


}
