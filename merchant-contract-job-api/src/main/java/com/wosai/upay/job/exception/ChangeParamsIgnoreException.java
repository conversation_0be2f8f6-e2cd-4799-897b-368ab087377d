package com.wosai.upay.job.exception;

/**
 * 这个异常用来标记一些切换参数时的异常
 * 这些异常在切换收单机构时可忽略
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
public class ChangeParamsIgnoreException extends MerchantContractException {
    public ChangeParamsIgnoreException(String message, Throwable cause) {
        super(message, cause);
    }

    public ChangeParamsIgnoreException(String message) {
        super(message);
    }

    public ChangeParamsIgnoreException(Throwable cause) {
        super(cause);
    }

    @Override
    public int getCode() {
        return 450;
    }
}
