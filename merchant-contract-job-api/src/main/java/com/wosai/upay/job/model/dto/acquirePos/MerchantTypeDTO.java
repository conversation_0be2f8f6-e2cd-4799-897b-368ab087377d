package com.wosai.upay.job.model.dto.acquirePos;

import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import lombok.Data;


/**
 * 商户营业执照类型和账户类型
 * <AUTHOR>
 */
@Data
public class MerchantTypeDTO {


    /**
     * 营业执照类型
     */
    private BusinessLicenseTypeEnum businessLicenseType;

    /**
     * 账户类型
     */
    private BankAccountTypeEnum accountType;

}