package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ContractTask {
    private Long id;

    private String merchant_sn;
    // CUA-10635 数据库字段长度只有40，在存储的时候会进行截取。该字段目前只有前端展示的作用，其他部分不要使用该字段
    private String merchant_name;

    private String type;

    private Integer status;

    private Integer affect_sub_task_count;

    private Integer affect_status_success_task_count;

    private Date create_at;

    private Date update_at;

    private Date complete_at;

    private Date priority;

    private Long version;

    private String event_msg;

    private String event_context;

    private String result;

    private String rule_group_id;

    //sp进件管理专用   true=>微信实名认证状态为  待联系人授权时
    private Boolean need_auth = false;
    /**
     * sp进件管理专用 该微信实名认证
     */
    private String auth_type;

    public Map getEventContext(){
        return JSON.parseObject(event_context, Map.class);
    }

}