
package com.wosai.upay.job.model.psbc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
@Data
public class SelfAuditRejectRequest {
    @NotBlank(message = "商户ID不可为空")
    private String merchantId;

    @NotBlank(message = "description不可为空")
    private String description;

    @NotBlank(message = "devCode不可为空")
    public String devCode;

    private String auditor;

    private String auditPlatform;
}
