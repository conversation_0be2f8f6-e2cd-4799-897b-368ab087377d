package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.FailEventConf;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/19 11:17
 * @Description:
 */
@JsonRpcService("/rpc/contractstatus")
@Validated
public interface ContractStatusService {
    /**
     * crm页面跳转不好判断  报备提供某个标志出来
     **/
    String MICRO_RESUB = "micro_resub";

    List<ContractStatus> selectByUpdateTime(@NotNull(message = "mtime不能为null") long mtime);

    ContractStatus selectByPrimaryKey(@NotNull(message = "id不能为null") long id);

    Map getMerchantContractStatusByType(@PropNotEmpty.List({
            @PropNotEmpty(value = "type", message = "业务类型不能为空"),
            @PropNotEmpty(value = "merchant_sn", message = "商户号不能为空"),
    }) Map params);

    Map getMerchantBankContractStatusByType(@PropNotEmpty.List({
            @PropNotEmpty(value = "type", message = "业务类型不能为空"),
            @PropNotEmpty(value = "merchant_sn", message = "商户号不能为空"),
    }) Map params);

    /**
     * 获取商户入网的文案
     * @param params
     * @return
     */
    Map getMerchantNetInStatusByMultiEvent(@PropNotEmpty.List({
            @PropNotEmpty(value = "type", message = "业务类型不能为空"),
            @PropNotEmpty(value = "merchant_sn", message = "商户号不能为空"),
    }) Map params);

    /**
     * 获取商户信息变更的文案
     * @param params
     * @return
     */
    Map getMerchantChangeDataTaskStatusByType(@PropNotEmpty.List({
            @PropNotEmpty(value = "type", message = "业务类型不能为空"),
            @PropNotEmpty(value = "merchant_sn", message = "商户号不能为空"),
    }) Map params);

    /**
     * 不存在返回null
     *
     * @param: [merchantSn]
     * @return: com.wosai.upay.job.model.ContractStatus
     * @date: 16:03
     */
    ContractStatus selectByMerchantSn(@NotEmpty(message = "merchantSn不能为空") String merchantSn);

    String getMessageByContractTask(ContractTask contractTask);

    /**
     * @param contractTask 任务
     * @param type 哪一个平台的提示信息 sp crm app
     * <AUTHOR>
     * @Description:
     * @time 15:35
     */
    String getMessageByContractTaskV2(ContractTask contractTask,@NotEmpty(message = "type不能为空") String type);

    /**
     * 获取入网原始 memo 信息
     * <p>
     * merchant-bank-service 异名换卡使用，因为数据总线只有状态，没有信息
     *
     * @param taskId
     * @return
     */
    String getMessageByTaskId(long taskId);

    /**
     * 进件驳回后重新提交
     *
     * @param merchantSn
     * @param fields
     * @return
     */
    Map rejectContractFields(@NotEmpty(message = "merchantSn不能为空") String merchantSn, List<Map<String, Object>> fields);

    /**
     * 进件驳回后重新提交
     *
     * @param merchantSn
     * @param fields
     * @param conf
     * @return
     */
    Map rejectContractFieldsV2(@NotEmpty(message = "merchantSn不能为空") String merchantSn, List<Map<String, Object>> fields, FailEventConf conf);

    /**
     * 判断商户是否已存在实名认证
     * true 已存在
     *
     * @param identity 身份证号码
     * @return
     */
    boolean existMerchant(@NotNull Integer type, @NotEmpty String identity);

    /**
     * @return
     * @Description:微信实名升级任务的状态查询（文案转义）返回相应授权码 返回参数参考  getMerchantContractStatusByType
     * @time 11:11 上午
     **/
    Map getWeixinUpgrade(@NotEmpty String merchantSn, @NotEmpty String soucre, @NotEmpty String taskType);


    /**
     * @return
     * <AUTHOR>
     * @Description:用户验证金额 1 验证中 继续等待验证输入
     * 2验证成功
     * 3验证失败
     * @time 4:38 下午
     **/
    int verifyAmount(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotNull(message = "金额不能为空") BigDecimal amount);

    /**
     * 主动查询代付订单结果
     * status :状态 FAIL SUCCESS PROGRESSING
     * msg:描述
     *
     * @param merchantSn
     * @return
     */
    Map queryPayForResult(@NotEmpty(message = "商户号不能为空") String merchantSn);


    String authChannelCode(@NotEmpty(message = "子商户号不能为空") String subMchId);

    /**
     * 查询商户进件状态，contract_status 失败时再查询有没有正在处理的任务
     *
     * @param merchantSn
     * @return
     */
    int getContractStatus(String merchantSn);
}
