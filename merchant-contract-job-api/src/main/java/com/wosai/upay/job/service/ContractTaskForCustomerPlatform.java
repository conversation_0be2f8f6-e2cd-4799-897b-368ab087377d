package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.workflow.bean.PageResult;
import com.wosai.upay.job.model.ContractInfoReq;
import com.wosai.upay.job.model.dto.ContractInfoDTO;
import com.wosai.upay.job.model.dto.WeChatUpgradeInfoDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Description:客服作业平台
 */
@JsonRpcService("/rpc/contractTaskForCustomer")
@Validated
public interface ContractTaskForCustomerPlatform {

    /**
     *
     * 客服作业平台根据商户号维度，查询进件信息
     * @param req 分页请求信息
     * @return ApiSixResult<PageResult<ContractInfoDTO>>
     */
   PageResult<ContractInfoDTO> getContractTaskForCustomer(@Valid ContractInfoReq req);


    /**
     *
     * 客服作业平台根据商户号维度，查询微信升级信息
     * @param req 分页请求信息
     *@return ApiSixResult<PageResult<WeChatUpgradeInfoDTO>>
     */
    PageResult<WeChatUpgradeInfoDTO> getWeChatUpgradeInfoForCustomer(@Valid ContractInfoReq req);

    /**
     * 客服作业平台根据商户号温度，查询收单机构信息
     * @return
     */
    Map getMerchantAcquirerInfo(@Valid ContractInfoReq req);
}