package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.bsm.creditpaybackend.model.Fitness;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.job.model.payLater.AppViewStatus;
import com.wosai.upay.job.model.payLater.ModifyFeeRate;
import com.wosai.upay.job.model.payLater.PayLaterInfoDTO;
import com.wosai.upay.job.model.payLater.SpaStage;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * @Description: 支付宝先享后付
 * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********&client=149690cf-95f5-49c9-84b9-17d70415aeef&home=
 * <AUTHOR>
 * @Date 2023/7/31 下午2:55
 */
@JsonRpcService("/rpc/pay_later")
@Validated
public interface PayLaterService {

    /**
     * app网关转发过来的参数类似
     *
     {
     "role": "super_admin",
     "client_version": "6.0.0",
     "account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "date_end": "*************",
     "id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "sub_appid": "sqb",
     "uc_user_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "cash_store_id": "a0145693-914f-486d-898c-afadebf127df",
     "merchant_id": "e2bc0928-014e-4a98-bbc0-1b8ea78a3a79",
     "msp_account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "store_id": "a0145693-914f-486d-898c-afadebf127df",
     "role_id": "35eb325c-4fad-4d44-8939-d1f0d46bf3c9",
     "merchant_user_id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "token": "********************************************************************************.76lJ8puYtN"
     }
     */

    /**
     * 落地页是否展示
     * @param params
     * @return
     */
    public Boolean displayIntroduce(@PropNotEmpty.List({
            @PropNotEmpty(value = Fitness.MERCHANT_ID,message = "{value} 不能为空")
    })Map params);

    /**
     * 根据行业获取费率
     * @param params
     * @return
     */
    public String computeFeeRateByIndustry(@PropNotEmpty.List({
            @PropNotEmpty(value = Fitness.MERCHANT_ID,message = "{value} 不能为空")
    })Map params);

    /**
     * 商户营业执照名称
     * @param params
     * @return
     */
    @Deprecated
    public String getMerchantBusinessLicenseName(@PropNotEmpty.List({
            @PropNotEmpty(value = Fitness.MERCHANT_ID,message = "{value} 不能为空")
    })Map params);

    /**
     * 提交先享后付以后,如果成功必须先看到一次审核成功流程
     * @param params
     * @return
     */
    public Boolean haveToLookSuccess(@PropNotEmpty.List({
            @PropNotEmpty(value = Fitness.MERCHANT_ID,message = "{value} 不能为空")
    })Map params);


    /**
     * app端状态
     * @param params
     * @return
     */
    public AppViewStatus getAppStatusView(@PropNotEmpty.List({
            @PropNotEmpty(value = Fitness.MERCHANT_ID,message = "{value} 不能为空")
    })Map params);

    /**
     * 开通先享后付
     * @param payLaterInfoDTO
     * @return
     */
    public void savePayLater(@Valid PayLaterInfoDTO payLaterInfoDTO);


    /**
     * 商户状态变化通知
     * @param contentMap
     * @return
     */
    public void fitnessMerchantSyncStatus(Map<String,String> contentMap);


    /**
     * 修改先享后付商户费率
     * @param modifyFeeRate
     * @return
     */
    public void modifyFitnessFeeRate(@Valid ModifyFeeRate modifyFeeRate);


    /**
     * spa开通流程展示
     * @param merchantSn
     * @return
     */
    public List<SpaStage> getSpaProcess(@NotBlank(message = "商户号不为空") String merchantSn);


    /**
     * 商户对应的行业小程序的APPID
     * @param merchantId
     * @return
     */
    String getMerchantAppId(@NotBlank(message = "商户ID不能为空") String merchantId);


}
