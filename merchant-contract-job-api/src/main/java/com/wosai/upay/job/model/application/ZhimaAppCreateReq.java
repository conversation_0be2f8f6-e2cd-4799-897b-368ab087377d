package com.wosai.upay.job.model.application;

import com.google.common.collect.Lists;
import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Created by hzq on 19/11/22.
 */
@Data
@Accessors(chain = true)
/**
 * 充电宝间连 签约芝麻信用
 */
public class ZhimaAppCreateReq {

    @NotEmpty(message = "merchant_sn不能为空")
    String merchant_sn;

    List<String> terminal_ids = Lists.newArrayList();

    private String fee_rate;

    /************
     * 基础信息字段 都是必传
     ************/

    private String service_name;
    private String contact_phone;
    private String contact_email;
    private String logo;

    /************
     * 风控配置 都是必传
     ************/

    //以下4字段 支付宝都是必填的  文档有误是可选
    private Integer score = 550; //可选	8	芝麻分准入门槛。低于服务使用门槛的消费者将不能使用该品类的服务，推荐填写550分，建议填写范围550~850。
    private String risk_policy = "A2"; //可选	16	综合风险评估，可取值：A1(高准入，满足芝麻分的情况下，通过率在90%以上)；A2(中准入，满足芝麻分情况下，通过率在80%以上)；A3(低准入，满足芝麻分情况下，通过率在60%以上)。
    private Integer global_orders_limit_number = 2; //可选	16	消费笔数上线。代表一个顾客在所有与芝麻信用合作的商户中，可同时享受该产品类信用服务的订单数量，推荐值为2，最多不超过5。
    private Boolean global_quota_switch = Boolean.TRUE; //可选	8	全局限额开关，可取值true和false。关闭后，当顾客在所有与芝麻信用合作的商户中，未结束的订单减免金额总额即便达到拦截阈值时，也可继续享受信用服务。

//     字段描述：是否开放部分免押，商户不能编辑 这个值不能传 否则支付宝会报错
//    private Boolean part_deposit_switch=Boolean.FALSE; //)	8	额度部分免开关，可取值true和false。关闭后，当顾客的可免额度小于所需费用时，顾客不可使用可免额度，需全部用资金支付。

    /************
     * 守护配置 按照文档配置是否可选必填
     ************/
    private String merchant_service_phone; //必填	32	商户客服电话，为必填字段，需为合法电话号码。
    private String money_operation_type; //可选	32	待还款的守约方式，可取值：promise_link(跳转到去守约页面完成守约)；promise_remind(文字提醒守约)。
    private String money_operation_value;//可选	1024	待还款守约方式的取值，值为去守约页面的url或者说明文案。
    private String product_operation_type;//可选	32	待还物的守约方式，可取值：promise_link(跳转到去守约页面完成守约)；promise_remind(文字提醒守约)。
    private String product_operation_value;//可选	1024	待还物守约方式的取值，值为去守约页面的url或者说明文案。
    private String action_operation_type;//	可选	32	提醒用户完成行为的守约方式，可取值：promise_link(跳转到去守约页面完成守约)；promise_remind(文字提醒守约)。
    private String action_operation_value;//可选	1024	提醒用户完成行为的守约方式的取值，值为去守约页面的url或者说明文案。
    private String merchant_service_url;//可选	1024	商户主页链接，取值为商户小程序地址或H5页面地址。
    private String button_end;//可选	1024	信用服务履约单提前结束按钮的url
    private String button_postpone;//可选	1024	信用服务履约单延期按钮的url
    private String button_replace;//可选	1024	信用服务履约单换新按钮的url
    private String button_buy;//可选	1024	信用服务履约单买断按钮的url
    private String button_repair;//可选	1024	信用服务履约单维修按钮的url
    private String button_installment;//可选	1024	信用服务履约单分期按钮的url
    private Integer fulfillment_days;//可选	16	合约履约周期，以天为单位。

    //    String extra_info = "extra_info"; //直接透传到支付宝


//    public ZhimaMCreditCreate toAliBean() {
//        ZhimaMCreditCreate create = new ZhimaMCreditCreate();
//        create.setSolution_id("solution_004");
//        create.setSmid(smid);
//        create.setBiz_no(UUID.randomUUID().toString());
//        create.setCreate_type("indirect");
//        create.setBase_info_config(new ZhimaMCreditCreate.BaseInfoConfig()
//                .setService_name(service_name)
//                .setLogo(logo)
//                .setContact_email(contact_email)
//                .setContact_phone(contact_phone));
//        List<ZhimaMCreditCreate.CategoryRiskInfo> categoryRiskInfos = Lists.newArrayList(new ZhimaMCreditCreate.CategoryRiskInfo()
//                .setCategory_code("ZMSC_1_1_8")
//                .setCategory_name("充电宝")
//                .setScore(score)
//                .setRisk_policy(risk_policy)
//                .setGlobal_orders_limit_number(global_orders_limit_number)
//                .setGlobal_quota_switch(global_quota_switch));
//        create.setRisk_config(new ZhimaMCreditCreate.RiskConfig()
//                .setCategory_risks(categoryRiskInfos)
//                //quota_gradient_rule 接口文档是错误的 这个字段必须填 额度最大值是200
//                .setQuota_gradient_rule(new ZhimaMCreditCreate.QuotaGradientRule()
//                        .setScore_400(200)
//                        .setScore_450(200)
//                        .setScore_500(200)
//                        .setScore_550(200)
//                        .setScore_600(200)
//                        .setScore_650(200)
//                        .setScore_700(200)
//                        .setScore_750(200)
//                        .setScore_800(200)
//                        .setScore_850(200)
//                        .setScore_900(200)
//                        .setScore_950(200)));
//        create.setPromise_config(new ZhimaMCreditCreate.PromiseConfig()
//                .setMerchant_service_phone(merchant_service_phone)
//                .setMoney_operation_type(money_operation_type)
//                .setMoney_operation_value(money_operation_value)
//                .setProduct_operation_type(product_operation_type)
//                .setProduct_operation_value(product_operation_value)
//                .setAction_operation_type(action_operation_type)
//                .setAction_operation_value(action_operation_value)
//                .setMerchant_service_url(merchant_service_url)
//                .setButton_end(button_end)
//                .setButton_postpone(button_postpone)
//                .setButton_replace(button_replace)
//                .setButton_buy(button_buy)
//                .setButton_repair(button_repair)
//                .setButton_installment(button_installment)
//                .setFulfillment_days(fulfillment_days));
//        return create;
//    }

}
