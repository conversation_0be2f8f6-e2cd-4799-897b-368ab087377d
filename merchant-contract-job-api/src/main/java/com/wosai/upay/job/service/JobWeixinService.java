package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.SettlementIdConfig;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * @Description: 微信的通用service
 * <AUTHOR>
 * @Date 2021/11/4 2:36 下午
 **/

@JsonRpcService("/rpc/weixin")
@Validated
public interface JobWeixinService {

    /**
     * 查询 商户当前状态 下 的 微信结算id
     * [指在当前信息使用的微信结算id,不做以往状态的参考]:
     * 1.结算id的规则可能变化;   2:商户的行业可能更改
     *
     * @param merchantSn
     * @return
     */
    String getSettlementId(@NotEmpty(message = "商户sn不能为空") String merchantSn);

    /**
     * 查询 指定名称 行业 证照类型 的微信结算id
     * @param merchantName
     * @param industryId
     * @param type
     * @return
     */
    String getSettlementId(@NotEmpty(message = "商户名不能为空") String merchantName,
                           @NotEmpty(message = "行业类型不能为空") String industryId,
                           @NotEmpty(message = "证照类型不能为空") Integer type);

    /**
     * 根据行业id获取对应的结算id配置
     * @param industryId 行业id
     * @return 配置信息
     */
    SettlementIdConfig getSettlementConfig(@NotEmpty(message = "行业id不能为空") String industryId);

    /**
     * 查询 微信商户号 的信息
     *
     * @param subMchId
     * @return
     */
    WxMchInfo getWxMchInfoBySubMchId(@NotEmpty(message = "微信商户号不能为空") String subMchId);


    /**
     * 查询 微信商户名和settlement_id的信息
     *
     * @param subMchId
     * @return
     */
    WxMchInfo getWxMchNameBySubMchId(@NotEmpty(message = "微信商户号不能为空") String subMchId);
}
