package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/17
 */
@Data
public class DecpParamResp {

    /**
     * 商户名称
     */
    @JsonProperty("merchant_name")
    private String merchantName;

    /**
     * 商户经营名称
     */
    @JsonProperty("business_name")
    private String businessName;

    /**
     * 钱包类型 默认对私钱包
     */
    @JsonProperty("wallet_type")
    private String walletType = "对私钱包";

    /**
     * 客户姓名 取默认银行卡的holder
     */
    private String holder;

    /**
     * 客户身份证号 取默认银行卡的证件号 脱敏处理
     */
    @JsonProperty("id_number")
    private String idNumber;

    /**
     * 手机号
     */
    @JsonProperty("phone_number")
    private String phoneNumber;

    /**
     * 钱包id，开通成功的时候才会返回
     */
    @JsonProperty("wallet_id")
    private String walletId;
}
