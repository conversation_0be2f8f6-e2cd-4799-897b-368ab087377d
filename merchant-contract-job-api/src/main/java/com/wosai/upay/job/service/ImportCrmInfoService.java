package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 用于对接crm,将没有走crm业务开通的数据导入到crm系统中
 * <AUTHOR>
 * @Date 2024/7/4 10:18
 */
@JsonRpcService("/rpc/imp_crm")
@Validated
public interface ImportCrmInfoService {

    /**
     * 更新纪录
     * @param merchantSn 商户号
     * @param appStatus 状态
     * @param appId crm定义的应用appId,找crm同事要
     * @param maintainUserId 如果不指定,默认不传
     * @see AppInfoModel#STATUS_PENDING,AppInfoModel#STATUS_FAIL,AppInfoModel#STATUS_SUCCESS
     */
    public void createOrUpdateBizOpenInfo(@NotBlank(message = "merchantSn不能为空")String merchantSn,
                                          @NotNull(message = "crm中的状态不能为空") Integer appStatus,
                                          @NotBlank(message = "appId不能为空") String appId,
                                          String maintainUserId);

}
