package com.wosai.upay.job.model.dto.response;

import lombok.Data;

/**
 * 插入营业执照更新任务结果响应
 *
 * <AUTHOR>
 * @date 2024/9/6 10:35
 */
@Data
public class InsertLicenceUpdateTaskResultRspDTO {

    private boolean success;

    private String message;

    public static InsertLicenceUpdateTaskResultRspDTO success() {
        InsertLicenceUpdateTaskResultRspDTO result = new InsertLicenceUpdateTaskResultRspDTO();
        result.setSuccess(true);
        return result;
    }

    public static InsertLicenceUpdateTaskResultRspDTO fail(String message) {
        InsertLicenceUpdateTaskResultRspDTO result = new InsertLicenceUpdateTaskResultRspDTO();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
}
