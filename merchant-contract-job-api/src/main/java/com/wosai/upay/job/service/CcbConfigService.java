package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ccbConfig.*;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
@JsonRpcService("/rpc/ccbConfig")
@Validated
public interface CcbConfigService {

    /**
     * 创建建行配置
     *
     * @param req 创建配置请求参数
     * @return 创建结果
     */
    ContractResponse createCcbConfig(@Valid CreateCcbConfigReq req);

    /**
     * 更新建行配置
     *
     * @param req 更新配置请求参数
     * @return 更新结果
     */
    ContractResponse updateCcbConfig(@Valid UpdateCcbConfigReq req);

    /**
     * 删除建行配置
     *
     * @param req 删除建行配置请求参数
     * @return 删除结果
     */
    ContractResponse deleteCcbConfig(@Valid DeleteCcbConfigReq req);

    /**
     * 查询建行配置
     *
     * @param req 请求参数
     * @return 建行配置信息
     */
    ListResult<CcbConfig> getCcbConfig(QueryCcbConfigReq req);

    /**
     * 查询对应的建行配置对应的修改记录
     *
     * @param req 查询修改记录的请求参数
     * @return 变更历史
     */
    ListResult<CcbConfigChangeHistory> getChangeHistoryByCcbConfigId(@Valid QueryCcbChangeHistoryReq req);

    /**
     * 获取ccb地区入网规则
     *
     * @param req
     * @return
     */
    CcbConfig getConfigByCode(ApiRequestParam<QueryCcbConfigCodeReq> req);

    /**
     * 获取ccb地区入网规则
     *
     * @param districtFullName 地区全称
     * @return
     */
    CcbConfig getConfigByLocation(ApiRequestParam<String> districtFullName);

    CcbConfig getConfigByMerchantId(String merchantId);


    /**
     * 根据行业id获取 转化后的mcc
     */
    String getCcbMccByIndustryId(String industryId);
}
