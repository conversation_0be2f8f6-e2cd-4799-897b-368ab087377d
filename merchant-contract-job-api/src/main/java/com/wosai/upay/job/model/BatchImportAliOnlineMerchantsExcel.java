package com.wosai.upay.job.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;


@Data
public class BatchImportAliOnlineMerchantsExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "商户号", width = 20)
    private String merchantSn;

    @Excel(name = "审核状态", width = 20)
    private String status;

    @Excel(name = "拒绝原因", width = 20)
    private String rejectReason;

    @Excel(name = "导入结果")
    private String message;

    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }

    public Boolean isAuditSuccess() {
        return "SUCCESS".equals(status);
    }
}
