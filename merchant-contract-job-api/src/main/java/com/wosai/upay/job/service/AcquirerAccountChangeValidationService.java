package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.dto.AccountChangeValidationResult;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.model.dto.AcquirerAccountChangeRecordRequest;
import org.springframework.validation.annotation.Validated;

import java.util.Date;

/**
 * 收单机构账户变更校验服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@JsonRpcService("/rpc/acquirer-account-validate")
@Validated
public interface AcquirerAccountChangeValidationService {


    /**
     * 校验是否允许账户变更（默认渠道号）
     *
     * @param merchantSn 商户号
     * @param acquirer 收单机构
     * @param oldAccountInfo 原银行卡信息
     * @param newAccountInfo 新银行卡信息
     * @return 校验结果
     */
    AccountChangeValidationResult validateAccountChange(String merchantSn, String acquirer,
                                                        BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo);


    /**
     * 校验是否允许账户变更
     * 
     * @param merchantSn 商户号
     * @param acquirer 收单机构
     * @param providerMerchantId 收单机构商户号（可选，为空则按商户+收单机构维度统计）
     * @param oldAccountInfo 原银行卡信息
     * @param newAccountInfo 新银行卡信息
     * @return 校验结果
     */
    AccountChangeValidationResult validateAccountChange(String merchantSn, String acquirer, String providerMerchantId,
                                                        BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo);

    /**
     * 校验是否允许账户变更-与当前在用银行卡比较（默认渠道商户号）
     *
     * @param merchantSn 商户号
     * @param acquirer 收单机构
     * @param newAccountInfo 新银行卡信息
     * @return 校验结果
     */
    AccountChangeValidationResult validateAccountChangeWithCurrentAccount(String merchantSn, String acquirer,  BankAccountSimpleInfoBO newAccountInfo);
    

    
    /**
     * 记录账户变更信息
     * 
     * @param request 账户变更记录请求
     * @return 记录ID
     */
    Long recordAccountChange(AcquirerAccountChangeRecordRequest request);
    
    /**
     * 更新账户变更记录状态
     * 
     * @param recordId 记录ID
     * @param status 新状态
     * @param remark 备注
     * @return 是否更新成功
     */
    boolean updateAccountChangeStatus(Long recordId, Integer status, String remark);
}