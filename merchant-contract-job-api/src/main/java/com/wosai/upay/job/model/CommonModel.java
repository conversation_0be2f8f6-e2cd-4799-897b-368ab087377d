package com.wosai.upay.job.model;

import com.wosai.data.util.CollectionUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by lihebin on 2018/7/16.
 */
public class CommonModel {


    public final static String RESULT_CODE = "result_code";
    public final static String RESULT_MESSAGE = "message";
    public final static String RESPONSE = "response";

    public final static String RESULT_CODE_FAIL = "0";
    public final static String RESULT_CODE_SUCCESS = "1";
    public final static String RESULT_CODE_ERROR = "2";
    public final static String RESULT_CODE_NEED_AUTH = "3";


    public static final String KEY_CONTRACT_STATUS = "contract_status";


    public static final String ID = "id";
    public final static String MERCHANT_SN = "merchant_sn";
    public final static String MERCHANT_ID = "merchant_id";
    public static final String STORE_SN = "store_sn";
    public final static String PAYWAY = "payway";
    public final static String PROVIDER = "provider";
    public static final String MTIME = "mtime";
    public static final String CTIME = "ctime";


    public final static String CONTENT = "content";
    public final static String TITLE = "title";


    public final static String TEMPLATE = "template";

    public final static String CELLPHONE = "cellphone";


    public final static String BODY = "body";

    public final static String DATA = "data";
    public final static String RESPDATA = "respData";

    public final static String VISIBLE = "visible";

    public final static String AGENT_NAME = "agent_name";

    //log
    public final static String USERNAME = "username";
    public final static String BEFORE_VALUE = "beforeValue";
    public final static String AFTER_VALUE = "afterValue";
    public final static String SYSTEM_CODE = "systemCode";
    public final static String OBJECT_CODE = "objectCode";
    public final static String FUNCTION_CODE = "functionCode";
    public final static String COLUMN_CODE = "columnCode";
    public final static String REMARK = "remark";


    public final static String TRADE_PARAMS = "trade_params";
    public final static String UP_TRADE_PARAMS = "up_trade_params";
    public final static String LAKALA_WANMA_TRADE_PARAMS = "lakala_wanma_trade_params";
    public final static String UP_DIRECT_TRADE_PARAMS = "up_direct_trade_params";
    public final static String LKL_ORG_TRADE_PARAMS = "lkl_up_trade_params";

    public static final Map<String, String> PROVIDER_KEY = CollectionUtil.hashMap(
            "1001", "cibbank_trade_params",
            "1002", "lakala_trade_params",
            "1003", "citicbank_trade_params",
            "1004", "cebbank_trade_params",
            "1005", "czbbank_trade_params",
            "1006", "ssrcbbank_trade_params",
            "1007", "cmbcbank_trade_params",
            "1008", "cibgzbank_trade_params",
            "1009", "citiczjbank_trade_params",
            "1011", "lakala_wanma_trade_params",
            "1013", "nucc_trade_params",
            "1014", "up_trade_params",
            "1015", "cibshbank_trade_params",
            "1016", "up_direct_trade_params",
            "1017", "upo_trade_params",
            "1018", "chinaums_trade_params",
            "1019", "uonline_trade_params",
            "1020", "up_tl_trade_params",
            "1033", "lkl_up_trade_params",
            "1034", "lakala_open_trade_params",
            "1037", "haike_up_trade_params",
            "1038", "fuyou_trade_params"
    );

    public static final String MCH_ID = "mch_id";
    public static final String MERNAME = "mer_name";
    public final static String CHANNEL_ID = "channel_id";
    public final static String RECE_ORG_NO = "rece_org_no";
    public final static String WEIXIN_MCH_ID = "weixin_mch_id";
    public final static String WEIXIN_SUB_APPID = "weixin_sub_appid";
    public final static String WEIXIN_SUB_MCH_ID = "weixin_sub_mch_id";
    public static final String ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";
    public static final String UNION_MCH_ID = "union_mch_id";
    public static final String PROVIDER_MCH_ID = "provider_mch_id";
    public static final String PARAMS_CONFIG_STATUS = "params_config_status";
    public static final String GOLD_STATUS = "gold_status";


    public final static String FEE_RATE = "fee_rate";
    public final static String LIQUIDATION_NEXT_DAY = "liquidation_next_day";


    public static final List<String> MUNICIPAL_CITY_LIST = Arrays.asList("北京市", "上海市", "天津市", "重庆市");

    public static final String KEY_REQUEST_PARAMS = "resquestParams";
    public static final String KEY_RESPONSE_MESSAGE = "responseMsg";

    public static final String TYPE = "type";


    public static final String TONGLIAN_TRADE_PARAMS = "up_tl_trade_params";




}
