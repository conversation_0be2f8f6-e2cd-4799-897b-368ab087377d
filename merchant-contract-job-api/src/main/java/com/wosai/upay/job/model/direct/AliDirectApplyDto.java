package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/22
 */
@Data
@Accessors(chain = true)
public class AliDirectApplyDto {

    private String merchant_sn;

    private Long task_id;

    private String batch_no;

    private Integer status;

    private String sign_url;

    private String result;

    private String user_id;

    private Date create_at;

    private Date priority;

    private Date update_at;

    private String request_body;

    private String response_body;

    private String form_body;
}
