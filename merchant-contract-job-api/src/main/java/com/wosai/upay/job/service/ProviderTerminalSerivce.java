package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ProviderTerminalBindConfigFindReq;
import com.wosai.upay.job.model.ProviderTerminalBindConfigResp;
import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import com.wosai.upay.job.model.ProviderTerminalDeleteReq;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAddRequest;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalAddResult;
import com.wosai.upay.job.model.providerterminal.ProviderTerminalBindTaskForNewSubMchAddRequest;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@JsonRpcService("/rpc/terminal")
@Validated
public interface ProviderTerminalSerivce {

    /**
     * 根据门店信息修改终端
     * @param store
     */
    void updateProviderTermianl(Map store);

    /**
     * 查询商户下的绑定记录, 给SPA上的喔嗒用的，返回值还有格式要求
     * @param req 请求参数
     * @return
     */
    Map<String, Object> findBindConfigs(ProviderTerminalBindConfigFindReq req);

    /**
     * 查询商户下所有[终端-支付源]绑定记录
     * @param merchantSn
     * @return
     */
    List<ProviderTerminalBindConfigResp> getBindConfigsByMerchantSn(String merchantSn);

    /**
     * 查询门店下所有[终端-支付源]绑定记录
     * @param storeSn
     * @return
     */
    List<ProviderTerminalBindConfigResp> getBindConfigsByStoreSn(String storeSn);

    /**
     * 查询终端下的所有[终端-支付源]绑定记录
     * @param terminalSn
     * @return
     */
    List<ProviderTerminalBindConfigResp> getBindConfigsByTerminalSn(String terminalSn);

    /**
     * 同步收单机构的 终端绑定记录
     * @param id
     * @return
     */
    ProviderTerminalBindConfig syncAcquireBindConfig(String id);


    /**
     * 根据终端Id,修改该商户T9设备交易上送数据
     * @param termId
     */
    void updateT9TerminalInfo(@NotBlank(message = "终端Id不为空") String termId);


    /**
     * 先解绑然后再重新绑定T9终端,这里的解绑/绑定指的是操作终端在银联的状态
     * @param termId
     */
    void restoreT9TerminalInfo(@NotBlank(message = "终端Id不为空") String termId);



    /**
     * 根据终端Id,修改该商户富友T9设备交易上送数据
     * @param termId
     */
    void updateFuYouT9TerminalInfo(@NotBlank(message = "终端Id不为空") String termId);

    /**
     * CUA-11092
     * 删除指定商户&支付通道 provider_terminal和 provider_terminal_bind_config数据
     * 小微盘店之后调用该接口删除旧商户终端级别的数据
     * @param req 请求参数
     */
    void deleteTerminalLevelProviderTerminal(@Valid ProviderTerminalDeleteReq req);

    /**
     * 添加终端
     * @param providerTerminalAddRequest 请求参数
     * @return 终端添加结果
     */
    ProviderTerminalAddResult addProviderTerminal(@Valid ProviderTerminalAddRequest providerTerminalAddRequest);

    /**
     * 新增子商户号添加终端绑定任务
     * @param request 请求参数
     */
    void addProviderTerminalBindTaskForNewSubMch(@Valid ProviderTerminalBindTaskForNewSubMchAddRequest request);

}
