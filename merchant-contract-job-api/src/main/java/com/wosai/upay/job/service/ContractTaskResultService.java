package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractTask;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Auther: lishuangqiang
 * @Date: 2019/4/1 14:54
 * @Description:查询任务
 */
@JsonRpcService("/rpc/contracTaskResult")
@Validated
public interface ContractTaskResultService {
    Map<String, Object> getContractasks(Map queryFilter);

    ContractTask selectByPrimaryKey(@NotNull(message = "id不能为null") Long id);

    /**
     * 子商户号报备成功的 微信实名认证流程
     * @param merchantSn
     * @param mchId
     * @return
     */
    ContractTask getWechatAuthSucceedTask(String merchantSn, String mchId);

}