package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.upay.job.validation.group.AddRuleGroup;
import com.wosai.upay.job.validation.group.UpdateRuleGroup;
import lombok.Data;
import lombok.ToString;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * RuleGroupDto
 *
 * <AUTHOR>
 * @date 2019-07-16 13:53
 */
@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RuleGroupDto implements Serializable {
    private static final long serialVersionUID = -5883196833135584786L;
    private Integer id;

    @NotBlank(message = "规则组唯一标识 group_id 不能为空", groups = UpdateRuleGroup.class)
    private String group_id;

    @NotBlank(message = "规则组名称 name 不能为空", groups = AddRuleGroup.class)
    private String name;

    @NotBlank(message = "业务方 vendor 不能为空", groups = AddRuleGroup.class)
    private String vendor;

    @NotBlank(message = "业务方应用 vendor_app 不能为空", groups = AddRuleGroup.class)
    private String vendor_app;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private List<ContractRuleCustomDto> contract_rules;

    private List<Rule> rules;

    @Data
    public static class Rule {
        private String contract_rule;
        private String depend_on;
    }
}
