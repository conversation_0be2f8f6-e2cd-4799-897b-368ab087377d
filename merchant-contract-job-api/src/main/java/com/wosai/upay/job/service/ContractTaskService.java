package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractTask;

import javax.validation.constraints.NotEmpty;

import org.springframework.validation.annotation.Validated;

/**
 * @Description:报备任务服务 直接插入任务
 * <AUTHOR>
 * Date 2019/10/13 11:25 上午
 * @see ContractEventService  报备事件服务直接插入事件（通联入网流程）
 * @see SelfHelpNetInEventService  插入事件（自主入网流程 银行卡服务调用）
 **/
@JsonRpcService("/rpc/contractTask")
@Validated
public interface ContractTaskService {


    /**
     * 调用微信接口查询微信子商户号是否已授权
     *
     * @param mchId
     * @return
     */
    boolean checkMchAuthBySubMchId(@NotEmpty(message = "微信子商户号不能为空") String mchId);

    /**
     * 调用微信接口查询商户在用微信子商户号是否已授权
     *
     * @param merchantSn
     * @return
     */
    boolean checkMchAuthByMchSn(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * <AUTHOR>
     * @Description: 将最新银行卡同步到原有收单机构
     * @time 16:42
     */
    ContractTask createSuccessTask(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * <AUTHOR>
     * @Description: 将最新银行卡同步到指定的收单机构
     * @time 16:42
     */
    ContractTask createTaskByAssignAcquire(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "收单机构不能为空") String acquirer);


    /**
     * 将指定银行卡同步到指定的收单机构
     *
     * @param merchantSn 商户号
     * @param acquire    收单机构
     * @param bankPreId  merchant_bank_account_pre表id
     * @return
     */
    ContractTask syncBankAccount2Acquire(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                         @NotEmpty(message = "收单机构不能为空") String acquire,
                                         @NotEmpty(message = "merchant_bank_account_pre表id") String bankPreId);


    /**
     * 将费率同步到指定的收单机构
     *
     * @param merchantSn
     * @param acquirer
     * @return
     */
    ContractTask syncFeeRate2Acquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "收单机构不能为空") String acquirer);


    /**
     * 将费率同步到指定的收单机构
     *
     * @param merchantSn
     * @param acquirer
     * @return
     */
    ContractTask syncFeeRate2Acquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "收单机构不能为空") String acquirer, @NotEmpty(message = "多业务id不能为空") String appid);


    /**
     * 新增富友更新商户基本信息任务
     *
     * @param merchantSn 商户号
     * @return 主任务主键id
     */
    Long insertFuYouUpdateMerchantInfoTask(String merchantSn);


    /**
     * 更新首家门店照片同步收单机构
     * 目前支持的收单机构：拉卡拉，富友，海科，收银宝
     *
     * @param merchantSn 商户号
     * @param storeId    门店id
     */
    void updateFirstStorePhotoSyncToAcquirer(String merchantSn, String storeId);

}
