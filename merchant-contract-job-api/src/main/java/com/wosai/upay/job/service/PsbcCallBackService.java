package com.wosai.upay.job.service;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.merchant.contract.model.psbc.bo.CallBackResponse;
import com.wosai.upay.merchant.contract.model.psbc.bo.MerchantStatusCallBack;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 邮储回调处理(merchant_contract项目中contractMerchantCallback调用)
 * <AUTHOR>
 * @Date 2021/4/16 15:47
 */
@JsonRpcService("/rpc/psbc_callBack")
@Validated
public interface PsbcCallBackService {

    /**
     * @param merchantStatusCallBack 邮储返回信息
     * @return Map<String,Object>
     * <AUTHOR>
     * @Description:
     * @time 09:26
     */
    CallBackResponse psbcCallBackHandle(MerchantStatusCallBack merchantStatusCallBack);
}
