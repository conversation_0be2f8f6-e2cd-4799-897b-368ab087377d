package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.model.ChangeAuditPushRequest;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.dto.acquirePos.*;
import com.wosai.upay.job.model.fyPos.OpenForeignCardRequest;
import com.wosai.upay.job.model.fyPos.OpenPreAuthRequest;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.model.sybPos.OpenSybForeignCardRequest;
import com.wosai.upay.job.model.sybPos.OpenSybPreAuthRequest;
import com.wosai.upay.merchant.contract.model.LimitResp;
import com.wosai.upay.merchant.contract.model.fuyou.response.ModifyCancelResponse;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


/**
 * @Description: 用于处理收单机构T9刷卡功能
 * <AUTHOR>
 * @Date 2024/2/26 17:16
 */
@JsonRpcService("/rpc/t9")
@Validated
public interface T9Service {

    /**
     * 扫码绑定校验,提供给IOT
     */
    void bindCheck(@Valid InnerBindCheckDTO dto);


    /**
     * crm开通校验
     */
    void crmOpenCheck(@Valid CrmOpenCheckDTO dto);


    /**
     * 开通银行卡刷卡
     * @param request
     * @return
     */
    ContractResponse applyPos(ApplyPosRequest request);

    /**
     * 获取POS机激活信息
     *
     * @param dto
     * @return
     */
    PosActiveInfo getPosActiveInfo(PosActiveInfoDTO dto);


    /**
     * 终端解绑 富友终端解绑
     * @param dto
     */
    void fyTermCancel(@Valid UnbindDTO dto);


    /**
     * 富友新增终端
     * @param
     */
    void fyTermAdd(@NotBlank(message = "终端号不为空") String terminalSn) throws CommonPubBizException;


    /**
     * 本接口用于spa端 外卡开通状态 Boolean-开通,false-未开通
     * 注意 通联外卡不需要T+1结算产品定义不需要在spa显示通联外卡
     * @return
     */
    Boolean getForeignCardStatus(@NotBlank(message = "商户号不为空") String merchantSn);

    /**
     * 刷卡开通状态 Boolean-开通,false-未开通
     * @return
     */
    Boolean getT9PosStatus(@NotBlank(message = "商户号不为空") String merchantSn);

    /**
     * 刷卡开通状态
     *
     * @param merchantSn 商户号
     * @param acquirer 收单机构
     * @return Boolean-开通,false-未开通
     */
    Boolean isT9PosOpen(@NotBlank(message = "商户号不为空") String merchantSn, String acquirer);


    /**
     * 富友扣率变更撤销
     * @param
     */
    ModifyCancelResponse fyModifyCancel(@NotBlank(message = "商户号不为空") String merchantSn,
                                        @NotBlank(message = "修改编号") String modifyNo);

    /**
     * 商户银行卡限额查询
     * @param merchantSn
     * @return
     */
    List<LimitResp> queryLimit(String merchantSn);

    /**
     * 判断商户是否开通T9刷卡功能并绑定一体化终端
     *
     * @param merchantSn 商户号
     * @return 是否开通成功并绑定终端 true-成功
     */
    Boolean isT9OpenSuccessAndBindTerminal(String merchantSn);

    /**
     * 注销对应收单机构一体化刷卡业务
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     */
    Boolean deactivateIntegratedCardBusiness(String merchantSn, String acquirer);



    /**
     * 当富友外卡开通审核通过后变更状态
     * @param auditPushRequest
     */
   void changeFyForeignCardStatus(ChangeAuditPushRequest auditPushRequest);


    /**
     * 开通富友外卡
     * @param request
     */
    void openFyForeignCard(OpenForeignCardRequest request);


    /**
     * 开通富友预授权
     * @param request
     */
    void openFyPreAuth(OpenPreAuthRequest request);


    /**
     * 关闭外卡
     * @param merchantSn
     * @param acquirer
     * @return
     */
     void closeForeignCard(String merchantSn, String acquirer);

    /**
     * 关闭预授权
     * @param merchantSn
     * @param acquirer
     * @return
     */
    void closePreAuth(String merchantSn, String acquirer);

    /**
     * 申请添加收银宝银行卡刷卡产品
     * @param merchantId 商户ID
     * @param feeMap 费率信息
     */
     void applyBankCardProduct(@NotBlank(message = "商户ID不为空") String merchantId,
                                   @NotEmpty(message = "商户费率") Map<String, Object> feeMap);

    /**
     * 申请添加收银宝外卡产品
     * @param merchantId 商户ID
     * @param feeMap 费率信息
     */
     void applyForeignCardProduct(@NotBlank(message = "商户ID不为空") String merchantId,
                                  @NotEmpty(message = "商户费率") Map<String, Object> feeMap);


    /**
     * 申请添加收银宝预授权产品
     * @param merchantId 商户ID
     */
     void applyPreAuthProduct(@NotBlank(message = "商户ID不为空") String merchantId);


    /**
     * 开通收银宝外卡
     * @param request
     */
    void openSybForeignCard(@Valid OpenSybForeignCardRequest request);



    /**
     * 开通收银宝预授权校验
     * @param merchantId
     */
    void openSybPreAuthCheck(@NotBlank(message = "商户Id不为空")String merchantId) throws CommonPubBizException;


    /**
     * 开通收银宝预授权
     * @param request
     */
    void openSybPreAuth(@Valid OpenSybPreAuthRequest request);


    /**
     * 向收单机构添加银行卡刷卡终端
     * @param terminalSn 终端号
     */
    void t9TermAdd(@NotBlank(message = "终端号不为空") String terminalSn) throws CommonPubBizException;


    /**
     * 刷卡终端解绑 注意富友不使用这个,原因富友解绑的时候需要穿换一个deviceFingerprint,但是IOT已经把设备删除,导致如果使用终端号
     * 重新查询得到的deviceFingerprint带有"-delete*"后缀,导致和添加的数据不一致,所以富友不使用这个接口
     * @see com.wosai.upay.job.service.T9Service#fyTermCancel(com.wosai.upay.job.model.dto.acquirePos.UnbindDTO)
     * @param terminalSn 终端号
     */
    void t9TermUnbind(@NotBlank(message = "终端号不为空") String terminalSn);



    /**
     * 查询商户提额详细信息
     *
     * @param merchantId 商户Id（必填）
     * @return 包含完整资质信息及当前额度的响应
     */
    MerchantQuotaDetailDTO getMerchantQuotaInfo(@NotBlank(message = "商户Id不为空") String merchantId);

    /**
     * 查询商户信息
     *
     * @param merchantId 商户id（必填）
     * @return 包含商户营业执照信息和银行卡信息信息
     */
    MerchantTypeDTO getMerchantType(@NotBlank(message = "商户Id不为空") String merchantId);
}
