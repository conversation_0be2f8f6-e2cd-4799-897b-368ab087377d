package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.ContractResponse;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * @Description: 银联开放平台的一番操作
 * <AUTHOR>
 * @Date 2020/8/28 6:12 PM
 **/
@JsonRpcService("/rpc/unionOpenJob")
@Validated
public interface UnionOpenJobService {

    /**
     * 获取银联开放平台参数
     * @param merchantSn
     * @return
     */
    Map<String, Object> getUnionOpenParamByMerchantSn(@NotEmpty(message="商户号不能为空") String merchantSn);

    /**
     * 通联商户 注册银联开放平台商户号
     * @param merchantSn
     * @return
     */
    ContractResponse contractUnionMerchant(@NotEmpty(message = "商户号不能为空") String merchantSn);
}
