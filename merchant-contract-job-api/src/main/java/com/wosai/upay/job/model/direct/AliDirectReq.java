package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
@Data
@Accessors(chain = true)
public class AliDirectReq {

    @NotBlank(message = "商户号不能为空")
    private String merchant_sn;

    @NotBlank(message = "应用标识不能为空")
    private String dev_code;

    private String remark;

    @Valid
    private Contact_info contact_info = new Contact_info();
    @Valid
    private App_info app_info = new App_info();

    @Data
    @Accessors(chain = true)
    public static class Contact_info {
        @NotBlank(message = "联系人姓名不能为空")
        private String contact_name;
        @NotBlank(message = "联系人电话不能为空")
        private String contact_phone;
        @NotBlank(message = "联系人邮箱不能为空")
        private String contact_email;
    }

    @Data
    @Accessors(chain = true)
    public static class App_info {

        private String account_pic;
        @NotBlank(message = "支付宝账号不能为空")
        private String account;
        @NotBlank(message = "费率不能为空")
        private String fee_rate;
        @NotNull(message = "套餐id不能为空")
        private Long trade_combo_id;
    }
}
