package com.wosai.upay.job.model;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * Created by lihebin on 2018/9/13.
 */
public class ContractConfig {
    public static final String NAME = "name";                                               //varchar(64) NOT NULL COMMENT '路由配置名称 ',

    public static final String DEFAULT_ROUTING_CITY = "其他";
    public static final List<String> WEIXIN_PAYPAY_AUTH_PATH = Lists.newArrayList(
            "https://qr.shouqianba.com/",
            "https://m.wosai.cn/qr/",
            "https://open-wap.shouqianba.com/"
    );  //微信授权支付目录

}
