package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.model.OnlinePaymentFailMessageQueryReq;
import com.wosai.upay.job.model.onlinePayment.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Validated
@JsonRpcService("/rpc/onlinePayment")
public interface OnlinePaymentService {

    /**
     * 开通线上收款
     * @param req 请求参数
     * @return 开通结果
     */
    OnlinePaymentOpenResp openOnlinePayment(@Valid OnlinePaymentOpenReq req);

    /**
     * 校验商户是否允许开通线上收款
     * @param req 请求参数
     * @return 是否允许开通线上收款
     */
    OnlinePaymentOpenCheckResp checkMerchantAllowOpenOnlinePayment(@Valid OnlinePaymentOpenCheckReq req);

    /**
     * 暂时没有调用
     * 查询商户线上收款开通状态
     * @param req 请求参数
     * @return 开通状态 0未开通 1开通中 2开通成功 3开通失败
     */
    Integer queryMerchantOnlinePaymentOpenStatus(@Valid MerchantOnlinePaymentOpenStatusReq req);

    /**
     * 查询商户线上收款子商户号的认证状态
     * @param req 请求参数
     * @return true认证成功 false认证失败
     */
    Boolean queryOnlinePaymentSubMchIdAuthStatus(@Valid OnlinePaymentSubMchIdAuthStatusQueryReq req);

    /**
     * 查询线上收款申请单信息
     * @param req 请求参数
     * @return 申请单信息
     */
    OnlinePaymentApplyInfoQueryResp queryOnlinePaymentApplyInfo(@Valid OnlinePaymentApplyInfoQueryReq req);

    /**
     * 导入支付宝线上收款加白商户
     * @param merchantSn 商户号
     */
    void importAliOnlineMerchants(@NotBlank(message = "商户号不能为空") String merchantSn, @NotBlank(message = "状态不能为空") String status, String rejectReason);

    /**
     * 查询线上收款失败文案
     * @param req
     * @return
     */
    Map<PlatformEnum, String> queryOnlinePaymentFailMessage(@Valid OnlinePaymentFailMessageQueryReq req);


}
