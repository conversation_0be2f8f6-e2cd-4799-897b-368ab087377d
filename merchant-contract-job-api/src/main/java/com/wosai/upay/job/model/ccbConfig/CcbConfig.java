package com.wosai.upay.job.model.ccbConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

/**
 * 建行配置
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CcbConfig {
    private Long id;

    private String district_code;

    private String province;

    private String city;

    private String private_min_price;

    private String public_min_price;

    private Boolean support_select_ins_no;

    private String ins_no;

    private String ins_no_list;

    private String account;

    private Boolean is_filter;

    private Boolean is_auto_change;

    private Integer delay_day;

    private String filter_rules;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    /**
     * 该地区的小微商户是否需要提交某些信息
     * true 需要; false 不需要
     */
    private Boolean micro_info;

    private String black_mcc;

    private Boolean apply_unionpay;

    private Boolean apply_decp;

    public void setIs_filter() {
        this.is_filter = !StringUtils.isEmpty(filter_rules);
    }

    public Boolean getIs_filter() {
        return !StringUtils.isEmpty(filter_rules);
    }
}