package com.wosai.upay.job.model.pos;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 开通预授权参数
 * <AUTHOR>
 * @Date 2025/03/21 下午5:02
 */
@Data
@Accessors(chain = true)
public class OpenPreAuthBaseRequest {
    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private String merchantId;

    /**
     * 应用开发者标识
     */
    @NotBlank(message = "应用开发者标识不能为空")
    private String devCode;



}
