package com.wosai.upay.job.model.providerterminal;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
@Accessors(chain = true)
public class ProviderTerminalBindTaskForNewSubMchAddRequest {

    @NotBlank(message = "商户号不能为空")
    private String merchantSn;
    @NotBlank(message = "子商户号不能为空")
    private String subMerchantId;
    @NotNull(message = "支付渠道不能为空")
    private PaywayEnum payway;
    /**
     * 子商户号所属通道
     */
    @NotNull(message = "通道不能为空")
    private ProviderEnum provider;
}
