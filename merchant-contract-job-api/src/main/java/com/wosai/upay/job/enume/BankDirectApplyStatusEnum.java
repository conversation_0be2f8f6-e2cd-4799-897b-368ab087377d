package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 子商户号用途枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum BankDirectApplyStatusEnum {
    /**
     * 无处理任务
     */
    NO_TASK("0000", "该商户无申请任务"),

    /**
     * 待处理
     */
    PENDING_TASK("1111", "申请任务待处理"),

    /**
     * 给个默认审核中的
     */
    BANK_AUDITING("2222","审核中"),

    /**
     * 成功
     */
    BANK_TASK_SUCCESS("8888", "审核成功"),

    /**
     * 未知失败原因
     */
    UNKNOWN_CODE("9999", "审核失败");

    ;
    private String code;
    private String message;

    public static String getMessage(Integer code) {
        final BankDirectApplyStatusEnum[] bankDirectApplyStatusEnums = values();
        for (BankDirectApplyStatusEnum bankDirectApplyStatusEnum : bankDirectApplyStatusEnums) {
            if (Objects.equals(bankDirectApplyStatusEnum.getCode(), code)) {
                return bankDirectApplyStatusEnum.getMessage();
            }
        }
        return null;
    }
}