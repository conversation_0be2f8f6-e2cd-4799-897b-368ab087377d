package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.dto.response.InsertFuYouDayZeroTaskResultRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 富友任务调度服务
 *
 * <AUTHOR>
 * @date 2024/5/13 09:38
 */
@JsonRpcService("/rpc/task/fuyou")
@Validated
public interface FuYouTaskService {


    /**
     * 新增富友开通D0任务
     *
     * @param merchantSn        商户号
     * @param duplicationCheck  重复校验 true-判断任务是否已存在,存在不会创建任务 false-不判断
     * @param completeDaysLimit 任务完成天数限制 null-默认天数限制
     * @return 是否成功 true-成功
     */
     InsertFuYouDayZeroTaskResultRspDTO insertFuYouOpenDayZeroTask(@NotBlank(message = "商户号不能为空") String merchantSn,
                                                                   boolean duplicationCheck,
                                                                   Integer completeDaysLimit);


    /**
     * 根据任务id触发富友开通D0任务
     *
     * @param taskId 任务id
     * @param force 是否强制处理任务
     */
     void processFuYouOpenDayZeroTaskByTaskId(@NotNull(message = "任务id不能为空") Long taskId, boolean force);


    /**
     * 处理富友开通D0定时任务(xxl-job调度)
     *
     * @param beginTimeGapDays 开始时间间隔(天数)
     * @param taskNum          任务数量
     * @param force            是否强制处理任务
     */
     void processFuYouOpenDayZeroTask(Integer beginTimeGapDays , Integer taskNum, boolean force);


    /**
     * 获取富友D0开通结果定时任务(xxl-job调度)
     *
     * @param beginTimeGapDays 开始时间间隔(天数)
     * @param taskNum          任务数量
     * @param force            是否强制处理任务
     */
     void queryFuYouOpenDayZeroResultTask(Integer beginTimeGapDays, Integer taskNum, boolean force);

    /**
     * 根据任务id手动触发查询富友D0开通结果
     *
     * @param taskId 任务id
     * @return 开通结果
     */
     String queryFuYouOpenDayZeroResultByTaskId(@NotNull(message = "任务id不能为空") Long taskId);


}
