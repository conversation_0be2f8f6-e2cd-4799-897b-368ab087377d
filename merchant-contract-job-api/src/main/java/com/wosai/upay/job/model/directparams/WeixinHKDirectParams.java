package com.wosai.upay.job.model.directparams;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 微信香港直连交易参数
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Data
public class WeixinHKDirectParams extends BaseParams {

    private WeixinTradeParams weixin_trade_params;


    @Data
    public static class WeixinTradeParams extends BaseTradeParams {
        private String weixin_mch_id;

        private String weixin_appid;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(weixin_appid) && StringUtils.isEmpty(weixin_mch_id);
        }
    }
}
