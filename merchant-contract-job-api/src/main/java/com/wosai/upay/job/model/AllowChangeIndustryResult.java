package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
@Data
@Accessors(chain = true)
public class AllowChangeIndustryResult {

    private Boolean allow;
    private String message;

    public static AllowChangeIndustryResult success() {
        return new AllowChangeIndustryResult()
                .setAllow(true);
    }

    public static AllowChangeIndustryResult fail(String message) {
        return new AllowChangeIndustryResult()
                .setAllow(false)
                .setMessage(message);
    }
}
