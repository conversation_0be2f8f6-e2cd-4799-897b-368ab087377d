package com.wosai.upay.job.model.dto.acquirePos;

import lombok.Data;



/**
 * 商户提额详细信息响应体
 * <AUTHOR>
 */
@Data
public class MerchantQuotaDetailDTO {

    /**
     * 经营名称
     */
    private String name;


    /**
     * 行业
     */
    private String businessScope;




    /**
     * 通联支付商户编号
     */
    private String tlMerchantNo;

    /**
     * 商户资质证明文件
     * - 小微商户可不上传（需配合isSmallMicro字段使用）
     * - 支持JPG/PNG/PDF格式
     */
    private String businessLicenseImage;

    /**
     * 法人身份证正面扫描件
     */
    private String legalPersonIdCardFrontPhoto;


    /**
     * 法人身份证反面扫描件
     */
    private String legalPersonIdCardBackPhoto;

    /**
     * 企业开户许可证
     */
    private String accountOpeningLicense;

    /**
     * 银行卡正面扫描件
     * - 要求显示完整卡号（部分数字可打码）
     * - 需与开户支行信息匹配
     */
    private String bankCardFrontImage;

    /**
     * 开户支行
     */
    private String branchName;

    /**
     * 实体经营场所影像记录
     * - 包含三个角度的照片：
     *   1. 内部经营环境
     *   2. 外部街道视角
     *   3. 门店招牌特写
     * - 每张照片大小不超过5MB
     */

    private String indoorImage;

    private String outdoorImage;

    private String brandImage;

    /**
     * 法人电话
     */
    private String legalPersonPhone;


    /**
     * 经营地址
     */
    private String businessAddress;

    /**
     *  价目表
     */
    private String priceImage;

}