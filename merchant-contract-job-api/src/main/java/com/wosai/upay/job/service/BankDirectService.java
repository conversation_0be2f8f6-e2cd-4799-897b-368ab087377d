package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import javax.validation.constraints.NotBlank;

import com.wosai.upay.job.model.psbc.ModifySupportingMaterialsRequest;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.validation.constraints.NullOrNotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 开通银行业务规范
 * <AUTHOR>
 * @Date 2021/4/7 09:49
 */
@JsonRpcService("/rpc/bank_direct_service")
@Validated
public interface BankDirectService {

    /**
     * @param: BankDirectReq 申请参数
     * @return:
     * @Author: zhmh
     * @Description:
     * @time: 09:51 2021/4/7
     */
    ContractResponse applyBankDirect(@Valid BankDirectReq bankDirectReq);

    /**
     * 获取申请单的状态和文案 给内部使用
     *
     * @param merchantSn 商户号
     * @param devCode    应用标识
     * @param platform   平台
     * @return 状态和文案
     */
    ApplyStatusResp getApplyInfo(@NotBlank(message = "商户号不能为空") String merchantSn,
                                 @NotBlank(message = "应用标识不能为空") String devCode,
                                 @NotBlank(message = "平台不能为空") String platform);

    /**
     * 获取申请单的转义后的文案状态
     *
     * @param merchantSn 商户号
     * @param devCode    应用标识
     * @return 状态
     */
    Integer getApplyStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                           @NotBlank(message = "应用标识不能为空") String devCode);

    /**
     * 获取商户进件银行对应的contract_task状态
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @return 进件任务状态，枚举见TaskStatus
     */
    Integer getContractTaskStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                                 @NotBlank(message = "应用标识不能为空") String devCode);

    /**
     * 获取建行签约的参数和链接等
     * @param request
     * @return
     */
    Map<String, String> getSignParamForCcb(Map request);

    /**
     * 开通建行数字货币
     * @param request 商户id、手机号不能为空
     * @return 开通结果
     */
    ContractResponse openCcbDecp(Map request);

    /**
     * 获取开通数币的参数
     * 从银行卡信息中获取
     * 如果有提交记录则从提交记录中获取手机号，否则获取商户的联系人手机号返回
     * @param request   token带来的商户id等
     * @return 回显的信息
     */
    DecpParamResp getOpenDecpParam(Map request);

    /**
     * 获取开通成功数币的信息
     * 如果是开通融合成功，则从融合信息中获取
     * 否则从批量开通的数据中获取
     */
    DecpParamResp getSuccessDecpParam(Map request);

    /**
     * 取消该商户的数币交易权限 && 如果该商户有批量开通的记录，将批量开通记录改为未提交、未激活
     * @param request
     * @return
     */
    ContractResponse cancelCcbDecp(Map request);


    /**
     * 自助撤销接口
     * @param request
     * @return
     */
    ContractResponse selfAuditReject(@Valid SelfAuditRejectRequest request);


    /**
     * 主动获取银行直连审核中的流程状态
     * @param merchantSn
     * @param devCode
     * @return
     */
    List<ViewProcess> getViewProcess(@NotBlank(message = "商户号不能为空") String merchantSn,
                                     @NotBlank(message = "应用标识不能为空") String devCode);

    /**
     * 修改辅助证明材料
     * @return
     */
    ContractResponse modifySupportingMaterials(@Valid ModifySupportingMaterialsRequest req);


    /**
     * 更新银行审批状态
     */
    void refreshAndHandleContractStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                                      @NotBlank(message = "应用标识不能为空") String devCode);



    /**
     * 申请邮储门店码
     * @param qrCount 指定申请数量
     * @return
     */
    void qrCodeBatchApply(@NotNull Integer qrCount);

    /**
     * 邮储二维码绑定解绑
     * @param storeId 商户Id
     * @param operType 操作类型 0-解绑；1-绑定
     * @param qrCode 收款码ID
     * @return
     */
    void psbcQrCodeOperate(@NullOrNotBlank(message = "门店ID不能为空") String storeId,
                           @NotNull(message = "操作类型不能为空") Integer operType,
                           @NullOrNotBlank(message = "收款码不能为空") String qrCode);



    /**
     * app网关转发过来的参数类似
     *
     {
     "role": "super_admin",
     "client_version": "6.0.0",
     "account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "date_end": "*************",
     "id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "sub_appid": "sqb",
     "uc_user_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "cash_store_id": "a0145693-914f-486d-898c-afadebf127df",
     "merchant_id": "e2bc0928-014e-4a98-bbc0-1b8ea78a3a79",
     "msp_account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "store_id": "a0145693-914f-486d-898c-afadebf127df",
     "role_id": "35eb325c-4fad-4d44-8939-d1f0d46bf3c9",
     "merchant_user_id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "token": "********************************************************************************.76lJ8puYtN"
     }
     */

    /**
     * 获取平安签约链接
     *
     * @param map
     * @return
     */
    String getPabSignUrl(Map map);


    /**
     * 平安回调给前端,前端再收钱吧App回调给我
     * @param map
     */
    void pabSignCallBack(Map map);

    /**
     * 针对华夏停用终端，调华夏终端注册接口修改状态
     * @param payMerchantId
     * @param providerTerminalId
     * @return
     */
    ContractResponse restartHXTerminal(String payMerchantId, String providerTerminalId);

    /**
     * 华夏通道商户 创建新门店校验 是否与商户异地
     * @param req
     * @return
     */
    HxStoreCheckResp checkCreateStoreProvinceDiffMerchant(@Valid CheckStoreDiffProvinceReq req);


    /**
     * 门店省份与商户省份异地校验
     * @param merchantId
     * @return
     */
    HxStoreCheckResp haveDiffProvinceStoreByMerchantId(String  merchantId);


    /**
     * 民生银行分润模式
     * @return
     */
    Set<String> getCmbcProfitSharingMode();




    /**
     * 民生银行签约机构
     * @return
     */
    Set<String> getCmbcContractBranch();
}
