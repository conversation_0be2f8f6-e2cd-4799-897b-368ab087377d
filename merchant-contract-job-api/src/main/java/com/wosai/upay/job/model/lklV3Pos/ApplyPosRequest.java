package com.wosai.upay.job.model.lklV3Pos;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2022/7/26 09:58
 */
@Data
@Accessors(chain = true)
public class ApplyPosRequest {
    /**
     * 商户id
     */
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    /**
     * 应用开发者标识
     */
    @NotBlank(message = "应用开发者标识不能为空")
    private String devCode;

    @NotBlank(message = "相关参数")
    private String form_body;

    /**
     * 指定通道
     */
    @NotBlank(message = "acquire不能为空")
    private String acquire;


}
