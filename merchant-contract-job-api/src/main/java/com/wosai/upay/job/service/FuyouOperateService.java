package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * @Author: haochen
 * @date: 2024/05/27
 * @Description: 富友操作类
 */
@JsonRpcService("/rpc/fuyou")
@Validated
public interface FuyouOperateService {

    /**
     * 补充富友对公凭证信息
     *
     * @param merchantSn
     * @param picUrl
     */
    void syncFuyouPic(String merchantSn, String picUrl);


    /**
     * 更新富友二级机构号
     *
     * @param merchantSn
     * @param subInsCd
     */
    ContractResponse syncFuyouSubInsCd(String merchantSn, String subInsCd);


    /**
     * 查询商户信息
     *
     * @param merchantSn
     */
    Map queryMerchant(String merchantSn);

    /**
     * 富友特殊行业开关
     *
     * @return
     */
    boolean fuYouSpecialIndustryApplySwitch();

    /**
     * 富友进件支付宝
     *
     * @param merchantSn
     * @return
     */
    ContractResponse contractAlipay(String merchantSn);

    /**
     * 富友进件微信
     *
     * @param merchantSn
     * @return
     */
    ContractResponse contractWechat(String merchantSn);
}
