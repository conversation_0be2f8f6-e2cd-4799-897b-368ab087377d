package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.PropNotBothNull;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.CustomAppidApply;
import com.wosai.upay.job.model.WeixinParamsUpdateApply;
import com.wosai.upay.job.model.application.CommonResult;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * Created by lihebin on 2018/7/10.
 */
@JsonRpcService("/rpc/contractWeixin")
@Validated
public interface ContractWeixinService {


    /**
     * 更新任务
     * @param params
     * @return
     */
    boolean updateCustomAppidApply(@PropNotEmpty.List({
            @PropNotEmpty(value = DaoConstants.ID, message = "ID不能为空")
    })Map params);



    /**
     * 根据商户id获取商户渠道信息
     * @param params
     * @return
     */
    Map getMerchantChannelMessage(@PropNotEmpty.List({
            @PropNotEmpty(value = CommonModel.MERCHANT_ID, message = "商户ID不能为空")
    }) Map params);


    /**
     * 回切商户至普通通道
     * @return
     */
    Map rollbackMerchantWeixinConfig(@PropNotEmpty.List({
            @PropNotEmpty(value = CommonModel.MERCHANT_ID, message = "商户ID不能为空")
    }) Map params);

    /**
     * 查询任务表
     * @return
     */
    Map getCustomAppidApplyByParams(@PropNotEmpty.List({
            @PropNotEmpty(value = CommonModel.MERCHANT_ID, message = "商户Id不能为空"),
            @PropNotEmpty(value = CustomAppidApply.TYPE, message = "type不能为空")
    }) Map params);

    /**
     *查询任务表
     *     merchant_sn
     *     license_name
     *     channel_no
     *     parent_merchant_id
     *     appid
     *     type 0:自定义关注公众号 1:取消关注公众号
     *     status 报名状态 0:提交成功, 1:报备成功，2: 报备失败，3:配置成功,切agent成功 4:切agent失败 5:切agent失败
     * @return
     */
    Map getCustomAppidApply(String channelNo, String merchantSn, int type, String licenseName, String parentMerchantId, String payAppid, String appid, String miniAppid, int status);

    /**
     * 修改商户微信参数
     * merchant_sn
     * merchant_short_name
     * service_phone
     * @param params
     * @return
     */
    Map updateWeixinParams(
            @PropNotBothNull(value = WeixinParamsUpdateApply.MERCHANT_SN, value2 = WeixinParamsUpdateApply.MERCHANT_ID, message = "{value}商户号和商户id{value2}不能都为NULL")
             Map params);



    /**
     * 获取商户最后一次微信参数修改时间
     * @param params
     * @return
     */
    Map getWeixinUpdateTime(
            @PropNotBothNull(value = WeixinParamsUpdateApply.MERCHANT_SN, value2 = WeixinParamsUpdateApply.MERCHANT_ID, message = "{value}商户号和商户id{value2}不能都为NULL")
            Map params);



    /**
     * 打开 or 关闭 点金计划
     * @param subMchId 微信子商户号
     * @param open  true 打开 false 关闭
     * @return
     */
    CommonResult changeGoldByMchId(@NotEmpty(message = "微信子商户号不能为空") String subMchId, boolean open);

    /**
     * 打开 or 关闭 商家小票
     * @param subMchId 微信子商户号
     * @param open  true 打开 false 关闭
     * @return
     */
    CommonResult changeTicketByMchId(@NotEmpty(message = "微信子商户号不能为空") String subMchId, boolean open);

    /**
     * 打开 or 关闭 广告
     * @param subMchId 微信子商户号
     * @param open  true 打开 false 关闭
     * @return
     */
    CommonResult changeAdvertiseByMchId(@NotEmpty(message = "微信子商户号不能为空") String subMchId, boolean open);
}
