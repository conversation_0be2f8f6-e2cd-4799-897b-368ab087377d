package com.wosai.upay.job.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/9/3 14:23
 */
@Data
public class QueryAuthStatusExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "微信子商户号", width = 20)
    private String wxSubMchId;


    @Excel(name = "认证状态",replace = {"未认证_false", "已认证_true"}, width = 20)
    private Boolean result;

    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }
}
