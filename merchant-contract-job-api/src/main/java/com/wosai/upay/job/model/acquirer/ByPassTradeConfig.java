package com.wosai.upay.job.model.acquirer;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
@Accessors(chain = true)
public class ByPassTradeConfig {

    private Integer payway;

    private Integer provider;

    private String b2c_agent_name;

    private String c2b_agent_name;

    private String wap_agent_name;

    private String mini_agent_name;

    private String app_agent_name;

    private String h5_agent_name;

    private Map params;
}
