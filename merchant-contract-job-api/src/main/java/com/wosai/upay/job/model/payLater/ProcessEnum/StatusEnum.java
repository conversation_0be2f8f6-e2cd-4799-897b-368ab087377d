package com.wosai.upay.job.model.payLater.ProcessEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 子商户号用途枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    ZFT_APPLYING(0,"支付宝内部审核"),
    ZFT_FAIL(30,"支付宝内部审核失败"),
    ZHIMA_APPLYING(10,"芝麻先享平台审核"),
    ZHIMA_FAIL(40,"芝麻先享平台审核失败"),
    SUCCESS(20,"开通成功"),
    ;
    private int code;
    private String message;

    public static String getMessage(Integer code) {
        final StatusEnum[] ProcessStatusEnum = values();
        for (StatusEnum bankDirectApplyStatusEnum : ProcessStatusEnum) {
            if (Objects.equals(bankDirectApplyStatusEnum.getCode(), code)) {
                return bankDirectApplyStatusEnum.getMessage();
            }
        }
        return null;
    }
    public static void main(String[] args) {
        System.out.println(getMessage(80));
    }
}