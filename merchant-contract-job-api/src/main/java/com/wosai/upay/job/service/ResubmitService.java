package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/8 18:59
 * @Description:重复提交服务
 */
@JsonRpcService("/rpc/resubmit")
@Validated
public interface ResubmitService {

    /**
     * 重复提交 只限于处理失败的任务
     *
     * @param: taskId
     * @return:
     * @date: 19:04
     */
    Map reSubmitByTaskId(@NotNull(message = "taskId不能为null") long taskId);


    /**
     * @return
     * <AUTHOR>
     * @Description:编辑处理意见 需要在进件管理给风控使用
     * @time 1:48 下午
     **/
    Map editOpnion(@NotNull(message = "taskId不能为null") long taskId, @NotEmpty(message = "处理意见不能为空") String msg);
}
