package com.wosai.upay.job.model.dto.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.List;

/**
 * 收单组路由规则过滤Request DTO对象
 *
 * <AUTHOR>
 * @date 2024/3/28 13:58
 */
@Data
@ApiModel("收单组路由规则过滤Request DTO对象")
public class GroupRouteRuleFilterReqDTO {

    /**
     * 商户特征对象属性
     */
    @ApiModelProperty("商户特征对象属性")
    private List<MerchantObjectPropertyDTO> merchantObjectPropertyDTOList;

    /**
     * 进件规则决策分类
     */
    @ApiModelProperty("进件规则决策分类")
    private String classification;

    /**
     * 进件规则决策名称
     */
    @ApiModelProperty("进件规则决策名称")
    private String name;

    /**
     * 选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择,描述满足规则时，是选择,不可选择还是只可选择对应的group_id
     */
    @ApiModelProperty("选择类型 ENABLE-可选择 UNABLE-不可选择 ONLY_CAN-只可选择,描述满足规则时，是选择,不可选择还是只可选择对应的group_id")
    private String chooseType;

    /**
     * 有效状态 0-失效 1-生效
     */
    @ApiModelProperty("有效状态 0-失效 1-生效")
    private Integer validStatus;

    /**
     * 更新起始时间(YYYY-MM-DD)
     */
    @ApiModelProperty("YYYY-MM-DD")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp updateStartDate;

    /**
     * 更新结束时间(YYYY-MM-DD)
     */
    @ApiModelProperty("更新结束时间(YYYY-MM-DD)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp updateEndDate;

    /**
     * 创建起始时间(YYYY-MM-DD)
     */
    @ApiModelProperty("创建起始时间(YYYY-MM-DD)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp createStartDate;

    /**
     * 创建结束时间(YYYY-MM-DD)
     */
    @ApiModelProperty("创建结束时间(YYYY-MM-DD)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp createEndDate;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @JsonProperty("page")
    @ApiModelProperty("页码")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @JsonProperty("page_size")
    @ApiModelProperty("每页大小")
    private Integer pageSize;



}
