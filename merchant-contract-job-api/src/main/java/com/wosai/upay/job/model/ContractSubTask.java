package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.wosai.pantheon.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ContractSubTask {
    private Long id;

    private Long p_task_id;

    private String merchant_sn;

    private String channel;

    private Integer default_channel;

    private Integer change_config;

    private Integer change_body;

    private Integer task_type;

    private String contract_id;

    private Integer payway;

    private Integer schedule_status;

    private Long schedule_dep_task_id;

    private Integer status;

    private Integer status_influ_p_task;

    private Date priority;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String request_body;

    private String response_body;

    private String result;
    private String contract_rule;
    private Integer retry;
    private String rule_group_id;

    public Map getResponseBody() {
        if (StringUtil.isEmpty(response_body)) {
            return new HashMap();
        } else {
            return JSON.parseObject(response_body, Map.class);
        }
    }

    public Map getRequestBody() {
        if (StringUtil.isEmpty(request_body)) {
            return new HashMap();
        } else {
            return JSON.parseObject(request_body, Map.class);
        }
    }

}