package com.wosai.upay.job.model.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 报备通道规则 dto 对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("报备通道规则 dto 对象")
public class McRuleGroupRspDTO {

    @ApiModelProperty("主键id")
    private Integer id;
    /**
     * 唯一标识
     */
    @ApiModelProperty("唯一标识")
    private String groupId;
    /**
     * 规则组名称
     */
    @ApiModelProperty("规则组名称")
    private String name;
    /**
     * 业务方
     */
    @ApiModelProperty("业务方")
    private String vendor;
    /**
     * 业务方应用
     */
    @ApiModelProperty("业务方应用")
    private String vendorApp;
    /**
     * 0禁用  1启用
     */
    @ApiModelProperty("0禁用  1启用")
    private Integer status;

    /**
     * 收单机构
     */
    @ApiModelProperty("收单机构")
    private String acquirer;

}

