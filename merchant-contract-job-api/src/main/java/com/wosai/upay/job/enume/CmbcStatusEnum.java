  package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


  /**
   * @Description: 民生银行状态对应的描述
   * <AUTHOR>
   * @Date 2025/5/19 09:29
   */
  @Getter
  @AllArgsConstructor
  public enum CmbcStatusEnum {
      ACCEPTED("0", "已受理"),
      PROCESSING("1", "处理中"),
      COMPLETED("2", "处理完成"),
      CANCELLED("3", "作废")
              ;

      private String code;
      private String message;


      public static String getMessage(String code) {
          final CmbcStatusEnum[] descriptions = values();
          for (CmbcStatusEnum description : descriptions) {
              if (Objects.equals(description.getCode(), code)) {
                  return description.getMessage();
              }
          }
          return null;
      }

      public static void main(String[] args) {
          System.out.println(getMessage("2"));
      }
  }