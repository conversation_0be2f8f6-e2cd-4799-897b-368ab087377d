package com.wosai.upay.job.model.acquirer;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**切换收单机构
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChangeAcquirerRequest {

    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;

    @NotEmpty(message = "收单机构不能为空")
    private String acquirer;

    @NotNull(message = "是否立即切换收单机构不能为空")
    private Boolean immediately;

    @NotBlank(message = "业务方不能为空")
    private String tradeAppId;

    @NotNull(message = "是否可以取消申请不能为空")
    private Boolean cancellable;

}
