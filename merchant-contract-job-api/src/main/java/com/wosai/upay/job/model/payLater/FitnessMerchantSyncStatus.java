package com.wosai.upay.job.model.payLater;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@Accessors(chain = true)
public class FitnessMerchantSyncStatus {
    /**
     * 商户PID
     */
    private String merchantPid;

    /**
     * 商户状态
     * 正常 NORMAL，
     * 异常 ABNORMAL，
     * 暂停中 PAUSING, 
     * 已暂停 PAUSED，
     * 关闭中CLOSING, 
     * 已关闭 CLOSED, 
     * 恢复中 RECOVERING
     */
    // fine TODO 枚举
    private String merchantStatus;

    /**
     * 最新芝麻商户信息审核状态
     * 未补全 INCOMPLETE
     * 审核中 UNDER_REVIEW
     * 已通过 PASSED
     * 未通过 FAILED
     */
    private String recentZhimaReviewStatus;

    /**
     * 芝麻商户信息审核失败原因
     * 最新芝麻商户信息审核状态为FAILED才有
     */
    private String reviewFailReason;

    /**
     * 通知号
     */
    private String notifyId;

    /**
     * 业务时间 yyyy-MM-dd HH:mm:ss
     * 用于通知业务顺序判断
     * 解决：通知可能失败然后重试，可能造成业务通知顺序混乱的情况
     */
    private String bizTime;

    @JsonIgnore
    public boolean isUnderReview() {
        return "UNDER_REVIEW".equals(recentZhimaReviewStatus);
    }

    @JsonIgnore
    public boolean isReviewFail() {
        return "FAILED".equals(recentZhimaReviewStatus);
    }

    @JsonIgnore
    public boolean isReviewSuccess() {
        return Objects.equals(merchantStatus,"NORMAL") && Objects.equals(recentZhimaReviewStatus,"PASSED");
    }


}