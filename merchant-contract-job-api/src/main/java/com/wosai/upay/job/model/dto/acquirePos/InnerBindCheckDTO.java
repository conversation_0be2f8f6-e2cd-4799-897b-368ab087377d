package com.wosai.upay.job.model.dto.acquirePos;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/26 19:38
 */
@Data
@Accessors(chain = true)
public class InnerBindCheckDTO {

    @NotBlank(message = "vendor_app_appid不为空")
    private String vendorAppAppid;

    @NotBlank(message = "商户号不为空")
    private String merchantSn;

    /**
     * 选填,但是通联收银宝商户必传
     */
    private String storeSn;

}
