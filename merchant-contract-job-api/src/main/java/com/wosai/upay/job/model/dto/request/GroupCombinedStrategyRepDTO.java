package com.wosai.upay.job.model.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 进件报备规则组策略组合detail dto 对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("进件报备规则组策略组合细节")
public class GroupCombinedStrategyRepDTO {

    /**
     * 规则组id，对应mc_rule_group.group_id
     */
    @ApiModelProperty("规则组id")
    private String groupId;
    /**
     * 收单机构
     */
    @ApiModelProperty("收单机构")
    private String acquirer;
    /**
     * 报备规则组类型 1-主规则组 2-备用规则组
     */
    @ApiModelProperty("报备规则组类型 1-主规则组 2-备用规则组")
    private Integer groupType;
    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;
    /**
     * 每日限制额度
     */
    @ApiModelProperty("每日限制额度")
    private Integer dailyLimitQuota;
    /**
     * 当日已使用额度
     */
    @ApiModelProperty("当日已使用额度")
    private Integer dailyUsageQuota;
    /**
     * 负载比例
     */
    @ApiModelProperty("负载比例")
    private BigDecimal loadRatio;

}

