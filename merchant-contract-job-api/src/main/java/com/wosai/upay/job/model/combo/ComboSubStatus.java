package com.wosai.upay.job.model.combo;

/**
 * @Description:商户套餐二级支付方式枚举
 * <AUTHOR>
 * Date 2020/7/20 6:23 下午
 **/
public enum ComboSubStatus {

    NOT_USE(2),

    NOT_SUPPORT(0),

    USING(1);

    private int val;

    ComboSubStatus(int val) {
        this.val = val;
    }

    public static ComboSubStatus toStatus(Integer val) {
        if (val == null) {
            return null;
        }
        for (ComboSubStatus comboSubStatus : ComboSubStatus.values()) {
            if (comboSubStatus.val == val) {
                return comboSubStatus;
            }
        }
        return null;
    }

    public static boolean support(Integer val) {
        if (val == null) {
            return false;
        }
        return NOT_SUPPORT != toStatus(val);
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }
}
