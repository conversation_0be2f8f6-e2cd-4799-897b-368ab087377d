package com.wosai.upay.job.model.ccbConfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Data
public class QueryCcbChangeHistoryReq {

    private int page =  1;

    private int pageSize = 10;

    @JsonProperty("ccb_config_id")
    @NotNull(message = "建行配置id不能为空")
    private Long ccbConfigId;
}
