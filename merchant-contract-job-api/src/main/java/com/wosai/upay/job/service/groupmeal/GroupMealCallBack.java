package com.wosai.upay.job.service.groupmeal;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

@Validated
@JsonRpcService("/rpc/groupMeal")
public interface GroupMealCallBack {
    /**
     * 供contract回调
     *
     * @param changeOrderId 申请单id
     * @param shopId        阿里 shop ID
     * @return 校验是否确实为之前团餐发起的修改 ShopCategory 申请   true:是
     */
    boolean callBack(String changeOrderId, String shopId);


    /**
     * 团餐商户洗数据,更改ShopCategory,
     *
     * @param from 开始时间 (毫秒值)
     * @param to   结束时间
     * @return
     */
    String check(String from, String to);

}
