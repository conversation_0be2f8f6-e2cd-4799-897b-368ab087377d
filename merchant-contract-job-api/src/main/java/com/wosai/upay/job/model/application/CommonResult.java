package com.wosai.upay.job.model.application;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by hzq on 19/11/27.
 */
@Data
@Accessors(chain = true)
public class CommonResult {

    private int code;
    private String msg;
    private Object biz_response;

    public CommonResult(){}

    public CommonResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public CommonResult(int code, String msg, Object biz_response) {
        this.code = code;
        this.msg = msg;
        this.biz_response = biz_response;
    }


    public static final int SUCCESS = 200;//请求成功
    public static final int BIZ_FAIL = 400;//客户端异常
    public static final int ERROR = 500;//系统异常

    public boolean isSuccess() {
        return code >= SUCCESS && code < BIZ_FAIL;
    }

    public boolean isBusFail() {
        return code >= BIZ_FAIL && code < ERROR;
    }

    public boolean isSysFail() {
        return code >= ERROR;
    }


}
