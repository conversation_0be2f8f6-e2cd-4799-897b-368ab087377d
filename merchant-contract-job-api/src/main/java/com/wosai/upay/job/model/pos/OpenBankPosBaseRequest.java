package com.wosai.upay.job.model.pos;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: 开通刷卡参数
 * <AUTHOR>
 * @Date 2025/03/21 下午5:02
 */
@Data
public class OpenBankPosBaseRequest {
    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private String merchantId;

    /**
     * 应用开发者标识
     */
    @NotBlank(message = "应用开发者标识不能为空")
    private String devCode;


    /**
     * 费率信息
     */
    @NotBlank(message = "费率信息feeMap不能为空")
    private Map feeMap;

    /**
     * 套餐Id
     */
    @NotNull(message = "套餐Id不能为空")
    private Long tradeComboId;

   
}
