package com.wosai.upay.job.model.dto;

import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019-07-29
 */
@Data
@Accessors(chain = true)
public class WeixinSubAppidDto {
    /**
     * 支付 APPID
     */
    @NotEmpty(message = "sub_appid 不能为空")
    private String sub_appid;

    /**
     * 推荐关注APPID
     */
    private String subscribe_appid;

    /**
     * 1服务号  2小程序   3 非收钱吧配置，不知type
     */
    @NotNull(message = "type 不能为空")
    private Integer type;

    /**
     * 应用场景
     */
    private String app;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     * 0 未使用    1 正在使用
     */
    private Integer status;
}
