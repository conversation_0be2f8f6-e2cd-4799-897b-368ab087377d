package com.wosai.upay.job.model.direct;


/**
 * 对外部提供几种微信状态的code和message
 * <AUTHOR>
 * @date 2020/12/19
 */
public enum WeixinDirectStatusCode {

    /**
     * 无处理任务
     */
    NO_TASK("0000", "该商户无微信直连申请任务"),

    /**
     * 待处理
     */
    PENDING_TASK("1111", "申请任务待处理"),

    /**
     * 给个默认审核中的
     */
    WEIXIN_AUDITING("2222","微信审核中"),

    /**
     * 成功
     */
    WEIXIN_TASK_SUCCESS("8888", "微信直连任务处理成功"),

    /**
     * 未知失败原因
     */
    UNKNOWN_CODE("9999", "未知原因");


    private String code;
    private String msg;

    WeixinDirectStatusCode(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static WeixinDirectStatusCode toStatusCode(String code) {
        if (code == null) {
            return null;
        }
        for (WeixinDirectStatusCode statusCode : WeixinDirectStatusCode.values()) {
            if (statusCode.getCode().equals(code)) {
                return statusCode;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
