package com.wosai.upay.job.model.onlinePayment;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.validation.In;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
public class OnlinePaymentApplyInfoQueryReq {

    @NotBlank(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    @NotNull(message = "支付源错误")
    @In(values = {2, 3}, message = "支付源错误")
    private Integer payway = PaywayEnum.WEIXIN.getValue();
}
