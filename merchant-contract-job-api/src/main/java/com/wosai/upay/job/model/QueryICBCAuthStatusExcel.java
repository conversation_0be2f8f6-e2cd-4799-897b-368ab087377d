package com.wosai.upay.job.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

@Data
public class QueryICBCAuthStatusExcel implements IExcelModel, IExcelDataModel {


    @Excel(name = "微信子商户号", width = 20)
    private String wxSubMchId;

    @Excel(name = "微信认证状态",replace = {"未认证_false", "已认证_true"}, width = 20)
    private Boolean wxResult;

    @Excel(name = "微信错误信息", width = 20)
    private String wxErrorMsg;

    @Excel(name = "支付宝子商户号", width = 20)
    private String alipaySubMchId;


    @Excel(name = "支付宝认证状态",replace = {"未认证_false", "已认证_true"}, width = 20)
    private Boolean alipayResult;


    @Excel(name = "支付宝错误信息", width = 20)
    private String alipayErrorMsg;


    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }
}
