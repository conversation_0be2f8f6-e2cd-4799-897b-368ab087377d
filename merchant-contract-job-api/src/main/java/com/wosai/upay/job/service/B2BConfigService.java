package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import org.springframework.validation.annotation.Validated;

/**
 * @Author: haochen
 * @date: 2025/03/27
 * @Description: 新增b2b交易参数
 */
@JsonRpcService("/rpc/b2bWechat")
@Validated
public interface B2BConfigService {


    /**
     * 新增B2B交易参数
     * @param merchantSn
     * @param appid
     * @param payMerchantId
     * @param appKey
     * @param feeRate
     * @return
     */
    ContractResponse addB2BWechatConfig(String merchantSn, String appid, String payMerchantId, String appKey, String feeRate);

}
