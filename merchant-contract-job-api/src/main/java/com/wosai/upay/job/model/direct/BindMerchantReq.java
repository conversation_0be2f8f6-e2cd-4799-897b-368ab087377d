package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/12/20
 */
@Data
@Accessors(chain = true)
public class BindMerchantReq {

    @NotBlank(message = "商户号不能为空")
    private String merchant_sn;

    @NotBlank(message = "应用不能为空")
    private String dev_code;

    @NotBlank(message = "被绑定的商户号不能为空")
    private String bind_merchant_sn;

    private String remark;
}
