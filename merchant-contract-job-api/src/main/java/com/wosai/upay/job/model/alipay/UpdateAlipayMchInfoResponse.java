package com.wosai.upay.job.model.alipay;


import avro.shaded.com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 支付宝修改子商户信息返回结果
 *
 * <AUTHOR>
 * @date 2022-08-02
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAlipayMchInfoResponse {

    //返回code
    private int code;
    //返回message
    private String message;
    //报备支付源的请求参数
    private Map<String, Object> requestParam = Maps.newHashMap();
    //报备支付源的响应参数
    private Map<String, Object> responseParam = Maps.newHashMap();
    //报备支付源获取的交易参数
    private Map<String, Object> tradeParam = Maps.newHashMap();
    //merchant_provider_params表id
    private String merchantProviderParamsId;


    public boolean isSuccess() {
        return code >= 200 && code < 400;
    }

    //报警
    public boolean isBusinessFail() {
        return code >= 400 && code < 500;
    }

    //要重试
    public boolean isSystemFail() {
        return code == 500;
    }

}
