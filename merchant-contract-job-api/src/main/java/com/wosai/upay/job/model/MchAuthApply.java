package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wosai.pantheon.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.*;

@Data
@Accessors(chain = true)
public class MchAuthApply {
    private Long id;

    private String type;

    private String auth_num;

    private String merchant_sn;

    private Long task_id;

    private Integer status;

    private Integer cancel_status;

    private String request_body;

    private String response_body;

    private String qrcode_data_pre;

    private String result;

    private String qrcode_data_after;

    private String qr_code_data_channel;

    private Date create_at;

    private Date commit_at;

    private Date audit_at;

    private Date finish_at;

    private Date update_at;

    private String pay_merchant_id;

    private Date priority;

    /**
     * 强制线下
     */
    private Boolean forceAuth;

    /**
     * 是否过期：true
     */
    private Boolean expire;

    public Map<String, List<String>> getPayMerchantId() {
        if (StringUtil.isEmpty(pay_merchant_id)) {
            return new HashMap();
        } else {
            HashMap<String, List<String>> resultMap = new HashMap<>();
            Map map = JSON.parseObject(pay_merchant_id, Map.class);
            Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> next = iterator.next();
                List<String> strings = JSONArray.parseArray(JSON.toJSONString(next.getValue()), String.class);
                resultMap.put(next.getKey(), strings);
            }
            return resultMap;
        }
    }

    /**
     * 是否为小微商户
     *
     * @return
     */
    public Boolean isMicroApply() {
        return "0".equalsIgnoreCase(type);
    }

    /**
     * 是否为 个体/组织商户
     *
     * @return
     */
    public Boolean isCompanyApply() {
        return "1".equalsIgnoreCase(type) || "2".equalsIgnoreCase(type);
    }

    /**
     * 是否为 事业单位/公益组织等其他机构
     * @return
     */
    public Boolean isOrganizationApply() {
        return !"0".equalsIgnoreCase(type) && !"1".equalsIgnoreCase(type) && !"2".equalsIgnoreCase(type);
    }


}