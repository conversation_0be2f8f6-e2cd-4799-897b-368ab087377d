package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSONObject;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PaymentConfig {
    public static final Map<Integer, PaymentMethod> PAYMENT_METHOD_MAP;

    static {
        PAYMENT_METHOD_MAP = new HashMap<>();
        PAYMENT_METHOD_MAP.put(2, new PaymentMethod(2, "支付宝2.0", Arrays.asList(
                new PaymentMethod.SubPaymentMethod(1, "B扫C"),
                new PaymentMethod.SubPaymentMethod(2, "C扫B"),
                new PaymentMethod.SubPaymentMethod(3, "WAP支付"),
                new PaymentMethod.SubPaymentMethod(4, "小程序支付")
        )));
        PAYMENT_METHOD_MAP.put(3, new PaymentMethod(3, "微信", Arrays.asList(
                new PaymentMethod.SubPaymentMethod(1, "B扫C"),
                new PaymentMethod.SubPaymentMethod(2, "C扫B"),
                new PaymentMethod.SubPaymentMethod(3, "WAP支付"),
                new PaymentMethod.SubPaymentMethod(4, "小程序支付")
        )));
        PAYMENT_METHOD_MAP.put(17, new PaymentMethod(17, "云闪付2.0", Arrays.asList(
                new PaymentMethod.SubPaymentMethod(1, "B扫C"),
                new PaymentMethod.SubPaymentMethod(2, "C扫B"),
                new PaymentMethod.SubPaymentMethod(3, "WAP支付"),
                new PaymentMethod.SubPaymentMethod(4, "小程序支付")
        )));
    }
    // 示例方法：获取支付方式
    public static PaymentMethod getPaymentMethod(int payway) {
        return PAYMENT_METHOD_MAP.get(payway);
    }

    public static void main(String[] args) {
        // 测试代码
        System.out.println(JSONObject.toJSONString(PAYMENT_METHOD_MAP.get(2)));
    }
}