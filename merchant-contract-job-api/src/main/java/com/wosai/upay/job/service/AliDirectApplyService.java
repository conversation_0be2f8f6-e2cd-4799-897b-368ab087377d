package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.model.direct.AliDirectApplyDto;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
@JsonRpcService("/rpc/aliDirectApply")
@Validated
public interface AliDirectApplyService {

    /**
     * 提交支付宝直连申请
     * @param aliDirectReq 请求参数
     * @return 提交结果
     */
    ContractResponse applyAliDirectPay(@Valid AliDirectReq aliDirectReq);

    /**
     * 获取申请单状态 商户主动查询 给外部使用，有频率限制
     * @param merchantSn 商户号
     * @param platform 查询平台
     * @return 状态和文案
     */
    ApplyStatusResp queryApplyStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                                     @NotBlank(message = "平台不能为空") String platform);

    /**
     * 获取申请单的状态和文案 给内部使用
     * @param merchantSn 商户号
     * @param platform 平台
     * @return 状态和文案
     */
    ApplyStatusResp getApplyStatusByType(@NotBlank(message = "商户号不能为空") String merchantSn,
                                         @NotBlank(message = "平台不能为空") String platform);

    /**
     * 支付宝回调授权商户
     * @param aliAuthorizeCallBack 回调参数
     */
    void authorizeMerchant(AliAuthorizeCallBack aliAuthorizeCallBack);

    /**
     * 根据taskId获取申请单
     * @param taskId 报备task的id
     * @return 返回值
     */
    AliDirectApplyDto getDirectApplyByTaskId(@NotNull(message = "taskId不能为空") long taskId);
}
