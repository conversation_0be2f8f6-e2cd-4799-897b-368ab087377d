package com.wosai.upay.job.model.acquirer;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
@Accessors(chain = true)
public class MerchantTradeConfig extends ByPassTradeConfig{

    private Integer b2c_formal;

    private Integer c2b_formal;

    private Integer wap_formal;

    private Integer mini_formal;

    private Integer app_formal;

    private Integer extend2_formal;

}
