package com.wosai.upay.job.model.dto.acquirePos;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 终端解绑
 * <AUTHOR>
 * @Date 2024/2/26 19:38
 */
@Data
@Accessors(chain = true)
public class UnbindDTO {

    @NotBlank(message = "设备指纹不为空")
    private String deviceFingerprint;

    @NotBlank(message = "终端Id不为空")
    private String terminalId;

    @NotBlank(message = "商户Id不为空")
    private String merchantId;


}
