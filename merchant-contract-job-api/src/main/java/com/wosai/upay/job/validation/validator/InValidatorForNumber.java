package com.wosai.upay.job.validation.validator;

import com.wosai.upay.job.validation.In;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2023/2/9 Time: 2:02 PM
 */
public class InValidatorForNumber implements ConstraintValidator<In, Number> {
    private final Set<String> values = new HashSet<>();
    @Override
    public void initialize(In constraintAnnotation) {
        for (long value : constraintAnnotation.values()) {
            values.add(Long.toString(value));
        }
    }

    @Override
    public boolean isValid(Number value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }

        return values.contains(value.toString());
    }
}
