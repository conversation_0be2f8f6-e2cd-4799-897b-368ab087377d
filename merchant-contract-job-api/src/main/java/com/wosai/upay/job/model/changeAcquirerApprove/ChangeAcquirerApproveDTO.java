package com.wosai.upay.job.model.changeAcquirerApprove;

import com.shouqianba.workflow.bean.CallBackBean;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 通过审批切换收单机构
 * <AUTHOR>
 * @Date 2021/9/2 14:18
 */
@Data
@Accessors(chain = true)
@Validated
public class ChangeAcquirerApproveDTO {

    /**
     * 单个申请的时候有值
     */
    private String merchantSn;

    /**
     * 申请原因 当申请类型是单个申请的时候使用此原因记录日志
     */
    private String reason;

    /**
     *批量申请传的附件地址
     */
    private List<String> attachmentUrls;

    /**
     * 审批模板,编号信息
     */
    private CallBackBean callBackBean;

    /**
     * 申请类型 batch-批量 , single-单个
     */
    @NotBlank(message = "申请类型不为空")
    private String applyType;

    /**
     * 目标收单机构
     */
    @NotBlank(message = "目标收单机构不为空")
    private String target;

    /**
     * 审批编号(用于日志记录以及关联切换收单机构数据)
     */
    @NotBlank(message = "审批编号不为空")
    private String auditSn;


    /**
     * 审批发起人名称
     */
    private String operatorName;

    /**
     * 审批发起人id
     */
    private String operator;

    /**
     *是否立刻生效
     */
    @NotNull
    private Boolean immediate;

    /**
     * 审批需要指定业务方
     */
    @NotBlank(message = "业务方不能为空")
    private String tradeAppId;


}
