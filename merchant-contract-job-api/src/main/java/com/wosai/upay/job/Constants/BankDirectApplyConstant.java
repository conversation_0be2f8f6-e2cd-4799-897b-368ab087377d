package com.wosai.upay.job.Constants;

/**
 * @Description: 银行直连申请单表相关常量
 * <AUTHOR>
 * @Date 2021/4/25 09:13
 */
public class BankDirectApplyConstant {

    /**
     * 状态常量
     */
    public static class Status {
        /**
         * 已提交
         */
        public static final Integer PENDING = 0;

        /**
         * 申请中
         */
        public static final Integer APPLYING = 10;

        /**
         * 申请成功
         */
        public static final Integer SUCCESS = 20;

        /**
         *申请失败
         */
        public static final Integer FAIL = 30;
    }

    /**
     * 过程状态常量
     */
    public static class ProcessStatus {
        /**
         * 未处理
         */
        public static final Integer PENDING = 0;

        /**
         * 进件中
         */
        public static final Integer CONTRACT_APPLYING = 10;

        /**
         * 进件成功
         */
        public static final Integer CONTRACT_SUCCESS = 20;

        /**
         * 微信商家认证成功
         */
        public static final Integer WX_AUTH_SUCCESS = 30;

        /**
         * 发起切换收单机构
         */
        public static final Integer APPLYING_CHANGE_ACQUIRE = 40;


        /**
         *申请成功
         */
        public static final Integer SUCCESS = 50;

        /**
         *申请失败
         */
        public static final Integer FAIL = 99;
    }



    /**
     * 申请中
     */
    public static final Integer APPLYING = 1;

    /**
     * 申请成功
     */
    public static final Integer SUCCESS = 2;

    /**
     *申请失败
     */
    public static final Integer FAIL = 3;


    /**
     * 申请中
     */
    public static final String APPLYING_MEMO = "申请中";

    /**
     * 申请成功
     */
    public static final String SUCCESS_MEMO = "申请成功";


    /**
     * 微信超时未实名
     */
    public static final String AUTH_TIME_OUT_MEMO = "未在有效期内完成微信商家认证";



    /**
     * DirectStatus表状态
     */
    public static class DirectStatus {
        /**
         * 待处理
         */
        public static final Integer PENDING = 0;

        /**
         * 申请中
         */
        public static final Integer PROCESSING = 1;

        /**
         * 处理成功
         */
        public static final Integer SUCCESS = 2;

        /**
         *处理失败
         */
        public static final Integer FAIL = 3;
    }


    /**
     * Extra字段的key值,仅限参考后续拓展以实际代码为准
     */
    public static class Extra {
        /**
         * 当前acquire
         */
        public static final String ACQUIRE = "acquire";

        /**
         * 当前收单机构 
         */
        public static final String PROVIDER = "provider";
        /**
         * process_status状态变更过程
         */
        public static final String PROCESS = "process";

        /**
         *用于crm_app银行业务开通过程视展示
         */
        public static final String VIEW_PROCESS = "view_process";

        /**
         * 数据来源,目前仅有建行批量导入
         */
        public static final String DATA_FROM = "from";

        /**
         * 换卡任务主键id
         */
        public static final String SYNC_BANK_ACCOUNT_TASK_ID = "task_id";
        /**
         * 是否校验微信实名认证状态
         */
        public static final String CHECK_WEIXIN_AUTH = "checkWeixinAuth";
        /**
         * 是否校验支付宝实名认证状态
         */
        public static final String CHECK_ALI_AUTH = "checkAliAuth";
        /**
         * 是否立即切换，不等到第二天凌晨
         */
        public static final String CHANGE_IMMEDIATELY = "change_immediately";
    }

    public static final String BANK_PRE_ID = "bank_pre_id";

    public static final String CRM_UESRID = "crm_uesrId";



}
