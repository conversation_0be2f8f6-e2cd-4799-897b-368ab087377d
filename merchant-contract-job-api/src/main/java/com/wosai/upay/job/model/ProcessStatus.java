package com.wosai.upay.job.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 记录邮储任务状态和变更时间
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessStatus {

    private Integer processStatus;

    private Long updateAt;

    private String desc;
}