package com.wosai.upay.job.enume.microUpgrade;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * @Description: 小微升级版本枚举
 * <AUTHOR>
 * @Date 2025/6/26 09:39
 */
public enum UpgradeVersion implements ITextValueEnum<String> {
    V2("V2","小微升级V2版本"),
    V3("V3","小微升级V3版本");

    private final String value;
    private final String text;

    UpgradeVersion(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getValue() {
        return "";
    }

    @Override
    public String getText() {
        return "";
    }
}
