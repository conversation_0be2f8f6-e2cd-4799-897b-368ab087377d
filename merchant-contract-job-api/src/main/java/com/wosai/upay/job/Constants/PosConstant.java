package com.wosai.upay.job.Constants;

/**
 * @Description: 收单机构刷卡常量
 * <AUTHOR>
 * @Date 2024/2/26 18:16
 */
public class PosConstant {
    public static final String UNKOWNER_VENDER_APP_ID = "未知设备类型";
    public static final String FAIL_SUPPORT = "激活失败,暂不支持绑定POS设备,请联系您的客户经理处理";
    public static final String EXIST_OTHER_POS = "该商户已有其他通道的POS终端,请解绑后重试!";
    public static final String FAIL_OTHER_ACQUIRE = "激活失败,POS通道不符,请联系您的客户经理处理";

    public static final String NET_IN_FAIL = "当前商户收单机构不在%s，暂时无法开通%s一体化刷卡业务，请切换收单机构后重试！";

    public static final String CAN_NOT_ACTIVE = "当前设备无法激活银行卡功能(收单机构不符),请联系您的专属客户经理";
    public static final String APPLY_OPEN = "请联系您的专属客户经理,开通银行卡业务";
    public static final String ACTIVE_NO_FAIL = "获取激活码失败,请联系您的专属客户经理";
    public static final String UNION_PAY_OPEN_FAIL = "银联商户报备失败，无法开通%s一体化刷卡业务，%s";

    public static final String SYB_NOT_ACTIVE = "收银宝审核中,请3分钟后重试";






}
