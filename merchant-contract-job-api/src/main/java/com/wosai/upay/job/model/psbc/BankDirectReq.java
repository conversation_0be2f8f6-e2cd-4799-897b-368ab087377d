package com.wosai.upay.job.model.psbc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;

/**
 * @Description: 开通银行直连请求参数
 * <AUTHOR>
 * @Date 2021/4/7 09:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankDirectReq {
    @NotBlank(message = "商户号不能为空")
    private String merchant_sn;

    @NotBlank(message = "应用标识不能为空")
    private String dev_code;

    @NotBlank(message = "相关参数")
    private String form_body;


}
