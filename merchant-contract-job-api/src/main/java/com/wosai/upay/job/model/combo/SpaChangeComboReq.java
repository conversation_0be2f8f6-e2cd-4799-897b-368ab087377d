package com.wosai.upay.job.model.combo;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;

import java.util.List;

/**
 * @Description:商户套餐套餐切换参数 因为需要兼容一段时间 过渡 暂时这个接口对象需要保留（设为默认逻辑兼容）
 * <AUTHOR>
 * Date 2020/6/8 10:02 上午
 **/
@Data
@Accessors(chain = true)
@Deprecated
public class SpaChangeComboReq {

    @NotEmpty(message = "子商户号交易参数id不能为空")
    /**
     * 交易参数ID
     **/
    private String paramId;
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;
    /**
     * 套餐明细ID
     **/
    private Long comboDetailId;
    /**
     * 商户套餐记录ID
     **/
    private Long merchantComboId;

    /**
     * 套餐需要应用的二级支付方式
     **/
    private List<String> subColumns;

    /**
     * 费率
     **/
    private String fee;
}
