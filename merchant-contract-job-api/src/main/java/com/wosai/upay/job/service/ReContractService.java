package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.AllowChangeIndustryRequest;
import com.wosai.upay.job.model.AllowChangeIndustryResult;
import com.wosai.upay.job.model.ReContract;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/8/8 14:55
 * @Description:重新报备服务
 */
@JsonRpcService("/rpc/reContract")
@Validated
public interface ReContractService {

    String RECONTRACT = "reContract";

    /**
     * *
     * 报备类型 该字段放在报备上下文中，标志该报备任务是否支持强制重新报备 contract服务通过该字段做接口幂等校验
     * "1"
     */
    String TYPE = "type";


    String REMARK = "remark";

    /**
     * 重新报备一个子商户号
     *
     * @param: reContract
     * @return:
     * @date: 10:28
     */
    Map reContract(@Valid ReContract reContract);

    /**
     * 商户在普通渠道报备一个子商户号，并且设置为默认
     *
     * @param merchantSn
     * @param remark
     */
    void reContractInNormalChannel(String merchantSn, String remark);

    /**
     * 商户在普通渠道报备一个子商户号，并且设置为默认
     *
     * @param merchantSn
     * @param remark
     */
    void reContractInNormalChannelV2(String merchantSn, String remark, boolean forceMicro);

    /**
     * 判断是否允许更新到目标行业
     * @param request 请求参数
     * @return 是否允许切换到目标行业
     */
    AllowChangeIndustryResult allowChangeIndustry(@Valid AllowChangeIndustryRequest request);

}
