package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/12/19
 */
@Data
@Accessors(chain = true)
public class ApplyStatusResp {

    /**
     * 开通状态 1开通中 2成功 3失败
     */
    private Integer status;

    private String contract_memo;

    private String contract_code;

    /**
     * 待签约或待账户验证的二维码
     */
    private String qrcode;

}
