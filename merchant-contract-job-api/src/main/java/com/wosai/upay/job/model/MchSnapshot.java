package com.wosai.upay.job.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MchSnapshot {

    /**
     * 向商户发起代运营操作返回支付宝操作批次号
     */
    private String batchNo;

    /**
     * 向商户发起代运营操作代运营操作类型
     */
    private String operateType;

    /**
     * 阿里行业
     */
    private String aliMcc;

    /**
     * 直连还是间连 true直连,false间连
     */
    private Boolean direct;

    /**
     * 门店账号
     */
    private String shopId;

    /**
     * 商户升级M4绑定时获取的bind_user_id, 间连商户作为pid 使用
     */
    private String bindUserId;

    /**
     * 新蓝海申请单状态，状态机参考（FAIL:报名失败，PASS:报名成功)
     */
    private String activityStatus;

    /**
     * 新蓝海/快消 活动申请 营业执照位置（优先取营业执照，若无取结算人身份证照片）
     */
    private String business_lic;

    /**
     * 新蓝海/快消 活动申请 门头照
     */
    private String shop_entrance_pic;

    /**
     * 新蓝海/快消 活动申请 内景照
     */
    private String indoor_pic;

    /**
     * 快消活动 特有 烟草许可证
     */
    private String tobacco_pic;

    /**
     * 新蓝海活动 特有 生活百货许可证
     */
    private String shop_url;

    /**
     * 新蓝海活动创建门店时的省Code
     */
    private String provinceCode;

    /**
     * 新蓝海活动申请市Code
     */
    private String cityCode;

    /**
     * 新蓝海活动申请区code
     */
    private String districtCode;

    /**
     * 新蓝海活动申请详细地址
     */
    private String address;

    /**
     * pc终端
     */
    private List<String> pcTerminals;
    /**
     * 审批点击 前置工作未完成
     * 前置工作完成后后续处理标志 填写1
     */
    private String holdHandle;
    /**
     * 字段有值表示线上报名
     * 没有值线下报名
     * 兼容 线上报名上线时 线下报名的任务未完成
     * 填写1
     */
    private String online;
    /**
     * 升级M3，更新MCC码 收单机构更新失败次数
     */
    private Integer updateFail;
    /**
     * 支付宝学校内标
     * 2088000151541822
     */
    private String instId;

    /**
     * 支付宝学校名称
     */
    private String instName;
}
