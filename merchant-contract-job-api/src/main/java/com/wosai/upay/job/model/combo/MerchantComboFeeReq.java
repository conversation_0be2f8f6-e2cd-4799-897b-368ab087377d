package com.wosai.upay.job.model.combo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: 切换费率用
 * <AUTHOR>
 * @Date 2020/9/2 5:11 下午
 **/
@Data
@Accessors(chain = true)
public class MerchantComboFeeReq {

    @NotNull(message = "商户套餐ID不能为空")
    @JsonProperty("merchant_combo_id")
    private Long merchantComboId;

    @NotNull(message = "二级支付方式费率不能为空")
    @JsonProperty("sub_fees")
    private Map subFees;

    @NotEmpty(message = "备注不能为空")
    private String remark;

}
