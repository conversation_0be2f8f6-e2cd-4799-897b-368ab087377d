package com.wosai.upay.job.model.direct;


/**
 * 对外部提供几种支付宝状态的code和message
 * <AUTHOR>
 * @date 2020/12/19
 */
public enum AliDirectStatusCode {

    /**
     * 无处理任务
     */
    NO_TASK("0000", "该商户无支付宝直连申请任务"),

    /**
     * 待处理
     */
    PENDING_TASK("1111", "支付宝直连申请任务待处理"),

    /**
     * 默认审核中的code和文案
     */
    ALI_AUDITING("2222", "支付宝审核中"),

    /**
     * 成功
     */
    ALI_TASK_SUCCESS("8888", "支付宝直连任务处理成功"),
    /**
     * 未知失败原因
     */
    UNKNOWN_CODE("9999", "未知原因");


    private String code;
    private String msg;

    AliDirectStatusCode(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static AliDirectStatusCode toStatusCode(String code) {
        if (code == null) {
            return null;
        }
        for (AliDirectStatusCode statusCode : AliDirectStatusCode.values()) {
            if (statusCode.getCode().equals(code)) {
                return statusCode;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
