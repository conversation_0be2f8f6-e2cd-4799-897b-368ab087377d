package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * 微信商家认证信息
 * <AUTHOR>
 * @date 2021-07-30
 */
@Data
@Accessors(chain = true)
public class MchAuthInfo extends MchAuthApply {

    public MchAuthInfo() {
    }

    public MchAuthInfo(MchAuthApply authApply) {
        BeanUtils.copyProperties(authApply, this);
    }

    /**
     * 微信商家认证任务Id
     */
    private Long taskId;

    /**
     * 任务关联待授权微信子商户号
     */
    private String taskPayMerchantId;

    /**
     * 商户号
     */
    private String merchantSn;

}
