package com.wosai.upay.job.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 进件报备规则组策略组合 dto 对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("进件报备规则组策略组合 dto 对象")
public class GroupCombinedStrategyRspDTO {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 策略类型 1-一主无备 2-一主一备 3-一主多备 4-一主一备+每日额度 5-负载比例
     */
    @ApiModelProperty("策略类型 1-一主无备 2-一主一备 3-一主多备 4-一主一备+每日额度 5-负载比例")
    private Integer strategyType;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 有效状态 0-失效 1-生效
     */
    @ApiModelProperty("有效状态 0-失效 1-生效")
    private Integer validStatus;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;


}

