package com.wosai.upay.job.model.onlinePayment;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OnlinePaymentApplyInfoQueryResp {

    /**
     * 开通状态 1开通中  2开通成功  3开通失败
     */
    private Integer status;

    /**
     * 具体开通状态
     * <p>0： 待处理
     * <p>11：报备成功
     * <p>12：待子商户号授权
     * <p>21：报备失败
     * <p>22：提交微信授权任务失败
     * <p>23：查询微信子商户号授权状态失败
     * <p>24：交易参数切换失败
     */
    private Integer processStatus;

    /**
     * 开通失败时的标题
     */
    private String failTitle;
    /**
     * 开通失败时的文案
     */
    private String failMessage;

    private String applyId;

    /**
     * 记录的线上收款开通流程
     */
    private List<OnlinePaymentApplyProcess> process;
}
