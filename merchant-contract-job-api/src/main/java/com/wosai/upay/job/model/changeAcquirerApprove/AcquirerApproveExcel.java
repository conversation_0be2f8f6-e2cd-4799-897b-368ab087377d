package com.wosai.upay.job.model.changeAcquirerApprove;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/9/3 14:23
 */
@Data
public class AcquirerApproveExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "收钱吧商户号", isImportField = "true", width = 20)
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;

    @Excel(name = "申请原因", isImportField = "true", width = 20)
    @NotEmpty(message = "申请原因不能为空")
    private String remark;

    @Excel(name = "执行结果")
    private String result;

    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }
}
