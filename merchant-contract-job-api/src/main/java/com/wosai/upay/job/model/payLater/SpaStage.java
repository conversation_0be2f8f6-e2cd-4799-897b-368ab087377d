package com.wosai.upay.job.model.payLater;

import com.wosai.upay.job.model.payLater.ProcessEnum.SpaStageStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description: spa流程展示
 * <AUTHOR>
 * @Date 2023/8/4 下午5:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaStage {
    /**
     * @see SpaStageStatusEnum
     */

    private Integer status;

    /**
     * 时间
     */
    private Date updateAt;
    /**
     * 描述
     */
    private String desc;
    /**
     * 备注
     */
    private String remark;

}
