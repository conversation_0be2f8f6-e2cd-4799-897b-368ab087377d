package com.wosai.upay.job.model.direct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/20
 */
@Data
@Accessors(chain = true)
public class ChangeApplyStatusReq {

    @NotBlank(message = "商户号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;

    @NotBlank(message = "应用标识不能为空")
    @JsonProperty("dev_code")
    private String devCode;

    @NotNull(message = "变更的状态不能为空")
    private Integer status;

    @JsonProperty("sub_merchant_sn")
    private String subMerchantSn;
    /**
     * 驳回时候的信息
     */
    private String message;
}
