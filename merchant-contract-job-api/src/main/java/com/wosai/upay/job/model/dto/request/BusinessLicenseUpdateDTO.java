package com.wosai.upay.job.model.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 营业执照更新请求DTO
 *
 * <AUTHOR>
 * @date 2024/9/5 17:15
 */
@Data
public class BusinessLicenseUpdateDTO {

    public static  final  Integer NEED_NOTIFY_RISK_RESULT = 1;

    public static  final  Integer NOT_NEED_NOTIFY_RISK_RESULT = 2;

    @JsonProperty("id")
    private String id;

    @JsonProperty("auditor_name")
    private String auditorName;

    @JsonProperty("audit_platform")
    private String auditPlatform;

    @JsonProperty("auditor")
    private String auditor;

    @JsonProperty("audit_id")
    private String auditId;

    @JsonProperty("msp_account_id")
    private String mspAccountId;

    @JsonProperty("license_address")
    private String licenseAddress;

    @JsonProperty("cash_store_id")
    private String cashStoreId;

    @JsonProperty("merchant_id")
    private String merchantId;

    @JsonProperty("number")
    private String number;

    @JsonProperty("merchant_user_id")
    private String merchantUserId;

    @JsonProperty("id_type")
    private Integer idType;

    @JsonProperty("business_license_status")
    private Integer businessLicenseStatus;

    @JsonProperty("legal_person_id_card_back_photo")
    private String legalPersonIdCardBackPhoto;

    @JsonProperty("legal_person_id_card_front_photo")
    private String legalPersonIdCardFrontPhoto;

    @JsonProperty("legal_person_id_card_address")
    private String legalPersonIdCardAddress;

    @JsonProperty("legal_person_id_card_issuing_authority")
    private String legalPersonIdCardIssuingAuthority;

    @JsonProperty("store_id")
    private String storeId;

    @JsonProperty("letter_of_authorization")
    private String letterOfAuthorization;

    @JsonProperty("proof_assistant_photos")
    private List<String> proofAssistantPhotos;

    @JsonProperty("license_type")
    private Integer licenseType;

    @JsonProperty("photo")
    private String photo;

    @JsonProperty("legal_person_id_number")
    private String legalPersonIdNumber;

    @JsonProperty("account_id")
    private String accountId;

    @JsonProperty("trade_license")
    private String tradeLicense;

    @JsonProperty("legal_person_name")
    private String legalPersonName;

    @JsonProperty("name")
    private String name;

    @JsonProperty("validity")
    private String validity;

    @JsonProperty("id_validity")
    private String idValidity;

    @JsonProperty("submitor")
    private String submitor;

    @JsonProperty("submit_platform")
    private String submitPlatform;

    @JsonProperty("submitor_name")
    private String submitorName;

    @JsonProperty("apply_remark")
    private String applyRemark;

    private Integer needNotifyRiskResultStatus;

    /**
     * 是否需要通知风控结果
     *
     * @return 是否需要通知风控结果 true-需要
     */
    public boolean needToNotifyRiskResult() {
        if (Objects.isNull(needNotifyRiskResultStatus)) {
            return true;
        }
        return NEED_NOTIFY_RISK_RESULT.equals(needNotifyRiskResultStatus);
    }
}

