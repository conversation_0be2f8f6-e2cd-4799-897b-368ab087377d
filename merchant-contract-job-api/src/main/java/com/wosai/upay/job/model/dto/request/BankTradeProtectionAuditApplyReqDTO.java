package com.wosai.upay.job.model.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 银行保障交易审核申请BO
 *
 * <AUTHOR>
 * @date 2024/8/15 10:05
 */
@Data
public class BankTradeProtectionAuditApplyReqDTO {

    public static final String BANK_TRADE_PROTECTION_AUDIT_APPLY_EVENT_KEY = "bankTradeProtectionAuditEvent";

    public static final String APPLY_TYPE_SINGLE = "single";

    public static final String APPLY_TYPE_MULTI = "multi";

    public static final String TRADE_PROTECTION_TYPE_CLOSE = "close";

    public static final String TRADE_PROTECTION_TYPE_OPEN = "open";

    /**
     * 申请类型 single-单商户申请，multi-多商户申请
     */
    private String applyType;

    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 商户号文件oss地址列表
     * 文件类型：excel
     */
    @JSONField(name = "merchantSnFile")
    private List<String> merchantSnFileOssUrls;

    /**
     * 交易保障开关类型 close-关闭保障 open-恢复保障
     */
    private String tradeProtectionType;

    /**
     * 审批单id 用于回溯
     */
    private String auditId;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 申请原因
     */
    private String applyReason;
}
