package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ForeignCard;
import com.wosai.upay.job.model.PreAuthApply;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.lklV3Pos.ApplyForeignCardRequest;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.model.lklV3Pos.LklV3MerchantResponse;
import com.wosai.upay.merchant.contract.model.LimitResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Description: LKlV3pos
 * <AUTHOR>
 * @Date 2022/7/26 09:52
 */
@JsonRpcService("/rpc/v3_pos")
@Validated
public interface LKlV3PosService {

    /**
     * 上传相应资料开通pos服务
     *
     * @param request
     * @return
     */
    ContractResponse applyPos(ApplyPosRequest request);


    /**
     * 查询lkl商户信息
     *
     * @param merchantSn
     * @return
     */
    LklV3MerchantResponse getLklMerchant(@NotBlank(message = "merchantSn不能为空") String merchantSn);

    /**
     * 查询拉卡拉限额信息
     * @param merchantSn
     * @return
     */
    List<LimitResp> queryLimit(@NotBlank(message = "merchantSn不能为空") String merchantSn);

    /**
     * 申请开通外卡
     *
     * @param request
     * @return
     */
    String applyForeignCard(ApplyForeignCardRequest request);



    /**
     * 获取外卡信息
     * @param merchantSn
     * @return
     */
    ForeignCard getForeignCard(@NotBlank(message = "商户号不能为空")String merchantSn);

    /**
     * 获取预授权信息
     * @param merchantSn
     * @return
     */
    PreAuthApply getPreAuthApply(@NotBlank(message = "商户号不能为空")String merchantSn);


    /**
     * 申请开通预授权
     *
     * @param merchantId
     * @return
     */
    String applyPreAuth(@NotBlank(message = "商户Id不能为空")String merchantId);

    /**
     * 查询外卡合同状态
     * @param merchantId
     * @return
     */
    CommonResult queryForeignCardContract(@NotBlank(message = "商户Id不能为空")String merchantId);

    /**
     * 查询预授权合同
     * @param merchantId
     * @return
     */
    CommonResult queryPreAuthContract(@NotBlank(message = "商户Id不能为空")String merchantId);



    public void openForeignCard(@NotBlank(message = "商户Id不能为空")String merchantId);

    /**
     * 将手机POS开通成功的商户导入到crm
     */
    public void initCrmRecord();
}
