package com.wosai.upay.job.service.callback;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTaskAndParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HXParam;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 */
@JsonRpcService("/rpc/HxbCallBack")
@Validated
public interface HxbCallBackService {

    /**
     * 查询任务信息 包括收单机构参数
     *
     * @param contractId
     * @return
     */
    ContractSubTaskAndParam getContractSubTaskAndParam(@NotEmpty(message = "contractId不能为空") String contractId);

    /**
     * 华夏任务回调处理
     *
     * @param contractId
     * @param contractResponse
     * @return
     */
    boolean hxbCallBackHandle(@NotEmpty(message = "contractId不能为空") String contractId, @NotNull(message = "返回信息不能为空") ContractResponse contractResponse);

    /**
     * 获取华夏默认配置
     *
     * @return
     */
    HXParam getDefaultParam();
}

