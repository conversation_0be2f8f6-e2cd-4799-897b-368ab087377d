package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.model.dto.EcApplyPushDTO;
import com.wosai.upay.job.model.dto.acquirePos.LklEcApplyDTO;
import com.wosai.upay.job.model.lklV3Pos.LKlEcApplyStatusResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;


/**
 * 签约申请记录表Service层
 *
 * <AUTHOR>
 */
@JsonRpcService("/rpc/ec_apply")
@Validated
public interface LklEcApplyService {

    /**
     * 当前商户电子合同流程状态
     * @param merchantSn 商户号
     * @param devCode 开发标识
     * @return
     */
     LKlEcApplyStatusResp getLklEcApplyStatus(@NotBlank(message = "merchantSn不能为空")String merchantSn,
                                              @NotBlank(message = "devCode不能为空") String devCode) throws CommonPubBizException;

    /**
     * 拉卡拉电子合同申请记录,只有在报错三四要素认证不通过时才会调用这个接口
     * @param dto 申请记录
     */
      void saveManualAuditingEcApply(@Valid LklEcApplyDTO dto);

    /**
     * 接收拉卡拉回调更新审核中的电子合同申请记录
     * @param dto 回调信息
     */
    Map<String,String> updateManualAuditingEcApply(EcApplyPushDTO  dto);

    /**
     * 更新EC申请单的状态
     * @param id           主键id
     * @param status       新的状态
     * @param detailStatus 新的详细状态
     * @param result       新的结果描述
     */
    void updateEcApplyStatusByPrimaryId(Long id,Integer status, Integer detailStatus, String result);

    /**
     * 更新EC申请单的状态
     * @param applyId       申请单Id
     * @param status       新的状态
     * @param detailStatus 新的详细状态
     * @param result       新的结果描述
     */
    void updateEcApplyStatusByApplyId(String applyId,Integer status, Integer detailStatus, String result);




    /**
     * 更新EC申请单,只有当状态是失败的时候才会删除
     * @param merchantSn       申请单Id
     * @param devCode       新的状态
     */
    void deleteLklEcApply(@NotBlank(message = "merchantSn不能为空")String merchantSn,
                          @NotBlank(message = "devCode不能为空") String devCode);





    /**
     * 取消待办
     * @param merchantSn       商户号
     * @param devCode       业务标识

     */
    void cancelByMerchantSn(String merchantSn,String devCode);

}
