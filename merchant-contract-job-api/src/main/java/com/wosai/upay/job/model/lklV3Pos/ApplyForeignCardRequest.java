package com.wosai.upay.job.model.lklV3Pos;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: 开通外卡申请参数
 * <AUTHOR>
 * @Date 2023/8/21 下午5:02
 */
@Data
public class ApplyForeignCardRequest {
    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private String merchantId;
    /**
     * 费率信息
     */
    @NotEmpty(message = "费率信息feeMap不能为空")
    private Map feeMap;

    /**
     * 套餐Id
     */
    @NotNull(message = "套餐Id不能为空")
    private Long tradeComboId;


    /**
     * 签约手机号不能为空
     */
    @NotBlank(message = "签约手机号不能为空")
    private String signMobile;
}
