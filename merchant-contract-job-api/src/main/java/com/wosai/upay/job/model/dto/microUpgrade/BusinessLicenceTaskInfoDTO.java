package com.wosai.upay.job.model.dto.microUpgrade;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Map;
import java.util.Date;

/**
 * 营业执照任务信息DTO
 *
 * <AUTHOR>
 * @date 2024/9/5
 */
@Data
public class BusinessLicenceTaskInfoDTO {

    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务上下文信息
     */
    private Map<String, Object> context;

    /**
     * 任务创建时间
     */
    private Timestamp createTime;

    /**
     * 任务更新时间
     */
    private Timestamp updateTime;
}