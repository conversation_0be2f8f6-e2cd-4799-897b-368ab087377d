package com.wosai.upay.job.model.dto.crm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shouqianba.cua.utils.json.JSON;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * crm 信息管理平台审批单
 *
 * <AUTHOR>
 * @date 2025/2/10 17:44
 */
@Data
public class CrmInformationManagementApplyFormDTO {

    /**
     * 审批单id
     */
    @JsonProperty("field_app_info_id")
    private Integer fieldAppInfoId;

    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private String storeId;

    /**
     * 设备号
     */
    @JsonProperty("dev_code")
    private String devCode;

    /**
     * 用户id
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 用户名
     */
    @JsonProperty("user_name")
    private String userName;

    /**
     * 组织id
     */
    @JsonProperty("organization_id")
    private String organizationId;

    /**
     * 平台
     * crm_app
     * app
     * msp
     */
    private String platform;

    /**
     * 字段信息
     */
    @JsonProperty("dev_param")
    private Map<Object, Object> devParam;

    /**
     * 业务方信息
     */
    @JsonProperty("business_app_info")
    private Map<String, Object> businessAppInfo;


    public List<CrmFormFieldInfoDTO> extractCrmFormFieldInfoDTOsFromDevParams() {
        if (MapUtils.isEmpty(devParam) || !devParam.containsKey("field_list")) {
            return null;
        }
        return JSON.parseArray(JSON.toJSONString(devParam.get("field_list")), CrmFormFieldInfoDTO.class);
    }

    public boolean applyFromApp() {
        return StringUtils.equals("app", this.platform);
    }

    public boolean applyFromCrm() {
        return StringUtils.equals("crm_app", this.platform);
    }


}
