package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

@JsonRpcService("/rpc/umbDataClean")
@Validated
public interface UmbDataCleanService {

    Map<String, Object> setTradeConfigForUmbShareProfit(List<String> snList, boolean shareProfit);

    Map<String, Object> fixHuaBeiForExistingMerchants(List<String> merchantUserList, int coreThreadNum);

}
