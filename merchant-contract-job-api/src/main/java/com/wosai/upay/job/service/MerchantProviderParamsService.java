package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.directparams.*;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import com.wosai.upay.job.validation.group.UpdateMerchantProviderParams;
import com.wosai.web.api.ListResult;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import vo.ApiRequestParam;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 商户各个渠道报备参数（直连 + 间连）
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@JsonRpcService("/rpc/merchantProviderParams")
@Validated
public interface MerchantProviderParamsService {

    /**
     * 添加支付宝2.0直连参数
     *
     * @param alipayV2DirectParams
     */
    boolean addAlipayV2DirectParams(@Valid AlipayV2DirectParams alipayV2DirectParams);

    /**
     * 添加支付宝2.0直连参数 + 商户日志
     * @param params
     * @param dto
     * @return
     */
    boolean addAlipayV2DirectParamsWithLog(AlipayV2DirectParams params, LogParamsDto dto);

    /**
     * 添加微信直连参数
     *
     * @param weixinDirectParams
     */
    boolean addWeixinDirectParams(@Valid WeixinDirectParams weixinDirectParams);

    /**
     * 添加微信直连参数 + 商户日志
     * @param params
     * @param dto
     * @return
     */
    boolean addWeixinDirectParamsWithLog(WeixinDirectParams params, LogParamsDto dto);

    /**
     * 添加支付宝国际直连参数
     *
     * @param alipayIntlDirectParams
     */
    boolean addAlipayIntlDirectParams(@Valid AlipayIntlDirectParams alipayIntlDirectParams);

    /**
     * 添加支付宝国际直连参数 + 商户日志
     * @param params
     * @param dto
     * @return
     */
    boolean addAlipayIntlDirectParamsWithLog(AlipayIntlDirectParams params, LogParamsDto dto);

    /**
     * 添加微信香港直连参数
     *
     * @param weixinHKDirectParams
     */
    boolean addWeixinHKDirectParams(@Valid WeixinHKDirectParams weixinHKDirectParams);

    /**
     * 添加微信香港直连参数 + 商户日志
     * @param params
     * @param dto
     * @return
     */
    boolean addWeixinHKDirectParamsWithLog(WeixinHKDirectParams params, LogParamsDto dto);

    /**
     * 添加翼支付直连参数
     *
     * @param bestpayDirectParams
     */
    boolean addBestpayDirectParams(@Valid BestpayDirectParams bestpayDirectParams);

    /**
     * 添加翼支付直连参数 + 商户日志
     * @param params
     * @param dto
     * @return
     */
    boolean addBestpayDirectParamsWithLog(BestpayDirectParams params, LogParamsDto dto);

    /**
     * 查询商户交易参数列表
     *
     * @param pageInfo                        分页信息
     * @param merchantProviderParamsCustomDto 查询条件
     * @return 商户交易参数列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/16573">api 文档</a>
     */
    ListResult<MerchantProviderParamsCustomDto> findMerchantProviderParamsList(PageInfo pageInfo,
                                                                               MerchantProviderParamsCustomDto merchantProviderParamsCustomDto);

    /**
     * 根据id查询商户交易参数详情
     *
     * @param id 商户交易参数
     * @return 商户交易参数详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/16582">api 文档</a>
     */
    MerchantProviderParamsCustomDto findMerchantProviderParamsDetailById(@NotBlank(message = "id 不能为空") String id);

    /**
     * 更新商户交易参数详情
     *
     * @param merchantProviderParamsDto 更新参数
     * @return 商户交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/16591">api 文档</a>
     */
    @Validated(UpdateMerchantProviderParams.class)
    MerchantProviderParamsDto modifyMerchantProviderParamsById(@NotNull(message = "参数不能为空") @Valid MerchantProviderParamsDto merchantProviderParamsDto);

    /**
     * 设置间连交易参数为默认交易
     *
     * @param id      交易参数id
     * @param feeRate 费率（选填，不填表示设为默认后费率不变）
     * @param remark  备注（请输入操作原因）
     * @return 该默认交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17032">api 文档</a>
     */
    MerchantProviderParamsDto setDefaultMerchantProviderParams(@NotBlank(message = "交易参数id不能为空") String id,
                                                               String feeRate,
                                                               @NotBlank(message = "备注remark不能为空") String remark);

    /**
     * 设置间连交易参数为默认交易 + 商户日志
     * @param id
     * @param feeRate
     * @param remark
     * @param dto
     * @return
     */
    MerchantProviderParamsDto setDefaultMerchantProviderParamsWithLog(String id, String feeRate, String remark, LogParamsDto dto);

    /**
     * 设置间连交易参数为默认交易
     *
     * @param id      交易参数id
     * @param feeRate 费率（选填，不填表示设为默认后费率不变）
     * @param remark  备注（请输入操作原因）
     * @param tradeAppid
     * @return 该默认交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17032">api 文档</a>
     */
    MerchantProviderParamsDto setDefaultMerchantProviderParamsByTradeApp(@NotBlank(message = "交易参数id不能为空") String id,
                                                               String feeRate,
                                                               @NotBlank(message = "备注remark不能为空") String remark,
                                                                         String tradeAppid);

    /**
     * 设置间连交易参数为默认交易 + 商户日志
     * @param id
     * @param feeRate
     * @param remark
     * @param tradeAppid
     * @param dto
     * @return
     */
    MerchantProviderParamsDto setDefaultMerchantProviderParamsByTradeAppWithLog(String id, String feeRate, String remark, String tradeAppid, LogParamsDto dto);

    /**
     * 设置间连交易参数为默认交易
     *
     * @param payMchId 支付源子商户号
     * @param remark   备注（请输入操作原因）
     * @return 该默认交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17032">api 文档</a>
     */
    MerchantProviderParamsDto setDefaultPayMchId(@NotBlank(message = "交易参数id不能为空") String payMchId,
                                                 @NotBlank(message = "备注remark不能为空") String remark);

    /**
     * @return msg
     * <AUTHOR>
     * @Description:获取设为默认的信息提示
     * @time 11:21 上午
     **/
    String getSetDefaultMerchantProviderParamsMsg(@NotBlank(message = "交易参数id不能为空") String id);

    /**
     * 删除直连交易参数
     *
     * @param paramsId  要删除的交易参数的id
     * @param subPayway
     * @param feeRate   费率：选填，不填表示切交易参数费率不变
     * @return 新的交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17041">api 文档</a>
     */
    boolean deleteDirectParams(@NotBlank(message = "交易参数 Id 不能为空") String paramsId, String subPayway, String feeRate);

    /**
     * 删除直连交易参数 + 商户日志
     * Params:
     * paramsId – 要删除的交易参数的id subPayway feeRate – 费率：选填，不填表示切交易参数费率不变
     * Returns:
     * 新的交易参数
     * See Also:
     * api 文档
     */
    boolean deleteDirectParamsWithLog(String paramsId, String subPayway, String feeRate, LogParamsDto dto);

    /**
     * 删除直连交易参数
     *
     * @param merchantId
     * @param payway
     * @param subPayway
     * @param feeRate    费率：选填，不填表示切交易参数费率不变
     * @return 新的交易参数
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/17041">api 文档</a>
     */
    boolean deleteDirectParamsByMerchantId(@NotBlank(message = "商户 Id 不能为空") String merchantId, int payway, String subPayway, String feeRate);

    /**
     * 获取收单机构商户信息
     *
     * @param merchantId 商户id
     * @return 收单机构商户信息
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/18661">api 文档</a>
     */
    List<AcquirerMerchantDto> getAcquirerMerchantInfo(@NotBlank(message = "商户 id 不能为空") String merchantId);

    /**
     * 添加微信sub_appid
     *
     * @param id
     * @param appidDto
     * @return
     */
    MerchantProviderParamsDto addWeixinSubAppid(@NotEmpty(message = "id 不能为空") String id, @NotNull(message = "appidDto 不能为空") @Valid WeixinSubAppidDto appidDto);


    /**
     * 添加微信sub_appid + 商户日志
     * @param id
     * @param appidDto
     * @param dto
     * @return
     */
    MerchantProviderParamsDto addWeixinSubAppidWithLog(String id, WeixinSubAppidDto appidDto, LogParamsDto dto);

    /**
     * @param id       交易参数ID
     * @param appidDto 添加的subAppid
     * <AUTHOR>
     * @Description:存在多个小程序appid时 提供设为默认操作
     * @time 3:33 下午 2020/9/15
     **/
    void setDefaultSubAppid(@NotEmpty(message = "id 不能为空") String id, @NotNull(message = "appidDto 不能为空") @Valid WeixinSubAppidDto appidDto);

    /**
     * 获取间连在用的支付源商户号
     *
     * @param merchantSn
     * @param payway
     * @return null表示没有在用的间连参数
     */
    String getInUsePayMchId(String merchantSn, int payway);

    /**
     * 获取间连在用的支付源商户号对应的交易参数信息
     *
     * @param merchantSn 商户号
     * @param payway 2支付宝 3微信
     * @return null表示没有在用的间连参数
     */
    MerchantProviderParamsDto getInUseProviderParams(String merchantSn, int payway);

    /**
     * 将 merchant_config 中的参数同步到 merchant_provider_params
     * 小工具等直接更改 merchant_config，导致 merchant_config 中的交易参数与 merchant_provider_params 正在使用中的参数不一致
     * 可使用此方法同步
     *
     * @param merchantSn
     * @return
     */
    boolean syncMerchantConfigToParams(String merchantSn);

    /**
     * 获取商户交易参数（原始数据，直接从数据库获取，不做处理）
     * merchant_sn 和 pay_merchant_id 不能同时为空
     *
     * @param req
     * @return
     */
    List<MerchantProviderParamsDto> getMerchantProviderParams(MerchantParamReq req);

    /**
     * 根据商户号查询出正在使用和线上微信子商户号来配置sub_appid
     * @return 配置结果
     */
    List<WeixinSubAppidAddResp> addWeixinSubAppidForUseAndOnline(@Valid WeixinSubAppidAddReq req);

    /**
     * 为久久折小程序配置微信子账户
     *
     * @param id       MerchantProviderParams表主键
     * @param appidDto 微信子账号
     * @return MerchantProviderParamsDto 配置后的数据
     * @Author: zhmh
     * @Description:
     * @time: 17:51 2020/11/10
     */
    MerchantProviderParamsDto justAddWeixinSubAppid(@NotEmpty(message = "MerchantProviderParamsId 不能为空") String id, @NotNull(message = "appIdDto 不能为空") @Valid WeixinSubAppidDto appidDto);

    /**
     * 通过微信子商户号配置微信appId
     *
     * @param wxSubMchId 微信子商户号
     * @param subAppId   微信appId
     * @return
     */
    MerchantProviderParamsDto addWeixinSubAppIdBySubMchId(@NotEmpty(message = "wxSubMchId 不能为空") String wxSubMchId, @NotEmpty(message = "wxSubMchId 不能为空") String subAppId);


    /**
     * 查询商户微信高效食堂子商户号
     *
     * @param merchantSn
     * @return
     */
    String getSchoolCanteenMchId(@NotEmpty(message = "merchantSn不能为空") String merchantSn);


    /**
     * 替换华夏微信子商户号
     * @param oriSubId 老商户号
     * @param tarSubId 新商户号
     * @return
     */
    boolean updateHxbWxSubMchId(@NotBlank(message = "原有子商户号oriSubId不为空") String oriSubId
            ,@NotBlank(message = "新商户号tarSubId不为空") String tarSubId);

    /**
     * 删除子商号
     * @param subMchId
     * @return
     */
    int deleteInvalidSubMchId(@NotBlank(message = "子商号不能为空") String subMchId);

    /**
     * 查询正在使用的子业务参数
     * @param merchantSn 商户号
     * @return
     */
    Map<String,List<MerchantProviderParamsCustomDto>> findMerchantSubBizParams(@NotBlank(message = "merchantSn不能为空") String merchantSn);

    /**
     * 结算id check
     */
    boolean checkSettleId(String id);


    /**
     *银行通道支持花呗分期
     * @param merchantSn 商户号
     */
    public void openHuaBei(@NotBlank(message = "商户号不能为空")String merchantSn);

    /**
     *银行通道关闭花呗分期
     * @param merchantSn 商户号集合
     */
    public void closeHuaBei(@NotBlank(message = "商户号不能为空")String merchantSn);

    /**
     * 商户开通微信跨城收款
     * @param merchantSn
     */
    @Deprecated
    void openCrossCityPayment(@NotBlank(message = "商户号不能为空") String merchantSn);

    /**
     * 商户开通跨城收款
     * @param crossCityPaymentOpenReq
     */
    void openCrossCityPayment(@Valid CrossCityPaymentOpenReq crossCityPaymentOpenReq);

    /**
     * 商户关闭跨城收款
     * @param crossCityPaymentCloseReq
     */
    ContractResponse closeCrossCityPayment(@Valid CrossCityPaymentCloseReq crossCityPaymentCloseReq);

    /**
     * 查询当前通道下的微信交易参数列表
     * @param merchantSn 商户号
     * @return 微信交易参数列表，如果不存在则返回空数组
     */
    List<MerchantProviderParamsDto> queryCurrentAcquirerWeixinParams(@NotBlank(message = "商户号不能为空") String merchantSn);


    /**
     * 查询子商户号授权状态
     * @param subMchId 子商户号
     * @return 查询子商户号授权状态
     */
    boolean queryMchIdAuthStatus(@NotBlank(message = "子商户号不能为空") String subMchId);



    /**通道开通/切换限制商户同1时间只能生效1个第三方收单机构通道
     * @param merchantSn 商户编号
     * @param provider 业务通道
     * @return
     */
    public void allowOpenBusiness(@NotBlank(message = "商户号不能为空") String merchantSn,Integer provider);


    /**
     *指定商户开通某个多业务
     * @param merchantSn 商户号
     * @param tradeAppId 业务方
     */
    public void openMultiTrade(@NotBlank(message = "商户号不能为空") String merchantSn
            , @NotBlank(message = "业务方不能为空") String tradeAppId);


    /**
     * 指定商户开通某个多业务前校验是否允许开通
     * @param merchantSn
     * @param tradeAppId
     * @return
     */
    MultiOpenCheckResp  checkOpenMultiTrade(@NotBlank(message = "商户号不能为空") String merchantSn
            , @NotBlank(message = "业务方不能为空") String tradeAppId);

    /**
     * 同步微信、支付宝子商户号信息到拉卡拉
     * @param providerMerchantId 商户号
     */
    SyncSubMchToLklResp syncSubMchToLkl(@NotBlank(message = "商户号不能为空") String providerMerchantId);

    /**
     * 查询银联云闪付的开通状态
     * @param req 请求参数
     * @return 返回信息
     */
    UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(@Valid UnionPayOpenStatusQueryReq req);

    /**
     * 查询云闪付开通状态
     * @param req
     * @return
     */
    UnionPayOpenStatusQueryResp queryUnionPayOpenStatusForCrm(@Valid ApiRequestParam<UnionPayOpenStatusQueryReq> req);

    /**
     * 切换商户支付模式
     * @param req 请求参数
     * @return 切换结果
     */
    CommonResult changePaymentMode(@Valid PaymentModeChangeReq req);
}
