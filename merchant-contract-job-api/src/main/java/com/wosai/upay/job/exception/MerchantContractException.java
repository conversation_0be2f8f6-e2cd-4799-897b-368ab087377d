package com.wosai.upay.job.exception;


import com.wosai.common.exception.RuntimeWithCodeException;

/**
 * 进件异常基类
 *
 * <AUTHOR>
 * @date 2019-07-16
 */
public abstract class MerchantContractException extends RuntimeWithCodeException {
    public MerchantContractException(String message, Throwable cause) {
        super(message, cause);
    }

    public MerchantContractException(String message) {
        super(message);
    }

    public MerchantContractException(Throwable cause) {
        super(cause);
    }
}
