package com.wosai.upay.job.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * @Author: jerry
 * @date: 2019/7/22 15:06
 * @Description:
 */
@Data
public class BatchContract implements IExcelModel, IExcelDataModel {

    @Excel(name = "商户号", isImportField = "true", width = 15)
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;
    @Excel(name = "备注", isImportField = "true", width = 20)
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Excel(name = "执行结果", width = 20)
    private String errorMsg;
    private int rowNum;

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public int getRowNum() {
        return this.rowNum;
    }

    @Override
    public void setRowNum(int rowNum) {
        this.rowNum = rowNum;
    }
}
