package com.wosai.upay.job.model.combo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 切换商户支付套餐
 * <AUTHOR>
 * @Date 2020/9/2 2:42 下午
 **/
@Data
@Accessors(chain = true)
public class MerchantComboReq {

    @NotNull(message = "商户套餐ID不能为空")
    @JsonProperty("merchant_combo_id")
    private Long merchantComboId;

    @NotNull(message = "二级支付方式不能为空")
    @JsonProperty("sub_columns")
    private List<String> subColumns;

    private String fee;

    @NotNull(message = "备注不能为空")
    private String remark;

    @NotEmpty(message = "商户号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;
}
