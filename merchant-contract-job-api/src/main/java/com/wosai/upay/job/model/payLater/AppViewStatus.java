package com.wosai.upay.job.model.payLater;

import lombok.Data;

/**
 * @Description: 手机端申请状态展示
 * <AUTHOR>
 * @Date 2023/7/31 下午3:24
 */
@Data
public class AppViewStatus {
    /**
     * 主状态 0-支付宝内部审核,10-芝麻先享平台审核,20-开通成功,30-支付宝内部审核失败,40-芝麻先享平台审核失败
     */
    public Integer status;

    /**
     * 主状态下的细节状态
     * 0-直付通商户申请中,10-待确认,20-芝麻商户审核中,30-申请成功,50-申请失败
     */
    public Integer subStatus;

    /**
     * 失败原因
     */
    public String result;


}
