package com.wosai.upay.job.model.dto.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wosai.upay.job.model.dto.ObjectPropertyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 收单组路由规则详情表表Response DTO对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("收单组路由规则详情表表Response DTO对象")
public class GroupRouteRuleDetailRspDTO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("对应进件规则决策表id")
    private Long ruleDecisionId;

    @ApiModelProperty("商户名称: name\n" +
            "商户类型(1-小微 2-个体户 3-企业商户 4-组织商户): type\n" +
            "区唯一编码: districtCode\n" +
            "省唯一编码: provinceCode\n" +
            "市唯一编码: cityCode\n" +
            "商户行业: industry\n" +
            "所属推广组织path(从一级开始): promotionOrganizationPath\n" +
            "银行账户类型(1-对私 2-对公): bankAccountType\n" +
            "法人类型(0-非法人 1-法人): legalPersonType\n" +
            "结算账户类型(1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他): settlementAccountType")
    private String objectPropertyType;

    @ApiModelProperty("逻辑操作类型 EQUAL-等于,NOT_EQUAL-不等于,CONTAIN-包含(给定对象属性值包含用户特征属性值),NOT_CONTAIN-不包含(给定对象属性值不包含用户特征属性值),\n" +
            "BE_CONTAINED-被包含(用户特征属性值包含给定对象属性值),NOT_BE_CONTAINED-被不包含(用户特征属性值不包含给定对象属性值）\n" +
            "IN, NOT_IN,START_WITH,NOT_START_WITH(主要用于所属推广组织path匹配)")
    private String logicalOperationType;

    @ApiModelProperty("原始对象属性值")
    private String objectPropertyValue;

    @ApiModelProperty("对象属性值列表")
    private List<ObjectPropertyDTO> objectPropertyDTOList;

    @ApiModelProperty("有效状态 0-失效 1-生效")
    private Integer validStatus;

    @ApiModelProperty("更新人")
    private Long updateBy;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;
}

