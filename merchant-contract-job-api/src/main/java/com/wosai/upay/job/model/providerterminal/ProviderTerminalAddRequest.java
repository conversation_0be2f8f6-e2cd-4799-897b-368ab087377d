package com.wosai.upay.job.model.providerterminal;

import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.enume.ProviderTerminalBindLevel;
import com.wosai.upay.job.validation.In;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Data
@Accessors(chain = true)
public class ProviderTerminalAddRequest {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;
    /**
     * 门店号
     */
    private String storeSn;
    /**
     * 终端号
     */
    private String terminalSn;

    /**
     * 终端号，支持传入，如果有值则使用该值
     */
    private String providerTerminalId;

    @Valid
    @NotNull(message = "收单机构商户信息不能为空")
    private ProviderTerminalAcquirerMerchantInfo acquirerMerchantInfo;
    /**
     * 级别
     */
    @NotNull(message = "级别不能为空")
    private ProviderTerminalBindLevel bindLevel;

    @AssertTrue(message = "商户号、门店号、终端号和绑定级别对应关系错误")
    public boolean isValidSn() {
        if (ProviderTerminalBindLevel.MERCHANT.equals(bindLevel)) {
            // 商户级别：merchantSn不能为空，storeSn和terminalSn必须为空
            return WosaiStringUtils.isNotBlank(merchantSn)
                    && WosaiStringUtils.isBlank(storeSn)
                    && WosaiStringUtils.isBlank(terminalSn);
        } else if (ProviderTerminalBindLevel.STORE.equals(bindLevel)) {
            // 门店级别：merchantSn和storeSn不能为空，terminalSn必须为空
            return WosaiStringUtils.isNotBlank(merchantSn)
                    && WosaiStringUtils.isNotBlank(storeSn)
                    && WosaiStringUtils.isBlank(terminalSn);
        } else if (ProviderTerminalBindLevel.TERMINAL.equals(bindLevel)) {
            // 终端级别：三个都不能为空
            return WosaiStringUtils.isNotBlank(merchantSn)
                    && WosaiStringUtils.isNotBlank(storeSn)
                    && WosaiStringUtils.isNotBlank(terminalSn);
        } else {
            return false;
        }
    }
}
