package com.wosai.upay.job.service.task;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * 进件任务结果追踪任务服务
 *
 * <AUTHOR>
 * @date 2024/9/5 17:11
 */
@JsonRpcService("/rpc/task/track-contract-result")
@Validated
public interface TrackContractTaskResultTaskService {

    /**
     * 对公账户凭证审批
     */
    public static final int BUSINESS_SCENE_TYPE_CORPORATE_RECEIPT_PHOTO_AUDIT = 1;

    /**
     * 公示网截图审批
     */
    public static final int BUSINESS_SCENE_TYPE_ANNOUNCEMENT_WEBSITE_PHOTO_AUDIT = 2;

    /**
     * 更新首家门店照片
     */
    public static final int BUSINESS_SCENE_TYPE_UPDATE_FIRST_STORE = 3;

    /**
     * 新增进件任务结果追踪任务
     *
     * @param merchantSn     商户号
     * @param auditId        审批id
     * @param contractTaskId 进件任务id
     * @param templateId     审批模板id
     */
    void insertTask(int businessSceneType, @NotBlank(message = "商户号不能为空") String merchantSn, String auditId, Long contractTaskId, Long templateId);

}
