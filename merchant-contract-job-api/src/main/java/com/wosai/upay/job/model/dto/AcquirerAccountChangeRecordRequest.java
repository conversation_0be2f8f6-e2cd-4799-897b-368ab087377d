package com.wosai.upay.job.model.dto;

import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import lombok.Data;

/**
 * 收单机构账户变更记录请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
public class AcquirerAccountChangeRecordRequest {

    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 收单机构
     */
    private String acquirer;

    /**
     * 收单机构商户号
     */
    private String acquirerMerchantId;

    /**
     * 原银行卡信息
     */
    private BankAccountSimpleInfoBO oldAccountInfo;

    /**
     * 新银行卡信息
     */
    private BankAccountSimpleInfoBO newAccountInfo;

    /**
     * 状态
     * 0：处理中
     * 1：成功
     * 2：失败
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}