package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * @Description: 手机外卡服务, 开通和校验接口会对接app网管
 * <AUTHOR>
 * @Date 2024/6/7 11:03
 */
@JsonRpcService("/rpc/mobile_pos")
@Validated
public interface MobilePosService {

    /**
     * app网关转发过来的参数类似
     *
     {
     "role": "super_admin",
     "client_version": "6.0.0",
     "account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "date_end": "*************",
     "id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "sub_appid": "sqb",
     "uc_user_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "cash_store_id": "a0145693-914f-486d-898c-afadebf127df",
     "merchant_id": "e2bc0928-014e-4a98-bbc0-1b8ea78a3a79",
     "msp_account_id": "9f095c3c-7860-49c3-9ef1-53d46e820a6f",
     "store_id": "a0145693-914f-486d-898c-afadebf127df",
     "role_id": "35eb325c-4fad-4d44-8939-d1f0d46bf3c9",
     "merchant_user_id": "67b47bb6-09f1-4273-ba61-c7e1cb06eba2",
     "token": "********************************************************************************.76lJ8puYtN"
     }
     */
    /**
     * 手机POS收款入口展示校验
     *
     * @param params
     * @return
     */
    Boolean isPaymentValid(Map params);


    /**
     * 开通手机POS
     *
     * @param params
     * @return
     */
    Boolean openMobilePos(Map params);


    /**
     * 关闭手机POS
     *
     * @param merchantSn
     * @return
     */
    void closeMobilePos(String merchantSn);


    /**
     * 关闭手机POS crm调用
     * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********#id-%E4%B8%9A%E5%8A%A1%E5%BC%80%E9%80%9A-%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88-3.8%E5%BA%94%E7%94%A8%E6%B3%A8%E9%94%80%E6%8E%A5%E5%8F%A3
     *
     * @param crmMap
     * @return
     */
    Map crmCloseMobilePos(Map crmMap);

    /**
     * 开通apple pay外卡支付
     *
     * @param merchantSn 商户号
     * @param auditId 审批Id
     */
    void openApplyPay(@NotEmpty(message = "商户号不能为空") String merchantSn, Long auditId);


}
