package com.wosai.upay.job.model.payLater;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 修改芝麻商户费率
 * <AUTHOR>
 * @Date 2023/8/4 上午11:58
 */
@Data
@Validated
public class ModifyFeeRate {

    @NotBlank(message = "商户号不为空")
    private String merchantSn;

    @NotBlank(message = "费率不为空")
    private String targetFeeRate;
}
