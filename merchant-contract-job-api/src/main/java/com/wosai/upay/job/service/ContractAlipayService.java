package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: 单独处理支付宝的一系列行为
 * <AUTHOR>
 * @Date 2020/11/24 6:12 PM
 **/
@JsonRpcService("/rpc/contractAlipay")
@Validated
public interface ContractAlipayService{

    /**
     * 确保商户支付宝 level = m3, service_code =
     * @param merchantSn
     * @return
     */
    ContractResponse configServiceCode(String merchantSn);
}
