package com.wosai.upay.job.model.psbc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/30
 */
@Data
public class ModifySupportingMaterialsRequest implements Serializable {
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    @NotBlank(message = "业务码devCode不能为空")
    private String devCode;

    @NotEmpty
    @Size(max = 4,min = 1)
    private List<String> picList;
}
