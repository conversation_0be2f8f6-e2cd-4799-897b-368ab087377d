package com.wosai.upay.job.model.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 银行保障交易配置请求DTO
 *
 * <AUTHOR>
 * @date 2024/8/15 14:49
 */
@Data
public class BankTradeProtectionConfigReqDTO {

    public static final Integer CLOSE_PROTECTION = 1;

    public static final Integer NOT_CLOSE = 2;

    public static final Integer RECOVER_PROTECTION = 3;

    public static final Integer NOT_RECOVER = 4;

    /**
     * 保障状态  1-关闭保障  2-不关闭 3-恢复保障 4-不恢复
     */
    @NotNull(message = "保障状态不能为空")
    private Integer protectionStatus;

    @NotEmpty(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 是否关闭保障
     *
     * @return 是否关闭保障 true-关闭 false-不关闭
     */
    public boolean isCloseProtection() {
        return Objects.equals(protectionStatus, CLOSE_PROTECTION);
    }

    /**
     * 是否不关闭
     *
     * @return 是否不关闭 true-不关闭 false-关闭
     */
    public boolean isNotClose() {
        return Objects.equals(protectionStatus, NOT_CLOSE);
    }

    /**
     * 是否恢复保障
     *
     * @return 是否恢复保障 true-恢复 false-不恢复
     */
    public boolean isRecoverProtection() {
        return Objects.equals(protectionStatus, RECOVER_PROTECTION);
    }

    /**
     * 是否不恢复
     *
     * @return 是否不恢复 true-不恢复 false-恢复
     */
    public boolean isNotRecover() {
        return Objects.equals(protectionStatus, NOT_RECOVER);
    }

}
