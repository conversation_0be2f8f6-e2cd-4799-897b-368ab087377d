package com.wosai.upay.job.model.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * 银行自动切三方请求DTO
 *
 * <AUTHOR>
 * @date 2024/6/18 14:24
 */
@Data
public class BankAutoChangeToThirdPartyReqDTO {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不为空")
    private String merchantSn;

    /**
     * 交易渠道,银行通道修复后会回切
     */
    @NotBlank(message = "交易渠道不为空")
    private String provider;

    /**
     * 支付源
     */
    @NotBlank(message = "支付源不为空")
    private String payWay;

    /**
     * 报错码（收钱啊）
     */
    @NotBlank(message = "错误码不能为空")
    private String errorCode;

    /**
     * 报错码含义（转义后的文案）
     */
    private String errorCodeMeaning;

    /**
     * 收单机构原始报错信息
     */
    private String acquirerOriginalErrorMsg;

    /**
     * 切换的业务方
     */
    private Set<String> traAppIds;

    /**
     * 切换至目标三方通道
     */
    private String targetAcquirer;

    /**
     * 任务工单实例id
     */
    private String taskInstanceId;
}
