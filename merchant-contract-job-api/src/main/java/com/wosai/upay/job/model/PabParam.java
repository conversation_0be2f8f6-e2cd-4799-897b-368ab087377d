package com.wosai.upay.job.model;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PabParam {

    /**
     * 收钱吧商户号
     */
    @NotBlank(message = "merchant_sn 不能为空")
    private String merchant_sn;

    /**
     * 平安银行子商户号
     */
    @NotBlank(message = "provider_mch_id 不能为空")
    private String provider_mch_id;

    /**
     * 商户终端号 b2c,固定
     */
    private String provider_term_id_b2c = "00000001";


    /**
     * 商户终端号 c2b，固定
     */
    private String provider_term_id_c2b = "00000002";

    /**
     * 微信子商户号
     */
    @NotBlank(message = "weixin_sub_mch_id 不能为空")
    private String weixin_sub_mch_id;

    /**
     * 支付宝子商户号
     */
    @NotBlank(message = "alipay_sub_mch_id 不能为空")
    private String alipay_sub_mch_id;

    /**
     * 云闪付子商户号
     */
    @NotBlank(message = "union_open_sub_mch_id 不能为空")
    private String union_open_sub_mch_id;


    /**
     * 费率
     */
    @NotBlank(message = "fee_rate 不能为空")
    private String fee_rate;

    /**
     * 证件号结尾4位
     */
    @NotBlank(message = "identify_end 不能为空")
    private String identify_end;

    /**
     * 银行卡持有人
     */
    @NotBlank(message = "holder_name 不能为空")
    private String holder_name;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不为空")
    private String operate;


}
