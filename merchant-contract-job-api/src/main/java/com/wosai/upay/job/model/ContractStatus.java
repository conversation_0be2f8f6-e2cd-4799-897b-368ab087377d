package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


@Data
@Accessors(chain = true)
public class ContractStatus {

    /**
     * 待处理
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 1;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 2;

    /**
     * 业务失败
     */
    public static final int STATUS_BIZ_FAIL = 3;

    /**
     * 这常量给外部用 实际数据库不存有这个状态
     */
    public static final int STATUS_WAIT_VERIFY = 11;

    /**
     * 等待代付
     */
    public static final int STATUS_WAIT_FOR_PAY = 12;

    /**
     * 代付成功
     */
    public static final int STATUS_PAY_FOR_SUCCESS = 13;


    private Long id;

    private String merchant_sn;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String acquirer;

    public static Boolean isFinish(int status){
        return status == STATUS_SUCCESS || status == STATUS_BIZ_FAIL;
    }


}