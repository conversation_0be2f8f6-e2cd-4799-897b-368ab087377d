package com.wosai.upay.job.model.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商户特征对象属性请求dto
 *
 * <AUTHOR>
 * @date 2024/3/28 14:19
 */
@Data
@ApiModel("商户特征对象属性请求dto")
@AllArgsConstructor
@NoArgsConstructor
public class MerchantObjectPropertyDTO {

    /**
     * 对象属性类型
     * 商户名称: name
     * 商户类型(1-小微 2-个体户 3-企业商户 4-组织商户): type
     * 区唯一编码: districtCode
     * 省唯一编码: provinceCode
     * 市唯一编码: cityCode
     * 商户行业: industry
     * 所属推广组织path(从一级开始): promotionOrganizationPath
     * 银行账户类型(1-对私 2-对公): bankAccountType
     * 法人类型(0-非法人 1-法人): legalPersonType
     * 结算账户类型(1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他): settlementAccountType
     */
    @ApiModelProperty("对象属性类型\n" +
            "商户名称: name\n" +
            "商户类型(1-小微 2-个体户 3-企业商户 4-组织商户): type\n" +
            "区唯一编码: districtCode\n" +
            "省唯一编码: provinceCode\n" +
            "市唯一编码: cityCode\n" +
            "商户行业: industry\n" +
            "所属推广组织path(从一级开始): promotionOrganizationPath\n" +
            "银行账户类型(1-对私 2-对公): bankAccountType\n" +
            "法人类型(0-非法人 1-法人): legalPersonType\n" +
            "结算账户类型(1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他): settlementAccountType")
    private String objectPropertyType;
    /**
     * 逻辑操作类型 EQUAL-等于,NOT_EQUAL-不等于,CONTAIN-包含(给定对象属性值包含用户特征属性值),NOT_CONTAIN-不包含(给定对象属性值不包含用户特征属性值),
     * BE_CONTAINED-被包含(用户特征属性值包含给定对象属性值),NOT_BE_CONTAINED-被不包含(用户特征属性值不包含给定对象属性值）
     * IN, NOT_IN,START_WITH,NOT_START_WITH(主要用于所属推广组织path匹配)
     */
    @ApiModelProperty("逻辑操作类型 EQUAL-等于,NOT_EQUAL-不等于,CONTAIN-包含(给定对象属性值包含用户特征属性值),NOT_CONTAIN-不包含(给定对象属性值不包含用户特征属性值),\n" +
            "BE_CONTAINED-被包含(用户特征属性值包含给定对象属性值),NOT_BE_CONTAINED-被不包含(用户特征属性值不包含给定对象属性值）\n" +
            "IN, NOT_IN,START_WITH,NOT_START_WITH(主要用于所属推广组织path匹配)")
    private String logicalOperationType;
    /**
     * 对象属性值 json字符串数组 例如: ["1","2","3"]
     */
    @ApiModelProperty("对象属性值 json字符串数组 例如: [\"1\",\"2\",\"3\"]")
    private List<String> objectPropertyValue;

}
