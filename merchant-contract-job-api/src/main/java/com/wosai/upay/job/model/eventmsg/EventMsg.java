package com.wosai.upay.job.model.eventmsg;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-09-10
 */
@Data
@Accessors(chain = true)
public class EventMsg {

    /**
     * 表名
     */
    private String table_name;

    /**
     * 变更字段名
     */
    private List<String> msg;

    /**
     * 事件类型
     */
    private Integer opt_type;

    /**
     * 变更后对象
     */
    private Map source;

    /**
     * 调用方
     */
    private String platform;
}
