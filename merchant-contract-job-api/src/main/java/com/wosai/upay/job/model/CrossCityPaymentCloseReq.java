package com.wosai.upay.job.model;

import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.validation.In;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
@Data
public class CrossCityPaymentCloseReq {

    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;

    @NotNull(message = "支付源错误")
    @In(values = {2, 3})
    private Integer payway;
}
