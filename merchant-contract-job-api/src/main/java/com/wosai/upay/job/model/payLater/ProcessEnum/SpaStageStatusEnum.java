package com.wosai.upay.job.model.payLater.ProcessEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 子商户号用途枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum SpaStageStatusEnum {
    PENDING(0, "提交申请"),
    ZFT_APPLYING(10,"直付通进件审核中"),
    ZFT_FAIL(20,"直付通进件审核失败"),
    CONFIRMING(30,"待商户确认协议"),
    ZFT_SUCCESS(40,"直付通进件审核成功"),
    ZHIMA_PENDING(50,"芝麻先享平台创建商户信息"),
    ZHIMA_APPLYING(60,"芝麻先享平台审核中"),
    ANT_SHOP_SUCCESS(90, "蚂蚁门店创建成功"),
    ANT_SHOP_APPLYING(100, "蚂蚁门店创建中"),
    ZHIMA_FAIL(70,"芝麻先享平台审核失败"),
    SUCCESS(80,"开通成功"),
    FAIL(110, "开通失败")
    ;
    private int code;
    private String message;

    public static String getMessage(Integer code) {
        final SpaStageStatusEnum[] ProcessStatusEnum = values();
        for (SpaStageStatusEnum bankDirectApplyStatusEnum : ProcessStatusEnum) {
            if (Objects.equals(bankDirectApplyStatusEnum.getCode(), code)) {
                return bankDirectApplyStatusEnum.getMessage();
            }
        }
        return null;
    }
    public static void main(String[] args) {
        System.out.println(getMessage(80));
    }
}