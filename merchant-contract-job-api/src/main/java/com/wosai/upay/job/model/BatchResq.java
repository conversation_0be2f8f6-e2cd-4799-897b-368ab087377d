package com.wosai.upay.job.model;

import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * @Author: jerry
 * @date: 2019/8/1 13:51
 * @Description:批量操作对象 有坑 ----------
 * 对于批量配置交易参数而言 rule oldRule都指代渠道   对于批量报备操作而言 指代规则
 */
@Data
public class BatchResq {

    @NotBlank(message = "规则不能为空", groups = {ByFile.class, ByRule.class})
    private String rule;
    @NotBlank(message = "文件不能为空", groups = {ByFile.class})
    private String url;

    @NotBlank(message = "旧规则不能为空", groups = {ByRule.class})
    private String oldRule;

    @NotNull(message = "生效时间不能为空", groups = {ChangeChannel.class})
    private Long effectTime;

    @NotNull(message = "备注不能为空", groups = {ByRule.class})
    private String remark;

    private String userName;

    private String userId;

    private String feeRate;

    //反序列化框架需要
    public BatchResq() {

    }

    public BatchResq(String rule, String userId, String userName) {
        this.userId = userId;
        this.rule = rule;
        this.userName = userName;
    }

    public interface ByFile {

    }

    public interface ByRule {

    }

    public interface ChangeChannel {

    }
}
