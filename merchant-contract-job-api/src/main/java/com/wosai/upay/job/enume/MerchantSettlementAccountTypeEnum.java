package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 商户结算账户类型枚举
 *
 * <AUTHOR>
 */
public enum MerchantSettlementAccountTypeEnum implements ITextValueEnum<Integer> {


    LEGAL_PRIVATE(1, "法人对私"),

    NON_LEGAL_PRIVATE(2, "非法人对私"),

    COMMON_PUBLIC(3, "普通对公"),

    OTHER_PUBLIC(4, "其他对公"),

    OTHER(999, "其他");

    private final Integer value;
    private final String text;

    MerchantSettlementAccountTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
