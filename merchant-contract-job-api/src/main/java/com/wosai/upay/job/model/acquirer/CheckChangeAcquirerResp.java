package com.wosai.upay.job.model.acquirer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020-05-13
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CheckChangeAcquirerResp {

    /**
     * 是否能够切换
     */
    private boolean can_change;

    /**
     * 不能切换时的原因
     */
    private String msg;

    public static CheckChangeAcquirerResp pass() {
        return new CheckChangeAcquirerResp(true, null);
    }

    public static CheckChangeAcquirerResp fail(String msg) {
        return new CheckChangeAcquirerResp(false, msg);
    }
}
