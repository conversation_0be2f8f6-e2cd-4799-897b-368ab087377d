package com.wosai.upay.job.model.dto.request;

import lombok.Data;


/**
 * 商户特征请求dto
 *
 * <AUTHOR>
 * @date 2024/8/23 16:25
 */
@Data
public class MerchantFeatureReqDTO {

    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户类型 1-小微 2-个体户 3-企业商户 4-组织商户
     */
    private String type;

    /**
     * 区唯一编码
     */
    private String districtCode;

    /**
     * 省唯一编码
     */
    private String provinceCode;

    /**
     * 市唯一编码
     */
    private String cityCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;


    /**
     * 商户行业
     */
    private String industry;

    /**
     * 所属推广组织path(从一级开始)
     */
    private String promotionOrganizationPath;

    /**
     * 商户所属组织path(从一级开始)
     */
    private String organizationPath;

    /**
     * 银行账户类型 1-对私 2-对公
     */
    private String bankAccountType;

    /**
     * 法人类型 0-非法人 1-法人
     */
    private String legalPersonType;

    /**
     * 结算账户类型 1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他
     */
    private String settlementAccountType;

    /**
     * 个人证件类型 1-身份证 2-港澳居民来往内地通行证 3-台湾居民来往大陆通行证 4-非中华人民共和国护照 5-中国护照 6-港澳居民居住证 7-台湾居民居住证
     */
    private String personalCertificateType;

}
