package com.wosai.upay.job.model.directparams;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 支付宝国际直连交易参数
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Data
public class AlipayIntlDirectParams extends BaseParams {

    private AlipayIntlParams alipay_intl_trade_params;


    @Data
    public static class AlipayIntlParams extends BaseTradeParams {

        private String client_id;

        private String private_key;

        private String merchant_id;

        private String merchant_alipay_industry;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(client_id) &&
                    StringUtils.isEmpty(private_key) &&
                    StringUtils.isEmpty(merchant_id) &&
                    StringUtils.isEmpty(merchant_alipay_industry);
        }
    }
}
