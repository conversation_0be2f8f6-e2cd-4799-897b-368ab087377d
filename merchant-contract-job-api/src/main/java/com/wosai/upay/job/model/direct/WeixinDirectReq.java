package com.wosai.upay.job.model.direct;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@Data
@Accessors(chain = true)
public class WeixinDirectReq {

    @NotBlank(message = "商户号不能为空")
    private String merchant_sn;

    @NotBlank(message = "应用不能为空")
    private String dev_code;

    private String remark;

    private Contact_info contact_info = new Contact_info();

    private App_info app_info = new App_info();

    @Data
    @Accessors(chain = true)
    public static class Contact_info {
        @NotBlank(message = "超级管理员姓名不能为空")
        private String contact_name;
        @NotBlank(message = "超级管理员证件号不能为空")
        private String contact_id_number;
        @NotBlank(message = "超级管理员联系手机号不能为空")
        private String mobile_phone;
        @NotBlank(message = "超级管理员联系邮箱不能为空")
        private String contact_email;
        /**
         * 联系人类型 1 法人 2经办人
         */
        private Integer contact_type;
        /**
         * 联系人证件类型
         */
        private Integer contact_id_doc_type;
        /**
         * 联系人证件正面照片
         */
        private String contact_id_doc_copy;

        /**
         * 联系人证件反面照片
         */
        private String contact_id_doc_copy_back;
        /**
         * 联系人证件有效期开始时间
         */
        private String contact_period_begin;

        /**
         * 联系人证件有效期结束时间
         * 2026-06-06
         * 长期
         */
        private String contact_period_end;

        /**
         * 业务办理授权函
         */
        private String business_authorization_letter;
    }

    @Data
    @Accessors(chain = true)
    public static class App_info {
//        @NotBlank(message = "费率信息不能为空")
        private String fee_rate;
        /**
         * 非信用卡活动费率值, 1、若填写了优惠费率活动ID，则该字段必填  2、仅能填入2位以内小数，且在优惠费率活动ID指定费率范围内
         */
        private String debit_activities_rate;

        /**
         * 信用卡活动费率值
         */
        private String credit_activities_rate;

        //当是借贷分离格式时,有该字段(微信费率配置)
        private List<Map> merchant_config;

        @NotNull(message = "套餐id不能为空")
        private Long trade_combo_id;
        @NotBlank(message = "结算id不能为空")
        private String settlement_id;
        private String app_id;
        private List<String> app_pics;
        private String certificate_letter_copy;
        private List<String> additional_info;
    }
}
