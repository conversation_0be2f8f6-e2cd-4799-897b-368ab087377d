package com.wosai.upay.job.model.directparams;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 微信直连交易参数
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Data
public class WeixinDirectParams extends BaseParams {

    /**
     * WAP 支付参数
     */
    private WeixinWapTradeParams weixin_wap_trade_params;

    /**
     * BSC CSB 参数
     */
    private WeixinTradeParams weixin_trade_params;

    /**
     * 小程序支付参数
     */
    private WeixinTradeParams weixin_mini_trade_params;

    /**
     * H5 支付参数
     */
    private WeixinWapTradeParams weixin_h5_trade_params;

    /**
     * App 支付参数
     */
    private WeixinWapTradeParams weixin_app_trade_params;


    @Data
    public static class WeixinWapTradeParams extends WeixinTradeParams {

        private String weixin_sub_appsecret;

        @Override
        public boolean empty() {
            return super.empty() && StringUtils.isEmpty(weixin_sub_appsecret);
        }
    }


    @Data
    public static class WeixinTradeParams extends BaseTradeParams {

        private String weixin_sub_mch_id;

        private String weixin_sub_appid;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(weixin_sub_appid) && StringUtils.isEmpty(weixin_sub_mch_id);
        }
    }
}
