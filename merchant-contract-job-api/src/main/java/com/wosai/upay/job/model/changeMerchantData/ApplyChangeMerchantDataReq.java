package com.wosai.upay.job.model.changeMerchantData;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
public class ApplyChangeMerchantDataReq {
    @NotEmpty(message = "商户号不能为空")
    private String merchantSn;
    private String bankPreId;
    @NotEmpty(message = "申请来源不能为空")
    private String applySource;
    /**
     * 业务参数
     */
    private String formBody;
    private boolean reContract = false;
}
