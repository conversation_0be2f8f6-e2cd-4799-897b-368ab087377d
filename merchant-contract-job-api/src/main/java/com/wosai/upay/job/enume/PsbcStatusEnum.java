  package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


/**
 * @Description: 邮储返回状态对应的描述
 * <AUTHOR>
 * @Date 2021/7/29 09:29
 */
@Getter
@AllArgsConstructor
public enum  PsbcStatusEnum {
    ENABLE("01", "启用"),
    DEACTIVATE("02", "停用"),
    LOGOUT("03", "注销"),
    APPROVAL_FAILED("05", "审批不通过"),
    TO_BE_ADDED("06", "待商户签约"),
    APPROVAL_TO_BE_EFFECTIVE("07", "审批通过待生效"),
    TO_BE_ASSIGNED("08", "待分配"),
    TO_BE_SIGN("91", "待商户签约"),

            ;

    private String code;
    private String message;


    public static String getMessage(String code) {
        final PsbcStatusEnum[] descriptions = values();
        for (PsbcStatusEnum description : descriptions) {
            if (Objects.equals(description.getCode(), code)) {
                return description.getMessage();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(getMessage("01"));
    }
}