package com.wosai.upay.job.model.acquirer;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: COP处理结果异步通知
 * <AUTHOR>
 * @Date 2023/11/9 下午2:15
 */
@NoArgsConstructor
@Data
public class COPCallBackNotice {


    @JSONField(name = "timestamp")
    private String timestamp;
    @JSONField(name = "eventCode")
    private String eventCode;
    @JSONField(name = "workFlowNo")
    private String workFlowNo;
    @JSONField(name = "source")
    private String source;
    @JSONField(name = "workFlowType")
    private String workFlowType;
    @JSONField(name = "data")
    private ContentDate data;

    @NoArgsConstructor
    @lombok.Data
    public static class ContentDate {
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "remark")
        private String remark;
        @JSONField(name = "handleNodeName")
        private String handleNodeName;
        @JSONField(name = "currentNodeName")
        private String currentNodeName;
        @JSONField(name = "handleDate")
        private String handleDate;
        @JSONField(name = "actId")
        private String actId;
        @JSONField(name = "actBatchNum")
        private String actBatchNum;
        @JSONField(name = "promotionId")
        private String promotionId;
        @JSONField(name = "actCouponLink")
        private String actCouponLink;
    }
}
