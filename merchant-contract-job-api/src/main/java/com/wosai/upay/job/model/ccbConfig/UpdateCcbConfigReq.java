package com.wosai.upay.job.model.ccbConfig;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
@Data
public class UpdateCcbConfigReq {

    @NotNull(message = "id不能为空")
    private Long id;

    @JsonProperty("private_min_price")
    @NotBlank(message = "对私账户结算底价不能为空")
    private String privateMinPrice;

    @JsonProperty("public_min_price")
    @NotBlank(message = "对公账户结算底价不能为空")
    private String publicMinPrice;

    @JsonProperty("support_select_ins_no")
    private Boolean supportSelectInsNo;

    @JsonProperty("ins_no")
    private String insNo;

    @JsonProperty("ins_no_list")
    private String insNoList;

    @NotBlank(message = "结算账号不能为空")
    private String account;

    @JsonProperty("is_auto_change")
    @NotBlank(message = "是否自动切通道")
    private String isAutoChange;

    @NotBlank(message = "操作人不能为空")
    private String operator;

    @JsonProperty("delay_day")
    private String delayDay = "1";

    @JsonProperty("filter_rules")
    private String filterRules;

    @NotNull(message = "图片配置不能为空")
    private Boolean micro_info;

    /**
     * mcc黑名单
     */
    @JsonProperty("black_mcc")
    private String blackMcc;


    @JsonProperty("apply_unionpay")
    private Boolean applyUnionpay;

    @JsonProperty("apply_decp")
    private Boolean applyDecp;
}
