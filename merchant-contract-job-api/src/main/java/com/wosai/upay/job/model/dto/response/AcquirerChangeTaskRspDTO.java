package com.wosai.upay.job.model.dto.response;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 收单机构切换任务返回DTO
 */
@Data
public class AcquirerChangeTaskRspDTO {

    /**
     * 任务id
     */
    private Integer id;


    /**
     * 商户号
     */
    private String merchantSn;

    /**
     * 原收单机构
     */
    private String sourceAcquirer;

    /**
     * 目标收单机构
     */
    private String targetAcquirer;

    /**
     * 状态 1：已提交 2：已发起报备 3：报备成功 5：已关闭交易权限  7：已发起提现  9：切换交易参数成功  11：已打开交易权限  19：切换成功  20：切换失败
     */
    private Integer status;

    /**
     * 提示语
     */
    private String memo;

    /**
     * 创建时间
     */
    private Timestamp createAt;

    /**
     * 更新时间
     */
    private Timestamp updateAt;


    /**
     * 状态变更过程
     */
    private String process;

    /**
     * 额外信息
     */
    private String extra;
}
