package com.wosai.upay.job.model.dto.microUpgrade;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 营业执照认证流程状态
 * @Author: zhmh
 * @Date: 2025/7/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CertificationProcessStatus {
    
    /**
     * 当前步骤
     */
    private int currentStep;
    
    /**
     * 提交时间
     */
    private String submitTime;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 创建空的流程状态
     */
    public static CertificationProcessStatus empty() {
        return new CertificationProcessStatus(0, "", null);
    }
}
