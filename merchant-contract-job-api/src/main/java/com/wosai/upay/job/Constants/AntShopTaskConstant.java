package com.wosai.upay.job.Constants;

/**
 * @Description: 创建蚂蚁店铺相关常量
 * <AUTHOR>
 * @Date 2021/2/24 15:38
 */
public class AntShopTaskConstant {

    //任务状态
    public static class TaskStatus {
        /**
         * 已提交
         */
        public static final Integer PENDING = 0;

        /**
         * 门店支付宝等级是否满足创建蚂蚁门店
         */
        public static final Integer QUALIFY = 1;

        /**
         * 创建支付宝门店申请
         */
        public static final Integer SHOP_CREATED_APPLY = 2;

        /**
         * 失败
         */
        public static final Integer FAIL = 6;

        /**
         * 成功
         */
        public static final Integer SUCCESS = 8;

        /**
         * 待发起授权
         */
        public static final int WAIT_OPERATION_APPLY = 9;

        /**
         * 已经发起授权
         */
        public static final int ALREADY_OPERATION_APPLY = 10;

    }

    //业务类型
    public static class BusinessType {
        /**
         * 扫码点单
         */
        public static final Integer SCAN_CODE = 1;

        /**
         * 其他
         */
        public static final Integer OTHER = 2;


    }

    public static final String TIME_OUT = "商户24h内未完成支付宝授权，请再次发起创建门店";

    public static final int MAX_RETRY = 60;
}
