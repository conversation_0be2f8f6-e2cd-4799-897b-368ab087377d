package com.wosai.upay.job.enume;

/**
 * <AUTHOR>
 * @date 2023/4/7
 */
public enum AuthAndComboTaskStatusCode {

    /**
     * 没有对应的任务
     */
    NO_TASK("0000", "该商户无授权和切换套餐任务"),
    /**
     * 待处理，定时任务还没有调度
     */
    PENDING_TASK("1111", "任务待处理"),
    /**
     * 待子商户号授权
     */
    WAIT_FOR_AUTH("10011", "子商户号待授权"),
    /**
     * 任务处理成功
     */
    TASK_SUCCESS("8888", "任务处理成功"),
    /**
     * 任务处理失败
     */
    TASK_FAIL("9999", "任务处理失败");


    private String code;
    private String msg;

    AuthAndComboTaskStatusCode(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
