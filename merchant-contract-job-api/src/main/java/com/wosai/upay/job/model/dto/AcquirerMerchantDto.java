package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * AcquirerMerchantDto
 *
 * <AUTHOR>
 * @date 2019-08-12 15:30
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcquirerMerchantDto implements Serializable {

    private static final long serialVersionUID = 8686637850253582711L;

    /**
     * 结算通道
     */
    private String provider;

    /**
     * 结算通道名称
     */
    private String providerName;

    /**
     * 收单机构唯一标识
     */
    private String acquirer;

    /**
     * 收单结构名称
     */
    private String acquirerName;

    /**
     * 收单机构商户id
     */
    private String acquirerMerchantId;

    /**
     * 收单机构终端号
     */
    private String acquirerTerminalNo;

    /**
     * 报备时间
     */
    private Long contractTime;

    private int status;

    private Integer disableStatus;

    private List<Object> disableReasons;

}