package com.wosai.upay.job.model.dto.acquirePos;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/27 10:56
 */
@Data
@Accessors(chain = true)
public class CrmOpenCheckDTO {

    @NotBlank(message = "商户号不为空")
    private String merchantSn;

    @NotBlank(message = "开通通道不为空")
    private String acquire;
}
