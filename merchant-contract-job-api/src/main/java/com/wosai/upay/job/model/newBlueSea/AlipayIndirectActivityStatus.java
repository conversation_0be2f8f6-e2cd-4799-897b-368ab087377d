package com.wosai.upay.job.model.newBlueSea;

import lombok.Getter;
import lombok.Setter;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/22 4:58 下午
 */
@Getter
@Setter
public class AlipayIndirectActivityStatus {

    /**
     * 支付宝子商户号
     */
    private String sub_merchant_id;
    /**
     * 报名id
     */
    private String order_id;

    /**
     * 拒绝的时候会返回
     * 失败原因
     */
    private String fail_reason;

    /**
     * 成功的时候返回（可选）
     * 费率
     */
    private String rate;


}
