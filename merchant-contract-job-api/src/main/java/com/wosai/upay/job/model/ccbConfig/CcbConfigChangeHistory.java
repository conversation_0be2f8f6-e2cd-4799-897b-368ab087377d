package com.wosai.upay.job.model.ccbConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 建行配置变更历史
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CcbConfigChangeHistory {

    public static final Integer OP_TYPE_CREATE = 1;

    public static final Integer OP_TYPE_UPDATE = 2;

    public static final Integer OP_TYPE_DELETE = 3;

    private Long id;

    private Long ccb_config_id;

    private Integer op_type;

    private String private_min_price;

    private String public_min_price;

    private Boolean support_select_ins_no;

    private String ins_no;

    private String ins_no_list;

    private String account;

    private Boolean is_auto_change;

    private Integer delay_day;

    private String filter_rules;

    private String operator;

    private Long update_time;

    private Boolean micro_info;

    private String black_mcc;

    private Boolean apply_unionpay;

    private Boolean apply_decp;
}