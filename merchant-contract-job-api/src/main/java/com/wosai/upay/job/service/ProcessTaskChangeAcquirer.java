package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description: 进行中的任务切换收单机构
 * <AUTHOR>
 * @Date: 2021/9/14 4:26 下午
 */
@JsonRpcService("/rpc/process_task_change")
@Validated
public interface ProcessTaskChangeAcquirer {


    /**
     * 商户入网进行中的任务 切换收单机构
     *
     * @param merchantSn 商户号
     * @param source     源收单机构
     * @param target     切换的收单机构
     * @return
     */
    boolean change(@NotBlank(message = "merchantSn 不能为空") String merchantSn,
                   @NotBlank(message = "source 不能为空") String source,
                   @NotBlank(message = "target 不能为空") String target);


    /**
     * 商户入网 指定任务状态 切换收单机构
     *
     * @param merchantSn 商户号
     * @param source     源收单机构
     * @param target     切换的收单机构
     * @return
     */
    boolean changeByTaskStatus(@NotBlank(message = "merchantSn 不能为空") String merchantSn,
                               @NotBlank(message = "source 不能为空") String source,
                               @NotBlank(message = "target 不能为空") String target, @NotNull(message = "status 不能为空") Integer status);


}


