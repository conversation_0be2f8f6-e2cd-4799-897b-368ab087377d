package com.wosai.upay.job.model.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * app通用请求DTO
 *
 * <AUTHOR>
 * @date 2024/8/15 16:33
 */
@Data
public class AppCommonReqDTO {

    @NotEmpty(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    @JsonProperty("uc_user_id")
    private String ucUserId;

    @JsonProperty("merchant_user_id")
    private String merchantUserId;

    @JsonProperty("role")
    private String role;
}
