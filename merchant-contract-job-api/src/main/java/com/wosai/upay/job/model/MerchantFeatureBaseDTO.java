package com.wosai.upay.job.model;

import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import lombok.Data;

/**
 * 商户特征基类
 *
 * <AUTHOR>
 * @date 2025/7/17 10:46
 */
@Data
public class MerchantFeatureBaseDTO {

    /**
     * 商户号
     */
    protected String merchantSn;

    /**
     * 商户名称
     */
    protected String name;

    /**
     * 商户类型 1-小微 2-个体户 3-企业商户 4-组织商户
     */
    protected String type;

    /**
     * 区唯一编码
     */
    protected String districtCode;

    /**
     * 省唯一编码
     */
    protected String provinceCode;

    /**
     * 市唯一编码
     */
    protected String cityCode;

    /**
     * 省名称
     */
    protected String provinceName;

    /**
     * 市名称
     */
    protected String cityName;


    /**
     * 商户行业
     */
    protected String industry;

    /**
     * 所属推广组织path(从一级开始)
     */
    protected String promotionOrganizationPath;

    /**
     * 商户所属组织path(从一级开始)
     */
    protected String organizationPath;

    /**
     * 银行账户类型 1-对私 2-对公
     */
    protected String bankAccountType;

    /**
     * 法人类型 0-非法人 1-法人
     */
    protected String legalPersonType;

    /**
     * 结算账户类型 1-法人对私 2-非法人对私 3-普通对公 4-其他对公 999-其他
     */
    protected String settlementAccountType;

    /**
     * 结算人证件类型 1-身份证 2-港澳居民来往内地通行证 3-台湾居民来往大陆通行证 4-非中华人民共和国护照 5-中国护照 6-港澳居民居住证 7-台湾居民居住证
     * (原个人证件类型）
     */
    protected String personalCertificateType;

    /**
     * 商户开通的应用列表json
     */
    protected String openedBusinessAppIdListJson;
    /**
     * 目标入网收单机构 lklV3 haike
     */
    protected String acquirer;
    /**
     * 品牌商户支付模式
     * 2商家模式 3微信品牌模式 4支付宝品牌模式 5支付宝微信品牌模式
     */
    protected String paymentMode;


    /**
     * 商户经营名称
     */
    protected String businessName;

    /**
     * 法人证件类型
     */
    protected Integer legalPersonCertificateType;

    /**
     * 法人身份证年龄
     */
    protected Integer legalIdCardAge;

    /**
     * 结算人身份证年龄—
     */
    protected Integer settlementIdCardAge;


    /**
     * 门店省  多个逗号隔开  ********,*********,4343434  比较只可以用in， not in
     */
    protected String storeProvinces;

    /**
     * 门店市   多个逗号隔开  ********,*********,4343434  比较只可以用in， not in
     */
    protected String storeCities;

    /**
     * 入网场景 业务开通/通道切换
     */
    protected String netInScene;


    /**
     * 额外特征
     */
    protected ExtraFeature extraFeature;

    @Data
    public static class ExtraFeature {

        protected MerchantBusinessLicenseInfo merchantBusinessLicenseInfo;

        protected AccountInfo accountInfo;

        @Data
        public static class AccountInfo {

            protected Integer accountType;

            protected String identityId;

            protected String holderName;
        }
    }
}
