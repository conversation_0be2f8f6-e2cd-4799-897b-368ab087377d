package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ProviderTerminal {

    public static final Integer OFFLINE = 0;
    public static final Integer ONLINE = 1;

    private Long id;

    private String merchant_sn;

    private String store_sn;

    private String terminal_sn;

    private String terminal_appid;

    private Integer provider;

    private String provider_terminal_id;
    /**
     * 收单机构商户号
     */
    private String acquirer_merchant_id;

    private Integer type;

    private Date create_at;

    private Date update_at;

    private String bound_sub_mch_ids;

}