package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.MerchantLakalaContract;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: HP
 * @Date: 2019/4/14
 * @Description:
 */

@JsonRpcService("/rpc/merchantLakalaContract")
@Validated
public interface MerchantLakalaContractService {

   List<MerchantLakalaContract> selectByParams(@NotBlank(message="merchant_sn不能为空")String merchant_sn,String contract_id, @NotNull(message="opt_type不能为null")int opt_type);


   
}
