package com.wosai.upay.job.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 进件报备规则组策略组合detail dto 对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel("进件报备规则组策略组合detail dto 对象")
public class GroupCombinedStrategyDetailRspDTO {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 收单组策略id，对应group_combined_strategy.id
     */
    @ApiModelProperty("收单组策略id")
    private Long groupStrategyId;
    /**
     * 规则组id，对应mc_rule_group.group_id
     */
    @ApiModelProperty("规则组id")
    private String groupId;
    /**
     * 收单机构
     */
    @ApiModelProperty("收单机构")
    private String acquirer;
    /**
     * 报备规则组类型 1-主规则组 2-备用规则组
     */
    @ApiModelProperty("报备规则组类型 1-主规则组 2-备用规则组")
    private Integer groupType;
    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;
    /**
     * 每日限制额度
     */
    @ApiModelProperty("每日限制额度")
    private Integer dailyLimitQuota;
    /**
     * 当日已使用额度
     */
    @ApiModelProperty("当日已使用额度")
    private Integer dailyUsageQuota;
    /**
     * 负载比例
     */
    @ApiModelProperty("负载比例")
    private BigDecimal loadRatio;
    /**
     * 有效状态 0-失效 1-生效
     */
    @ApiModelProperty("有效状态 0-失效 1-生效")
    private Integer validStatus;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

}

