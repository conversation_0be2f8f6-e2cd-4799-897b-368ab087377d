package com.wosai.upay.job.model.dto.request;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 饭卡支付配置交易参数请求参数
 *
 * <AUTHOR>
 * @date 2025/5/23 13:39
 */
@Data
public class FoodCardConfigTradeParamReqDTO {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    /**
     * partnerId
     */
    private String partnerId;

    /**
     * 商户id
     */
    @NotBlank(message = "商户id不能为空")
    private String providerMchId;

    /**
     * 二级账户
     */
    @NotBlank(message = "二级账户不能为空")
    private String providerMchSubId;

    /**
     * 代扣协议号
     */
    @NotBlank(message = "代扣协议号不能为空")
    private String providerAgreementNo;

}
