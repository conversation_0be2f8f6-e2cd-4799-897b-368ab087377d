package com.wosai.upay.job.model.direct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/12/25
 */
@Data
@Accessors(chain = true)
public class AliAuthorizeCallBack {

    @JsonProperty("batch_no")
    private String batchNo;

    @JsonProperty("app_auth_token")
    private String appAuthToken;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("re_expires_in")
    private long reExpiresIn;

    @JsonProperty("auth_time")
    private long authTime;

    @JsonProperty("app_refresh_token")
    private String appRefreshToken;

    @JsonProperty("auth_app_id")
    private String authAppId;

    @JsonProperty("app_id")
    private String appId;

    @JsonProperty("expires_in")
    private long expiresIn;

    @JsonProperty("app_auth_code")
    private String appAuthCode;
}
