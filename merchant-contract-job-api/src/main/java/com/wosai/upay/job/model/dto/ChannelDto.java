package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.upay.job.validation.group.UpdateChannel;
import lombok.Data;
import lombok.ToString;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * ChannelDto
 *
 * <AUTHOR>
 * @date 2019-08-01 19:51
 */
@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChannelDto implements Serializable {

    private static final long serialVersionUID = 1889050931523758117L;

    private Integer id;

    @NotBlank(message = "报备通道唯一标识不能为空", groups = UpdateChannel.class)
    private String channel;

    @NotBlank(message = "当前仅支持更新渠道名称，所以渠道名称不能为空", groups = UpdateChannel.class)
    private String name;

    private String subject;

    private Integer payway;

    private String payway_channel_no;

    private String provider;

    private String acquirer;

    private Date create_at;

    private Date update_at;

    private String channel_no;

    private String private_key;

    private String provider_metadata;

    private Map<String, Object> provider_metadata_map;

    private String acquirer_metadata;

    private Map<String, Object> acquirer_metadata_map;
}