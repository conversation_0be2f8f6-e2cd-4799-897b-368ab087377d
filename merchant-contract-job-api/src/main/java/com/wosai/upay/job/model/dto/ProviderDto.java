package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * ProviderDto
 *
 * <AUTHOR>
 * @date 2019-07-17 10:33
 */
@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
public class ProviderDto {
    private Integer id;

    private String provider;

    private String name;

    @JsonProperty("create_at")
    private Date createAt;

    @JsonProperty("update_at")
    private Date updateAt;

    private List<Payway> metadata;

    @Data
    public static class Payway {
        private Integer payway;
        private String name;
        List<Subpayway> subpayways;
    }

    @Data
    public static class Subpayway {
        private Integer subpayway;
        private String name;
    }
}
