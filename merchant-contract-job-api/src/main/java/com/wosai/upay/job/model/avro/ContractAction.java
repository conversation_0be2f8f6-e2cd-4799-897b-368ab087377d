/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.model.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class ContractAction extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -1256745462541032453L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"ContractAction\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"ctime\",\"type\":[\"string\",\"null\"],\"meta\":\"进件任务创建时间\"},{\"name\":\"mtime\",\"type\":[\"string\",\"null\"],\"meta\":\"进件任务最新状态变更时间\"},{\"name\":\"type\",\"type\":[\"string\",\"null\"],\"meta\":\"进件任务类型（新增商户入网、微信升级认证等，剔除附件上传事件）\"},{\"name\":\"status\",\"type\":[\"string\",\"null\"],\"meta\":\"进件状态（提交、失败、成功）\"},{\"name\":\"message\",\"type\":[\"string\",\"null\"],\"meta\":\"上游返回的原始文案，根据不同机构返回的信息提取文案部分\"},{\"name\":\"TrsMemo\",\"type\":[\"string\",\"null\"],\"meta\":\"配置的转译文案\"},{\"name\":\"provider\",\"type\":[\"null\",\"string\"],\"default\":null,\"meta\":\"支付通道\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<ContractAction> ENCODER =
      new BinaryMessageEncoder<ContractAction>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<ContractAction> DECODER =
      new BinaryMessageDecoder<ContractAction>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<ContractAction> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<ContractAction> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<ContractAction>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this ContractAction to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a ContractAction from a ByteBuffer. */
  public static ContractAction fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence ctime;
  @Deprecated public java.lang.CharSequence mtime;
  @Deprecated public java.lang.CharSequence type;
  @Deprecated public java.lang.CharSequence status;
  @Deprecated public java.lang.CharSequence message;
  @Deprecated public java.lang.CharSequence TrsMemo;
  @Deprecated public java.lang.CharSequence provider;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public ContractAction() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param ctime The new value for ctime
   * @param mtime The new value for mtime
   * @param type The new value for type
   * @param status The new value for status
   * @param message The new value for message
   * @param TrsMemo The new value for TrsMemo
   * @param provider The new value for provider
   */
  public ContractAction(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.CharSequence ctime, java.lang.CharSequence mtime, java.lang.CharSequence type, java.lang.CharSequence status, java.lang.CharSequence message, java.lang.CharSequence TrsMemo, java.lang.CharSequence provider) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.ctime = ctime;
    this.mtime = mtime;
    this.type = type;
    this.status = status;
    this.message = message;
    this.TrsMemo = TrsMemo;
    this.provider = provider;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return ctime;
    case 3: return mtime;
    case 4: return type;
    case 5: return status;
    case 6: return message;
    case 7: return TrsMemo;
    case 8: return provider;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: ctime = (java.lang.CharSequence)value$; break;
    case 3: mtime = (java.lang.CharSequence)value$; break;
    case 4: type = (java.lang.CharSequence)value$; break;
    case 5: status = (java.lang.CharSequence)value$; break;
    case 6: message = (java.lang.CharSequence)value$; break;
    case 7: TrsMemo = (java.lang.CharSequence)value$; break;
    case 8: provider = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public java.lang.CharSequence getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(java.lang.CharSequence value) {
    this.ctime = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.CharSequence getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.CharSequence value) {
    this.mtime = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public java.lang.CharSequence getType() {
    return type;
  }

  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(java.lang.CharSequence value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.CharSequence getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.CharSequence value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'message' field.
   * @return The value of the 'message' field.
   */
  public java.lang.CharSequence getMessage() {
    return message;
  }

  /**
   * Sets the value of the 'message' field.
   * @param value the value to set.
   */
  public void setMessage(java.lang.CharSequence value) {
    this.message = value;
  }

  /**
   * Gets the value of the 'TrsMemo' field.
   * @return The value of the 'TrsMemo' field.
   */
  public java.lang.CharSequence getTrsMemo() {
    return TrsMemo;
  }

  /**
   * Sets the value of the 'TrsMemo' field.
   * @param value the value to set.
   */
  public void setTrsMemo(java.lang.CharSequence value) {
    this.TrsMemo = value;
  }

  /**
   * Gets the value of the 'provider' field.
   * @return The value of the 'provider' field.
   */
  public java.lang.CharSequence getProvider() {
    return provider;
  }

  /**
   * Sets the value of the 'provider' field.
   * @param value the value to set.
   */
  public void setProvider(java.lang.CharSequence value) {
    this.provider = value;
  }

  /**
   * Creates a new ContractAction RecordBuilder.
   * @return A new ContractAction RecordBuilder
   */
  public static ContractAction.Builder newBuilder() {
    return new ContractAction.Builder();
  }

  /**
   * Creates a new ContractAction RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new ContractAction RecordBuilder
   */
  public static ContractAction.Builder newBuilder(ContractAction.Builder other) {
    return new ContractAction.Builder(other);
  }

  /**
   * Creates a new ContractAction RecordBuilder by copying an existing ContractAction instance.
   * @param other The existing instance to copy.
   * @return A new ContractAction RecordBuilder
   */
  public static ContractAction.Builder newBuilder(ContractAction other) {
    return new ContractAction.Builder(other);
  }

  /**
   * RecordBuilder for ContractAction instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<ContractAction>
    implements org.apache.avro.data.RecordBuilder<ContractAction> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence ctime;
    private java.lang.CharSequence mtime;
    private java.lang.CharSequence type;
    private java.lang.CharSequence status;
    private java.lang.CharSequence message;
    private java.lang.CharSequence TrsMemo;
    private java.lang.CharSequence provider;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(ContractAction.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.ctime)) {
        this.ctime = data().deepCopy(fields()[2].schema(), other.ctime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.mtime)) {
        this.mtime = data().deepCopy(fields()[3].schema(), other.mtime);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.type)) {
        this.type = data().deepCopy(fields()[4].schema(), other.type);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.status)) {
        this.status = data().deepCopy(fields()[5].schema(), other.status);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.message)) {
        this.message = data().deepCopy(fields()[6].schema(), other.message);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.TrsMemo)) {
        this.TrsMemo = data().deepCopy(fields()[7].schema(), other.TrsMemo);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.provider)) {
        this.provider = data().deepCopy(fields()[8].schema(), other.provider);
        fieldSetFlags()[8] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing ContractAction instance
     * @param other The existing instance to copy.
     */
    private Builder(ContractAction other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.ctime)) {
        this.ctime = data().deepCopy(fields()[2].schema(), other.ctime);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.mtime)) {
        this.mtime = data().deepCopy(fields()[3].schema(), other.mtime);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.type)) {
        this.type = data().deepCopy(fields()[4].schema(), other.type);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.status)) {
        this.status = data().deepCopy(fields()[5].schema(), other.status);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.message)) {
        this.message = data().deepCopy(fields()[6].schema(), other.message);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.TrsMemo)) {
        this.TrsMemo = data().deepCopy(fields()[7].schema(), other.TrsMemo);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.provider)) {
        this.provider = data().deepCopy(fields()[8].schema(), other.provider);
        fieldSetFlags()[8] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public ContractAction.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public ContractAction.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public ContractAction.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public ContractAction.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public java.lang.CharSequence getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public ContractAction.Builder setCtime(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.ctime = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public ContractAction.Builder clearCtime() {
      ctime = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.CharSequence getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public ContractAction.Builder setMtime(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.mtime = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public ContractAction.Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public java.lang.CharSequence getType() {
      return type;
    }

    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public ContractAction.Builder setType(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.type = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public ContractAction.Builder clearType() {
      type = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.CharSequence getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public ContractAction.Builder setStatus(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.status = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public ContractAction.Builder clearStatus() {
      status = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'message' field.
      * @return The value.
      */
    public java.lang.CharSequence getMessage() {
      return message;
    }

    /**
      * Sets the value of the 'message' field.
      * @param value The value of 'message'.
      * @return This builder.
      */
    public ContractAction.Builder setMessage(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.message = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'message' field has been set.
      * @return True if the 'message' field has been set, false otherwise.
      */
    public boolean hasMessage() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'message' field.
      * @return This builder.
      */
    public ContractAction.Builder clearMessage() {
      message = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'TrsMemo' field.
      * @return The value.
      */
    public java.lang.CharSequence getTrsMemo() {
      return TrsMemo;
    }

    /**
      * Sets the value of the 'TrsMemo' field.
      * @param value The value of 'TrsMemo'.
      * @return This builder.
      */
    public ContractAction.Builder setTrsMemo(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.TrsMemo = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'TrsMemo' field has been set.
      * @return True if the 'TrsMemo' field has been set, false otherwise.
      */
    public boolean hasTrsMemo() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'TrsMemo' field.
      * @return This builder.
      */
    public ContractAction.Builder clearTrsMemo() {
      TrsMemo = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'provider' field.
      * @return The value.
      */
    public java.lang.CharSequence getProvider() {
      return provider;
    }

    /**
      * Sets the value of the 'provider' field.
      * @param value The value of 'provider'.
      * @return This builder.
      */
    public ContractAction.Builder setProvider(java.lang.CharSequence value) {
      validate(fields()[8], value);
      this.provider = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'provider' field has been set.
      * @return True if the 'provider' field has been set, false otherwise.
      */
    public boolean hasProvider() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'provider' field.
      * @return This builder.
      */
    public ContractAction.Builder clearProvider() {
      provider = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public ContractAction build() {
      try {
        ContractAction record = new ContractAction();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.ctime = fieldSetFlags()[2] ? this.ctime : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.mtime = fieldSetFlags()[3] ? this.mtime : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.type = fieldSetFlags()[4] ? this.type : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.status = fieldSetFlags()[5] ? this.status : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.message = fieldSetFlags()[6] ? this.message : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.TrsMemo = fieldSetFlags()[7] ? this.TrsMemo : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.provider = fieldSetFlags()[8] ? this.provider : (java.lang.CharSequence) defaultValue(fields()[8]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<ContractAction>
    WRITER$ = (org.apache.avro.io.DatumWriter<ContractAction>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<ContractAction>
    READER$ = (org.apache.avro.io.DatumReader<ContractAction>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
