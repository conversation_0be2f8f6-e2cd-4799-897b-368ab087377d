package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Data
@Accessors(chain = true)
public class HxbParamsV2 extends HxbParams{

    /**
     * 费率
     */
    @NotBlank(message = "fee_rate 不能为空")
    private String fee_rate;

    /**
     * 证件号结尾4位
     */
    @NotBlank(message = "identify_end 不能为空")
    private String identify_end;

    /**
     * 银行卡持有人
     */
    @NotBlank(message = "holder_name 不能为空")
    private String holder_name;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不为空")
    private String operate;

}
