package com.wosai.upay.job.model.payLater.ProcessEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 子商户号用途枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum SubStatusEnum {

    ZFT_APPLYING(0,"直付通商户申请中"),
    CONFIRMING(10,"待确认"),
    ZHIMA_APPLYING(20,"芝麻先享平台审核"),
    SUCCESS(30,"申请成功"),
    FAIL(40,"芝麻先享平台审核失败"),
    ;
    private int code;
    private String message;

    public static String getMessage(Integer code) {
        final SubStatusEnum[] ProcessStatusEnum = values();
        for (SubStatusEnum bankDirectApplyStatusEnum : ProcessStatusEnum) {
            if (Objects.equals(bankDirectApplyStatusEnum.getCode(), code)) {
                return bankDirectApplyStatusEnum.getMessage();
            }
        }
        return null;
    }
    public static void main(String[] args) {
        System.out.println(getMessage(80));
    }
}