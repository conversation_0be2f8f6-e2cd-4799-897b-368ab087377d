package com.wosai.upay.job.model.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 营业执照变更-回填金额校验请求
 *
 * <AUTHOR>
 * @date 2025/2/19 16:05
 */
@Data
public class LicenseUpdateAmountVerifyReqDTO {

    @JsonProperty("merchant_id")
    @NotBlank(message = "商户号不能为空")
    private String merchantId;

    /**
     * 业务申请id
     */
    @NotBlank(message = "业务申请编号不能为空")
    private String businessId;

    /**
     * 营业执照审批单id
     */
    @JsonProperty("field_app_info_id")
    @NotNull(message = "营业执照审批单编号不能为空")
    private Integer fieldAppInfoId;

    /**
     * 验证金额
     **/
    @NotNull(message = "验证金额不能为空")
    private BigDecimal amount;
}
