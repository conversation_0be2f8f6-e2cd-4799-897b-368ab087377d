package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 院校通参数导入
 *
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@Accessors(chain = true)
public class HopeEduParams {

    /**
     * 收钱吧商户id
     * 因为院校通没有收单机构商户号，通过终端来区分的，所以把收钱吧168商户号也当成院校通商户号
     */
    @NotBlank(message = "merchantId不能为空")
    private String merchantId;

    /**
     * 0：关闭新希望参数  1：打开新希望参数
     */
    @Min(value = 0, message = "b2cStatus只能是0或者1")
    @Max(value = 1, message = "b2cStatus只能是0或者1")
    private int b2cStatus;

}
