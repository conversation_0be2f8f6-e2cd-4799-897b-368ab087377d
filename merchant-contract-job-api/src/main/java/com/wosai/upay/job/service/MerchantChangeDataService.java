package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.changeMerchantData.ApplyChangeMerchantDataReq;
import com.wosai.upay.job.model.changeMerchantData.CommonResp;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

@JsonRpcService("/rpc/merchantChangeData")
@Validated
public interface MerchantChangeDataService {
    /**
     * 发起商户信息变更,(入网后真实性审核驳回)
     *
     */
    CommonResp applyChangeMerchantData(@Valid ApplyChangeMerchantDataReq params);


    /**
     * 查询商户信息变更任务状态接口
     *
     * @return null没有相关任务  1处理中  2成功  3失败
     */
    Integer queryMerchantChangeDataTaskStatus(@NotEmpty(message = "商户号不可为空") String merchantSn);

    /**
     * 提交微信认证授权任务,
     *
     * @return
     */
    CommonResp submitAuthAndComboTask(@NotEmpty(message = "商户号不可为空") String merchantSn, Map bizParams);


    /**
     * 查询微信子商户号和切换套餐任务的状态
     *
     * @return null没有相关任务  1处理中  2成功  3失败
     */
    Integer queryAuthAndComboTaskStatus(@NotEmpty(message = "商户号不可为空") String merchantSn);


    /**
     * 查询微信授权任务文案接口
     *
     * @return {
     * "contract_code":"",
     * "contract_memo":""
     * <p>
     * (url  子商户号 ...)
     * }
     */
    Map<String, Object> queryAuthAndComboTaskMsgAndCode(@NotEmpty(message = "商户号不可为空") String merchantSn, @NotEmpty(message = "平台类型不可为空") String type);


    Boolean queryWeixinAuthStatus(@NotEmpty(message = "商户号不可为空") String merchantSn);
}
