package com.wosai.upay.job.model.onlinePayment;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Data
@Accessors(chain = true)
public class OnlinePaymentOpenCheckResp {

    /**
     * 1:校验通过   0：校验失败
     */
    private Integer checkResult;

    /**
     * 错误信息，校验失败时必传
     */
    private String checkFailMsg;

    public static OnlinePaymentOpenCheckResp success() {
        return new OnlinePaymentOpenCheckResp().setCheckResult(1);
    }

    public static OnlinePaymentOpenCheckResp fail(String message) {
        return new OnlinePaymentOpenCheckResp().setCheckResult(0).setCheckFailMsg(message);
    }
}
