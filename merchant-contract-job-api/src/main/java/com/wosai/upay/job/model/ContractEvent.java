package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ContractEvent {

    //============= 状态 =============
    /**
     * 待处理
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 1;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 5;

    /**
     * 业务失败
     */
    public static final int STATUS_BIZ_FAIL = 6;

    //============= 类型 =============

    /**
     * 商户基本信息变更
     */
    public static final int OPT_TYPE_MERCHANT_BASIC_INFORMATION = 0;

    /**
     * 结算账户信息变更
     */
    public static final int OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS = 1;

    /**
     * 商户费率信息更新
     */
    public static final int OPT_TYPE_MERCHANT_FEERATE = 2;

    /**
     * 附件上传
     */
    public static final int OPT_TYPE_ATTACHMENT_UPLOADING = 3;

    /**
     * 入网
     */
    public static final int OPT_TYPE_NET_IN = 4;

    /**
     * 微信实名失败重新提交
     * 类型已废弃
     */
    @Deprecated
    public static final int OPT_TYPE_RE_AUTH = 8;

    /**
     * 重新入网
     */
    public static final int OPT_TYPE_NET_CRM_UPDATE = 9;

    /**
     * 失败，这种类型的入网事件，直接生成失败的 Task
     */
    public static final int OPT_TYPE_FAIL = 10;
    /**
     * 更新营业执照
     */
    public static final int OPT_TYPE_UPDATE_BUSINESS_LICENSE = 11;

    /**
     * 商户信息变更 --> 行业变更与支付业务联动
     */
    public static final int OPT_TYPE_UPDATE_MERCHANT_DATA = 12;

    /**
     * 是否强制更新 商户信息变更强制更新lkl
     */
    public static final String FORCE_UPDATE = "force_update";

    /**
     * 业务参数
     */
    public static final String FORM_BODY = "form_body";


    private Long id;

    private Long task_id;

    private String merchant_sn;

    // fine TODO 枚举
    private Integer event_type;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String event_msg;

    private String result;

    /**
     * 报备规则组id
     */
    private String rule_group_id;

}