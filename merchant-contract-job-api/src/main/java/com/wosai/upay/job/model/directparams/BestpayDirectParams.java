package com.wosai.upay.job.model.directparams;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 翼支付直连参数
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Data
public class BestpayDirectParams extends BaseParams {

    private BestpayParams bestpay_trade_params;

    @Data
    public static class BestpayParams extends BaseTradeParams {

        /**
         * 翼支付商户号
         */
        private String merchant_id;

        /**
         * 翼支付商户交易密钥
         */
        private String merchant_key;

        /**
         * 翼支付商户交易密码
         */
        private String merchant_pwd;

        /**
         * 翼支付子商户号
         */
        private String sub_merchant_id;

        /**
         * 翼支付门店id
         */
        private String store_id;

        /**
         * 是否支持分账
         */
        private String ledger_detail;

        @Override
        public boolean empty() {
            return StringUtils.isEmpty(merchant_id) &&
                    StringUtils.isEmpty(merchant_key) &&
                    StringUtils.isEmpty(merchant_pwd) &&
                    StringUtils.isEmpty(sub_merchant_id) &&
                    StringUtils.isEmpty(store_id) &&
                    StringUtils.isEmpty(ledger_detail);
        }

    }
}
