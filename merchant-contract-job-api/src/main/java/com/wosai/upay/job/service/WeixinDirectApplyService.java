package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.direct.*;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@JsonRpcService("/rpc/weixinDirectApply")
@Validated
public interface WeixinDirectApplyService {

    /**
     * 申请微信直连
     * @param weixinDirectReq 开通请求
     * @return 是否提交成功
     */
    ContractResponse applyWeixinDirectPay(@Valid WeixinDirectReq weixinDirectReq);

    /**
     * 获取申请单状态 商户主动查询 给外部使用，有频率限制
      * @param merchantSn 商户号
     * @param devCode 应用参数
     * @param platform 查询平台
     * @return 状态和文案
     */
    ApplyStatusResp queryApplyStatus(@NotBlank(message = "商户号不能为空") String merchantSn,
                                     @NotBlank(message = "应用标识不能为空") String devCode,
                                     @NotBlank(message = "平台不能为空") String platform);

    /**
     * 获取申请单的状态和文案 给内部使用
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @param platform 平台
     * @return 状态和文案
     */
    ApplyStatusResp getApplyStatusByType(@NotBlank(message = "商户号不能为空") String merchantSn,
                                         @NotBlank(message = "应用标识不能为空") String devCode,
                                         @NotBlank(message = "平台不能为空") String platform);

    /**
     * 根据申请单的次数判断是否允许转人工
     * @param merchantSn 商户号
     * @param devCode 应用标识
     * @return 是否允许
     */
    ContractResponse allowApplyToManual(@NotBlank(message = "商户号不能为空") String merchantSn,
                                        @NotBlank(message = "应用标识不能为空") String devCode);

    /**
     * 申请转人工
     * @param weixinDirectReq 此次申请转人工的申请
     * @return 是否提交成功
     */
    ContractResponse applyWeixinDirectPayToManual(@Valid WeixinDirectReq weixinDirectReq);

    /**
     * 运营审核通过转人工的申请单 并更改状态
     * @param req 参数
     * @return 提交结果
     */
    ContractResponse changeWeixinDirectApplyStatus(@Valid ChangeApplyStatusReq req);

    /**
     * 绑定已有商户
     * @param bindMerchantReq 请求
     * @return 提交结果
     */
    ContractResponse bindMerchantWeixinDirectPay(@Valid BindMerchantReq bindMerchantReq);

    /**
     * 根据行业id查询微信直连结算id等信息
     * @param industryId 行业id
     * @return 结算id等信息
     */
    WeixinDirectIndustry getWeixinDirectIndustry(@NotBlank(message = "行业id不能为空") String industryId);

    /**
     * 根据taskId获取申请单
     * @param taskId 报备task的id
     * @return 返回值
     */
    WeixinDirectApplyDto getDirectApplyByTaskId(@NotNull(message = "taskId不能为空") long taskId);

    /**
     * 根据商户号和应用开发者标识来获取微信直连申请单，给sp查详细状态
     * @param merchantSn 商户号
     * @param devCode 应用开发者标识
     * @return 申请单详细信息
     */
    WeixinDirectApplyDto getDirectApplyByMerchantSnAndDevCode(@NotBlank(message = "merchant_sn不能为空") String merchantSn, @NotBlank(message = "dev_code不能为空") String devCode);

}
