package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.merchant.contract.enume.LklPicTypeV3;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/3 14:54
 * @Description:拉卡拉回调服务(merchant_contract调用)
 */
@JsonRpcService("/rpc/lakalaCallBack")
@Validated
public interface LakalaCallBackService {


    ContractSubTask querySubTaskByContractId(@NotEmpty(message = "contractId不能为空") String contractId);

    Boolean updateContractSubTask(boolean success, @NotEmpty(message = "contractId不能为空") String contractId, Map callbackMsg);

    Long savePicTaskByContract(@NotBlank(message = "merchantSn不能为空") String merchantSn, Map<String, Object> request);

    Long savePicTaskByRisk(@NotBlank(message = "merchantSn不能为空") String merchantSn, @NotNull Map<String, Object> request, @NotNull Integer status);

    //以下都是lklv3版本
    Boolean savePicTaskByRiskV3(@NotBlank(message = "商户号不能为空") String merchantSn,
                                @NotNull(message = "图片类型不能为空") LklPicTypeV3 type,
                                @NotBlank(message = "图片地址不能为空") String url);


    Map contractV3CallBack(@NotNull(message = "message不能为空") Map callbackMsg);

}