package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.upay.job.model.InitBankAcquireInfoDTO;
import com.wosai.upay.job.model.McAcquirerChangeDTO;
import com.wosai.upay.job.model.acquirer.ChangeAcquirerRequest;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.model.acquirer.ClearParam;
import com.wosai.upay.job.model.acquirer.SyncMchStatusResp;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.model.dto.request.BankAutoChangeToThirdPartyReqDTO;
import com.wosai.upay.job.model.dto.request.ChangeAcquirerReqDTO;
import com.wosai.upay.job.model.dto.request.MerchantFeatureReqDTO;
import com.wosai.upay.job.model.dto.response.AcquirerChangeTaskRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 用来处理收单机构相关逻辑，比如判断商户正在生效的收单机构，商户收单机构切换等
 *
 * <AUTHOR>
 * @date 2019-09-12
 */
@JsonRpcService("/rpc/acquirer")
@Validated
public interface AcquirerService {

    /**
     * 获取商户生效收单机构
     *
     * @param merchantSn
     * @return lkl 或者 tonglian
     */
    String getMerchantAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * 获取商户当前收单机构的名称
     * @param merchantSn
     * @return 拉卡拉、支付宝、微信、翼支付、上海兴业、通联、索迪斯、邮储银行、银联商务、广发银行、建设银行、华夏银行、工商银行、通联收银宝、海科、富友、平安银行、泰隆银行、福建农信社、江苏银行、民生银行、泸州银行、国通星驿、中投科信
     */
    String getMerchantAcquirerName(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * 获取商户当前收单机构的清算参数
     *
     * @param merchantSn
     * @return
     */
    ClearParam getClearParam(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * 获取商户指定收单机构的清算参数
     *
     * @param merchantSn
     * @return
     */
    ClearParam getClearParamByAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "收单机构不能为空") String acquirer);

    /**
     * 获取直连商户的支付源商户号
     *
     * @param merchantSn 收钱吧商户号
     * @param payway     支付方式 2支付宝 3微信
     * @return 支付源商户号
     */
    String getDirectPayMerchantId(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                  @NotNull(message = "支付方式不能为空") Integer payway);

    /**
     * 提交切换收单机构申请
     *
     * @param merchantSn
     * @param acquirer   McConstant.ACQUIRER_LKL  或者  McConstant.ACQUIRER_TONGLIAN
     * @return
     */
    boolean applyChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                @NotEmpty(message = "收单机构不能为空") String acquirer);


    /**
     * 提交切换收单机构申请,注意此接口默认修改移动支付业务相关信息
     *
     * @param merchantSn
     * @param acquirer    McConstant.ACQUIRER_LKL  或者  McConstant.ACQUIRER_TONGLIAN
     * @param immediately true-立刻切换,false-夜晚切换
     * @return
     */
    boolean applyChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                @NotEmpty(message = "收单机构不能为空") String acquirer,
                                @NotNull(message = "是否立即切换收单机构不能为空") Boolean immediately);

    /**
     * 是否存在未完成的切换收单机构申请
     *
     * @param merchantSn 商户号
     * @return 是否存在 true-存在
     */
    boolean existedChangeAcquirerNotFinish(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * 查询最近一次未完成的切换收单机构申请
     * @param merchantSn 商户号
     * @return 申请单，如果不存在则返回 null
     */
    McAcquirerChangeDTO queryLatestNotFinishChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn);

    /**
     * 根据申请单号查询申请单
     * @param applyId 申请单号
     * @return 申请单，如果不存在则返回 null
     */
    McAcquirerChangeDTO queryMcAcquirerChangeApplyByApplyId(@NotEmpty(message = "申请单号不能为空") String applyId);
    /**
     * 提交切换收单机构申请
     *
     * @param merchantSn
     * @param acquirer    McConstant.ACQUIRER_LKL  或者  McConstant.ACQUIRER_TONGLIAN
     * @param immediately true-立刻切换,false-夜晚切换
     * @param tradeAppId  业务方appId,如果不传不可以使用
     * @return
     * @see AcquirerService#applyChangeAcquirer(java.lang.String, java.lang.String, java.lang.Boolean) 默认使用移动支付业务tradeAppId
     */
    boolean applyChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                @NotEmpty(message = "收单机构不能为空") String acquirer,
                                @NotNull(message = "是否立即切换收单机构不能为空") Boolean immediately,
                                @NotBlank(message = "业务方不能为空") String tradeAppId);


    /**
     * 提交切换收单机构申请，并指定是否可以取消
     *
     * @param  request 包含所有参数的请求对象
     * @return
     * @see AcquirerService#applyChangeAcquirer(java.lang.String, java.lang.String, java.lang.Boolean) 默认使用移动支付业务tradeAppId
     */
    boolean applyChangeAcquirer(@Valid ChangeAcquirerRequest request);




    /**
     * 根据id查询切换收单机构任务
     *
     * @param taskId 任务id
     * @return 切换收单机构任务
     */
    AcquirerChangeTaskRspDTO getAcquirerChangeTaskById(@NotNull(message = "任务id不能为空") Integer taskId);


    /**
     * 提交切换收单机构任务
     * 一级模板code: 22AL331U8SVS
     *
     * @param changeAcquirerReqDTO   切收单机构请求参数
     * @param logParamsDto           日志参数
     * @return 任务id
     */
    Integer submitChangeAcquirerTask(@Valid ChangeAcquirerReqDTO changeAcquirerReqDTO,
                                    @NotNull(message = "日志参数不能为空") LogParamsDto logParamsDto);

    /**
     * 检查是否能够切换到目标机构,注意此接口默认校验移动支付业务相关信息
     *
     * @param merchantSn
     * @param acquirer   McConstant.ACQUIRER_LKL  或者  McConstant.ACQUIRER_TONGLIAN
     * @return
     */
    CheckChangeAcquirerResp checkChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                                @NotEmpty(message = "收单机构不能为空") String acquirer);

    /**
     * 检查是否能够切换到目标机构
     *
     * @param merchantSn
     * @param acquirer   McConstant.ACQUIRER_LKL  或者  McConstant.ACQUIRER_TONGLIAN
     * @param tradeAppId 业务方appId 支持指定,如果不指定则不可以使用
     * @return
     * @see AcquirerService#checkChangeAcquirer(java.lang.String, java.lang.String) 默认使用支付业务tradeAppId
     */
    CheckChangeAcquirerResp checkChangeAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn,
                                                @NotEmpty(message = "收单机构不能为空") String acquirer,
                                                @NotBlank(message = "业务方不能为空") String tradeAppId);

    /**
     * 将商户状态同步到收单机构
     *
     * @param merchantSn
     * @param status     0：关闭  1：正常  2：禁用
     * @return
     */
    SyncMchStatusResp syncMchStatusToAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotNull(message = "商户状态为空") int status);

    /**
     * 同步商户状态到指定收单机构&同步指定收单机构下的子商户号状态
     *
     * @param merchantSn 商户号
     * @param status     0：关闭 1：正常 2：禁用
     * @param acquirer   收单机构
     * @return 同步结果
     */
    SyncMchStatusResp syncMchAndSubMchIdStatus(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotNull(message = "商户状态为空") int status, String acquirer);


    /**
     * 将商户状态同步到收单机构
     *
     * @param merchantSn
     * @param status     0：关闭  1：正常  2：禁用
     * @return
     */
    SyncMchStatusResp syncMchStatusToTargetAcquirer(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotNull(message = "商户状态为空") int status, String acquirer);

    /**
     * 通过审批提交切换收单机构申请
     *
     * @param dto 通过审批提交的信息
     * @return callBackBean  审批相关信息
     */
    CallBackBean applyChangeAcquirerByApprove(@Valid ChangeAcquirerApproveDTO dto);

    /**
     * 修改contract_status中商户收单机构
     *
     * @param merchantSn
     * @param acquirer
     */
    void updateMerchantAcquirer(String merchantSn, String acquirer);

    /**
     * 通联商户切换到拉卡拉前置检查
     * 【【【【临时使用，过后删除】】】】
     *
     * @param merchantSn
     */
    void changeToLklCheck(String merchantSn);

    /**
     * 通联商户切换到拉卡拉
     * 【【【【临时使用，过后删除】】】】
     *
     * @param merchantSn
     */
    void changeToLkl(String merchantSn);

    /**
     * 通联商户强制切换到拉卡拉
     * 【【【【临时使用，过后删除】】】】
     *
     * @param merchantSn
     */
    void forceChangeToLkl(String merchantSn);



    /**
     * 根据收单机构获取费率
     * @param merchantSn
     */
    List<Map> getFeeRateByAcquirer(String merchantSn, String acquirer);

    /**
     * 银行自动切换到三方收单机构
     *
     * @param reqDTO 请求参数
     * @return 是否成功 true-成功
     */
    boolean bankAutoChangeToThirdParty(@Valid BankAutoChangeToThirdPartyReqDTO reqDTO);

    /**
     * 校验商户是否可以入指定的groupId(mc_rule_group.group_id)
     *
     * @param merchantFeature  商户特征
     * @param groupId          规则组id(mc_rule_group.group_id)
     * @param acquirerTypeEnum 收单机构
     * @return 校验结果 true-可以入 false-不可以入
     */
    boolean checkEligibilityToGroupId(MerchantFeatureReqDTO merchantFeature, String groupId, AcquirerTypeEnum acquirerTypeEnum);

    /**
     * 获取商户所在收单机构的商户状态
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 商户状态
     */
    AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn, String acquirer);



    /**
     * 银行通用参数导入时初始化相关信息
     * @param info 相关信息
     */
    void initBankAcquireData(@Valid InitBankAcquireInfoDTO info);


    /**
     * 设置商户为立即切换收单机构
     * @param merchantSn 商户号
     */
    void setImmediately(@NotBlank(message = "商户号不能为空") String merchantSn);
}
