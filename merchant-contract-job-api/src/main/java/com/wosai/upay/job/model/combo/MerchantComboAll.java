package com.wosai.upay.job.model.combo;

import com.wosai.trade.service.result.TradeAppResult;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.trade.service.result.TradeComboResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MerchantComboAll extends MerchantComboParam {
    private MerchantProviderParamsDto merchantProviderParamsDto;
    private TradeAppResult tradeAppResult;
    private TradeComboResult tradeComboResult;
    private TradeComboDetailResult tradeComboDetailResult;
}
