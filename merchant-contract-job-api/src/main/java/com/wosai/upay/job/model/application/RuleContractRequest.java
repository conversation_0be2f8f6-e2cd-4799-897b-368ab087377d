package com.wosai.upay.job.model.application;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/3/13 12:23 下午
 **/
@Data
@Accessors(chain = true)
public class RuleContractRequest {

    @NotEmpty(message = "规则不能为空")
    private String rule;
    private String merchantSn;
    private String merchantId;
    /**
     * 是否允许重新报备 默认不允许
     * 是否允许统一规则下报备多次
     */
    private boolean reContract = false;

    /**
     * 报备完成后是否配置交易参数 默认配置
     */
    private boolean configParam = true;
    /**
     * configParam为true时交易参数默认配置到merchant_config terminals不为空时则配置到对应的terminal_config中
     **/
    private List<String> terminals = new ArrayList<>();
    /**
     * configParam为true才会有用
     */
    private String fee;
    /**
     * 调用方名称
     */
    @NotEmpty(message = "调用方不能为空")
    private String plat;
    /**
     * 使用于支付源微信  是否配置子商户号(0是 1否)  默认自动配置
     **/
    private int weixinSubdevConfigStatus = 0;

    private Map<String, String> customFields;

    /**
     * 报备微信子商户号是否强制按小微报备
     */
    private boolean forceMicro = false;
}
