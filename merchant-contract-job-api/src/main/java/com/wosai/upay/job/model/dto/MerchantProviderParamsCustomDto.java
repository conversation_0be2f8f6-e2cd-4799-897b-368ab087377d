package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * MerchantProviderParamsCustomDto
 *
 * <AUTHOR>
 * @date 2019-08-05 18:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantProviderParamsCustomDto extends MerchantProviderParamsDto {

    private String merchant_name;
    private String merchant_alias;

    private String channel;
    private String channel_name;

    private String acquirer;
    private String acquirer_name;
    private Integer clear_type;

    private String provider_name;

    private String sub_payway;

    /**
     * 银联开放平台商户号
     */
    private String unionOpenSystem;


    /**
     * 多业务生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subEffectTime;

}
