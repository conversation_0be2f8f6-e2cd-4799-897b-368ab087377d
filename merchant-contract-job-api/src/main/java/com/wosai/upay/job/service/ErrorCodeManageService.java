package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.response.ErrorInfoPromptTextRspDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 错误码管理服务
 *
 * <AUTHOR>
 * @date 2024/7/22 11:38
 */
@JsonRpcService("/rpc/error-code-manage")
@Validated
public interface ErrorCodeManageService {

    /**
     * 根据原始错误信息获取对接平台的提示文案
     *
     * @param viewEndpoint 文案要被哪端(crm,SP,app,consumer ErrorMsgViewEndpointTypeEnum)看到
     * @param originalMsg  原始错误信息
     * @param platform     原始错误信息来自哪一个对接平台
     * @return 提示文案
     */
    ErrorInfo getPromptMessage(@NotBlank(message = "展示端不可以为空") String viewEndpoint,
                               @NotBlank(message = "错误信息不可以为空") String originalMsg,
                               String platform);

    /**
     * 根据原始错误信息获取提示文案
     *
     * @param originalMsg  原始错误信息
     * @param platform     原始错误信息来自哪一个对接平台
     * @return 提示文案列表
     */
    List<ErrorInfoPromptTextRspDTO> getAllEndPointPromptMessage(@NotBlank(message = "错误信息不可以为空") String originalMsg, String platform);
}
