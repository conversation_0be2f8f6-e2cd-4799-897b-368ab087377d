package com.wosai.upay.job.model.lklV3Pos;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/12/09 11:50
 */
@Data
@Accessors(chain = true)
public class LKlEcApplyStatusResp {

    /**当前电子合同状态
     * 0-不存在电子合同,需要进行申请
     * 1-人工审核中,
     * 2-人工审核通过,
     * 3-人工审核失败,
     * 4-签约地址过期,
     * 5-签约成功,
     * 6-签约失败
     */
    private String status;

    /**
     * 状态对应的中文含义
     */
    private String msg;


    /**
     * 2-人工审核通过时返回H5链接
     */
    private String url;


    /**
     * 存在的时候返回
     */
    private String ecApplyId;


    /**
     * 存在的时候返回
     */
    private Map feeMap;


    private Long tradeComboId;




}
