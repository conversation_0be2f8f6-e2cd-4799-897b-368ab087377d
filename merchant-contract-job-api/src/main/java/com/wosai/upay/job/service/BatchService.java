package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.model.dto.BatchProcessingDTO;
import com.wosai.upay.job.model.dto.ImportUmsParamsDTO;
import com.wosai.upay.job.model.dto.QueryAuthStatusDTO;
import com.wosai.upay.job.model.dto.QueryIsPostalCardDto;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author: jerry
 * @date: 2019/7/22 14:44
 * @Description:交易参数管理服务 提供sp批量报备 批量切交易参数功能
 */
@JsonRpcService("/rpc/batch")
@Validated
public interface BatchService {

    /**
     * 根据规则及上传文件生成批量任务
     *
     * @param: BatchResq
     * @return: BatchResp
     * @date: 15:16
     */
    @Validated(value = BatchResq.ByFile.class)
    BatchResp batchContract(@Valid BatchResq batchResq);

    /**
     * 根据规则及文件生成批量配置交易参数任务
     *
     * @param: url  oss文件key
     * @param: rule ContractRule rule字段
     * @return:
     * @date: 15:17
     */
    @Validated(value = {BatchResq.ByFile.class, BatchResq.ChangeChannel.class})
    BatchResp batchChangeByFile(@Valid BatchResq batchResq);

    /**
     * 根据规则生成批量配置交易参数任务
     *
     * @param: oldRule  原配置的规则
     * @param: rule  需要配置交易参数的规则
     * @return:
     * @date: 15:18
     */
    @Validated(value = {BatchResq.ByRule.class, BatchResq.ChangeChannel.class})
    BatchResp batchChangeByRule(@Valid BatchResq batchResq);

    /**
     * 批量导入支付宝跨城收款商户
     *
     * @param aliOnlineMerchantsBatchImportReq 请求参数
     * @return 返回结果
     */
    BatchResp batchImportAliOnlineMerchants(@Valid AliOnlineMerchantsBatchImportReq aliOnlineMerchantsBatchImportReq);


    /**
     * @param dto
     * <AUTHOR>
     * @Description: 通过审批切换收单机构
     * @time 16:03
     */
    void changAcquire(ChangeAcquirerApproveDTO dto);


    /**
     * 取消切换收单机构
     *
     * @param dto 通过审批提交的信息
     * @return
     */
    CallBackBean cancelChangAcquire(ChangeAcquirerApproveDTO dto);


    /**
     * 通过审批完成仅入网
     *
     * @param dto 通过审批提交的信息
     * @return
     */
    CallBackBean onlyContract(ChangeAcquirerApproveDTO dto);

    /**
     * 通过审批查询微信子商户号状态
     *
     * @param dto 通过审批提交的信息
     * @return callBackBean  审批相关信息
     */
    CallBackBean queryAuthStatus(QueryAuthStatusDTO dto);

    /**
     * 通过审批查询微信子商户号状态
     *
     * @param dto 通过审批提交的信息
     * @return callBackBean  审批相关信息
     */
    @Deprecated
    CallBackBean queryIsPostCard(QueryIsPostalCardDto dto);

    /**
     * 批量审批通用接口
     *
     * @param dto 通过审批提交的信息
     * @return callBackBean  审批相关信息
     */
    CallBackBean doBatchProcessing(BatchProcessingDTO dto);


    /**
     * 华夏银行线下进件，数据导入接口
     * <p>
     * 接口已废弃，空实现
     *
     * @param hxbParams
     */
    @Deprecated
    void importHxbParams(@Valid HxbParams hxbParams);


    /**
     * 华夏银行线下进件，数据导入接口
     *
     * @param hxbParams
     */
    void importHxbParamsV2(@Valid HxbParamsV2 hxbParams);

    /**
     * 工商银行线下进件，数据导入接口
     *
     * @param immediately true立即切换，false凌晨2点切换 废弃字段
     */
    void importIcbcParams(@Valid IcbcParams icbcParams, boolean immediately);

    /**
     * 补充 merchant_name, ali_mcc, wx_settlement_id
     *
     * @param merchantProviderParamsId
     */
    void fillPayAuthInfo(String merchantProviderParamsId);

    /**
     * 建行线下导入
     *
     * @param merchantSn
     * @param feeRate
     */
    void importCcb(@NotBlank(message = "商户号不能为空") String merchantSn, @NotBlank(message = "费率不能为空") String feeRate);


    /**
     * 华夏银行多业务线下进件，数据导入接口
     *
     * @param hxbParams
     */
    void importHxbMultiTradeParams(@Valid HxbParamsV2 hxbParams);


    /**
     * 平安银行参数导入
     *
     * @param pabParam
     */
    void importPab(@Valid PabParam pabParam);

    /**
     * 泰隆银行参数导入
     *
     * @param zjtlcbParams 泰隆银行商户参数
     */
    void importZjtlcb(@Valid ZjtlcbParams zjtlcbParams);

    /**
     * 银行通用参数导入
     *
     * @param row      每一行数据
     * @param operator 提交人
     */
    void commonImport(List<String> row, String operator);

    /**
     * 批量导入银商参数（按商户维度）
     *
     * @param importUmsParamsDTO
     */
    void importUmsParams(ImportUmsParamsDTO importUmsParamsDTO);

    /**
     * 批量导入银商参数（按门店维度）
     *
     * @param importUmsParamsDTO
     */
    void storeImportUmsParams(ImportUmsParamsDTO importUmsParamsDTO);

    /**
     * 配置院校通参数
     *
     * @param hopeEduParams
     */
    void configHopeEduParams(HopeEduParams hopeEduParams);


    /**
     * 间连参数通用导入
     *
     * @param row      每一行数据
     * @param operator 提交人
     */
    void indirectCommonImport(List<String> row, String operator);
}
