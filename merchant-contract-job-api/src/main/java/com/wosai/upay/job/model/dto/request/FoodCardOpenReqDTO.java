package com.wosai.upay.job.model.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 饭卡支付开通请求参数
 *
 * <AUTHOR>
 * @date 2025/5/23 13:39
 */
@Data
public class FoodCardOpenReqDTO {

    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    /**
     * 附件名称, 需要带文件后缀
     */
    @NotBlank(message = "附件名称不能为空")
    private String fileName;


    /**
     * 文件url
     */
    @NotBlank(message = "文件url不能为空")
    private String fileUrl;
}
