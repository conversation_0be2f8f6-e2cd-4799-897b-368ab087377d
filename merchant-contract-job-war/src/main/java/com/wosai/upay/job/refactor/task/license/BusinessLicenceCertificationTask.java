package com.wosai.upay.job.refactor.task.license;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.shouqianba.model.dto.request.MerchantTradeParamsDisableReasonManageReqDTO;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.shouqianba.model.enums.TradeParamsDisableReasonOperateTypeEnum;
import com.shouqianba.service.MerchantContractService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.shouqianba.withdrawservice.service.WithdrawService;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.model.MerchantBankAccountUpdateVo;
import com.wosai.upay.bank.model.MerchantBusinessLicenseUpdateVo;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.avro.MicroUpgradeSuccessDTO;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.PaymentModeChangeBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.model.dto.request.BusinessLicenseUpdateDTO;
import com.wosai.upay.job.model.dto.response.ErrorInfoPromptTextRspDTO;
import com.wosai.upay.job.model.dto.response.InsertLicenceUpdateTaskResultRspDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.event.MicroUpgradeSuccessEvent;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.model.enums.VerifyStatusEnum;
import com.wosai.upay.job.refactor.service.rpc.risk.RiskMerchantBusinessLicenseAuditService;
import com.wosai.upay.job.refactor.service.rpc.risk.req.RiskEntryResult;
import com.wosai.upay.job.refactor.task.AbstractInternalScheduleTaskHandleTemplate;
import com.wosai.upay.job.refactor.task.HaikeUnionPayContractResultTask;
import com.wosai.upay.job.refactor.task.license.micro.AbstractUpdateTradeParamsTemplate;
import com.wosai.upay.job.service.*;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.lkl.service.PayNotifyService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.wallet.service.WalletService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 营业执照认证任务
 * 任务放在每天凌晨一点后，六点前 每10秒调度一次
 *
 * <AUTHOR>
 * @date 2024/9/6 11:11
 */
@Component
@Slf4j
public class BusinessLicenceCertificationTask extends AbstractInternalScheduleTaskHandleTemplate {

    public static final String MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON = "小微升级";

    @Resource
    private ContractStatusDAO contractStatusDAO;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private MerchantContractService merchantContractService;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private McProviderBiz mcProviderBiz;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Resource
    private MerchantProviderParamsServiceImpl merchantProviderParamsService;

    @Resource
    private AcquirerServiceImpl acquirerService;

    @Resource
    private RiskMerchantBusinessLicenseAuditService riskMerchantBusinessLicenseAuditService;

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Autowired
    private WalletService walletServcie;

    @Autowired
    private WithdrawService withdrawService;

    @Autowired
    protected BusinessLogBiz businessLogBiz;

    @Resource
    private T9ServiceImpl t9Service;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Resource(name = "aliKafkaTemplate")
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    @Resource
    private PayNotifyService payNotifyService;

    @Resource
    private ErrorCodeManageService errorCodeManageService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private MerchantService merchantService;

    @Resource
    private ContractEventService contractEventService;
    @Resource
    private PaymentModeChangeBiz paymentModeChangeBiz;

    @Resource
    private TradeConfigClient tradeConfigClient;

    @Resource
    private HaikeUnionPayContractResultTask haikeUnionPayContractResultTask;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    public static final String MICRO_UPGRADE_MSG_PLATFORM = "merchant-contract-job_micro_upgrade_platform";


    public static String MICRO_UPGRADE_SUCCESS_TOPIC = "events_CUA_micro_upgrade_success";

    public static final String RE_CONTRACT_SUB_TASK_TYPE = "重新入网";

    public static final String CHANGE_PARAMS_SUB_TASK_TYPE = "参数变更";

    public static final String WAIT_ALL_RE_CONTRACT_FINISHED_MARK = "等待进件结束";

    public static final String WAIT_FEW_MINUTES_MARK = "等待几分钟";

    public static final String WAIT_CHANGE_ACQUIRER_FINISHED_MARK = "等待切收单机构任务结束";

    public static final String INTERNAL_MICRO_UPGRADE_REMARK = "CUA内部构建营业执照升级任务-1";



    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION;
    }


    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        executeProperty.setSupportParallel(true);
        return executeProperty;
    }

    @Resource
    private TradeConfigService tradeConfigService;

    /**
     * 主任务开始前的前置操作
     * 关闭商户交易状态，限制某个时间点之前（的交易）不可逆向操作（商户层级）
     */
    @Override
    protected void mainTaskPreProcessor(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
    }

    /**
     * 主任务执行完后的后置操作
     * 无论如何都要打开商户的交易状态
     */
    @Override
    protected void mainTaskPostProcessor(InternalScheduleMainTaskDO mainTaskDO, List<InternalScheduleSubTaskDO> sortedSubTasks) {
        if (!mainTaskDO.taskSuccess() && !mainTaskDO.taskFail()) {
            return;
        }
        Optional<ContractStatusDO> statusDOOpt = contractStatusDAO.getByMerchantSn(mainTaskDO.getMerchantSn());
        if (!statusDOOpt.isPresent()) {
            return;
        }
        Optional<String> merchantIdOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(mainTaskDO.getMerchantSn());
        if (!merchantIdOpt.isPresent()) {
            return;
        }
        ContractStatusDO contractStatusDO = statusDOOpt.get();
        String acquirer = contractStatusDO.getAcquirer();
        clearLklCache(merchantIdOpt.get());
        tradeConfigClient.updateClearProviderByAcquirer(merchantIdOpt.get(), acquirer);
        if (mainTaskDO.taskSuccess()) {
            // 品牌支付模式商户重新挂靠
            paymentModeChangeBiz.tryChangePaymentModeAfterMicroUpgrade(mainTaskDO.getMerchantSn());
            insertHaikeUnionPayContractResultTaskIfNeeded(mainTaskDO);
        }
    }

    /**
     * 如果海科升级成功，插入海科云闪付报备结果轮询任务
     *
     * @param mainTaskDO 主任务
     */
    private void insertHaikeUnionPayContractResultTaskIfNeeded(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            MainTaskContextBOInner mainTaskContext =
                    JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
            if (Objects.isNull(mainTaskContext)) {
                return;
            }
            List<String> successReContractAndUpdateParamsAcquirers =
                    mainTaskContext.getSuccessReContractAndUpdateParamsAcquirers();
            if (CollectionUtils.isEmpty(successReContractAndUpdateParamsAcquirers)) {
                return;
            }
            String haikeAcquirer = AcquirerTypeEnum.HAI_KE.getValue();
            if (successReContractAndUpdateParamsAcquirers.contains(haikeAcquirer)) {
                haikeUnionPayContractResultTask.insertTaskWithDelay(mainTaskDO.getMerchantSn(), 5);
            }
        } catch (Exception e) {
            log.error("插入海科云闪付报备结果轮询任务失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("插入海科云闪付报备结果轮询任务失败, merchantSn:" + mainTaskDO.getMerchantSn() + ", message:" + e.getMessage());
        }
    }

    private void clearLklCache(String merchantId) {
        try {
            log.info("小微升级清除拉卡拉缓存, merchantId:{}", merchantId);
            payNotifyService.clearMchCacheByMerchantId(merchantId);
        } catch (Exception e) {
            log.warn("小微升级清除拉卡拉缓存失败, merchantId:{}", merchantId, e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级清除拉卡拉缓存失败, merchantId:" + merchantId + ", message:" + e.getMessage());
        }
    }

    private void successCertificateMicroRecordToRedis(String merchantSn) {
        try {
            stringRedisTemplate.opsForValue().set("merchant-contract-job:successCertificateMicroRecord:" + merchantSn, "1", 30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("商户营业执照升级成功，但是写入redis失败, merchantSn:{}", merchantSn, e);
        }
    }
    
    public boolean removeSuccessCertificateMicroRecordFromRedis(String merchantSn) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.delete("merchant-contract-job:successCertificateMicroRecord:" + merchantSn));
        } catch (Exception e) {
            log.warn("商户营业执照升级成功，但是删除redis失败, merchantSn:{}", merchantSn, e);
            return false;
        }
    }

    @Resource
    private ChatBotUtil chatBotUtil;

    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (isReContractSubTask(subTaskDO)) {
            return handleReContractSubTask(mainTaskDO, subTaskDO);
        }
        InternalScheduleSubTaskProcessResultBO resultBO = handleChangeParamsSubTask(mainTaskDO, subTaskDO);
        if (resultBO.processStatusIsSuccess()) {
            processMicroUpgradeSuccess(mainTaskDO);
            noticeRiskMicroContractAndUpgradeTaskResult(mainTaskDO.getMerchantSn(), mainTaskDO, resultBO.processStatusIsSuccess(), resultBO.getResult());
            return resultBO;
        }
        if (resultBO.processStatusIsFail()) {
            noticeRiskMicroContractAndUpgradeTaskResult(mainTaskDO.getMerchantSn(), mainTaskDO,false, resultBO.getResult());
        }
        return resultBO;
    }


    public void processMicroUpgradeSuccess(InternalScheduleMainTaskDO mainTaskDO) {
        if (!StringUtils.equals(mainTaskDO.getRemark(), INTERNAL_MICRO_UPGRADE_REMARK)) {
            updateBusinessLicenseAndSubsidiaryInformation(mainTaskDO, true);
        }
        try {
            sendMicroUpgradeSuccessMsgToKafka(mainTaskDO);
            // 目前的逻辑下，小微升级成功，富友肯定不会参与重新入网，所以富友的参数一定要禁用
            disableOriginalFuYouParams(mainTaskDO.getMerchantSn());
            // 如果haike和lkl都重新入网，如果haike没有成功，需要禁用haike的以前参数，否则商户信息及营业执照类型收钱吧和收单机构不一致
            disableReContractFailedAndUpdateParamsAcquirerParams(mainTaskDO);
            deleteFuYouAndLklIntegratedCardSwipeRecord(mainTaskDO.getMerchantSn());
        } catch (Exception e) {
            log.warn("商户重新入网成功，已经变更交易参数，已经更新营业执照,后续操作出现异常, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
        }
    }

    private void deleteFuYouAndLklIntegratedCardSwipeRecord(String merchantSn) {
        log.info("商户小微升级成功，删除富友,拉卡拉,通联刷卡一体化开通记录, merchantSn:{}", merchantSn);
        doDeleteIntegratedCardSwipeRecordCatchException(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        doDeleteIntegratedCardSwipeRecordCatchException(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
        doDeleteIntegratedCardSwipeRecordCatchException(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.FU_YOU.getValue());
        doDeleteIntegratedForeignCardCatchException(merchantSn, AcquirerTypeEnum.TONG_LIAN_V2.getValue());
    }

    private void doDeleteIntegratedForeignCardCatchException(String merchantSn, String acquirer) {
        try {
            t9Service.deactivateCrmAndCuaForeignCard(merchantSn, acquirer);
        } catch (Exception e) {
            log.warn("商户删除外卡开通记录失败, merchantSn:{}, acquirer:{}, message:{}", merchantSn, acquirer, e.getMessage(), e);
        }
    }

    private void doDeleteIntegratedCardSwipeRecordCatchException(String merchantSn, String acquirer) {
        try {
            if (t9Service.isT9PosOpen(merchantSn, acquirer)) {
                t9Service.deactivateCrmAndCuaIntegratedCardSwiping(merchantSn, acquirer);
            }
        } catch (Exception e) {
            log.warn("商户删除刷卡一体化开通记录失败, merchantSn:{}, acquirer:{}, message:{}", merchantSn, acquirer, e.getMessage());
        }
    }

    private void disableReContractFailedAndUpdateParamsAcquirerParams(InternalScheduleMainTaskDO mainTaskDO) {
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        List<String> allContractedAcquirers = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn()).stream()
                    .map(t -> StringExtensionUtils.toSafeString(mcProviderBiz.getAcquirerByProvider(t.getProvider().toString())))
                    .distinct()
                    .collect(Collectors.toList());
        List<String> successReContractAndUpdateParamsAcquirers = mainTaskContextBOInner.getSuccessReContractAndUpdateParamsAcquirers();
        disableDeletedSuccessReContractAcquirerMerchantId(mainTaskDO.getMerchantSn(), successReContractAndUpdateParamsAcquirers);
        List<String> needDeleteAcquirers = Lists.newArrayList(
                Sets.difference(Sets.newHashSet(allContractedAcquirers), Sets.newHashSet(successReContractAndUpdateParamsAcquirers))
        );
        for (String needDeleteAcquirer : needDeleteAcquirers) {
            try {
                MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(mainTaskDO.getMerchantSn());
                req.setDisableReasonDetail("小微升级成功后，禁用未升级成功的收单机构商户号");
                merchantTradeParamsBiz.disableMerchantAcquirerMerchantId(mainTaskDO.getMerchantSn(), needDeleteAcquirer, req);
            } catch (Exception e) {
                log.warn("商户重新入网成功，已经变更交易参数，但是禁用收单机构交易参数失败，商户号:{}, 收单机构:{}", mainTaskDO.getMerchantSn(), needDeleteAcquirer, e);
            }
        }
    }

    private void disableDeletedSuccessReContractAcquirerMerchantId(String merchantSn, List<String> successReContractAndUpdateParamsAcquirers) {
        for (String acquirer : successReContractAndUpdateParamsAcquirers) {
            try {
                Optional<MerchantProviderParamsDO> lastedDeletedAcquirerMerchantParams = merchantTradeParamsBiz.getLastedDeletedAcquirerMerchantId(merchantSn, acquirer);
                if (!lastedDeletedAcquirerMerchantParams.isPresent()) {
                    return;
                }
                MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(merchantSn);
                req.setDisableReasonDetail("小微升级成功后，禁用已删除的收单机构商户号");
                merchantTradeParamsBiz.disableAcquirerMerchantId(lastedDeletedAcquirerMerchantParams.get().getProviderMerchantId(), req);
            } catch (Exception e) {
                log.warn("商户重新入网成功，已经变更交易参数，但是禁用收单机构交易参数失败，商户号:{}, 收单机构:{}", merchantSn, acquirer, e);
            }
        }
    }

    public MerchantTradeParamsDisableReasonManageReqDTO getMerchantTradeParamsDisableReasonManageReqDTO(String merchantSn) {
        MerchantTradeParamsDisableReasonManageReqDTO req = new MerchantTradeParamsDisableReasonManageReqDTO();
        req.setMerchantSn(merchantSn);
        req.setOperateType(TradeParamsDisableReasonOperateTypeEnum.ADD);
        req.setAccessSide(TradeParamsDisableReasonAccessSideEnum.CUA);
        req.setOperator("merchant-contract-job");
        req.setDisableReason(MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON);
        return req;
    }


    private void disableOriginalFuYouParams(String merchantSn) {
        try {
            MerchantTradeParamsDisableReasonManageReqDTO req = getMerchantTradeParamsDisableReasonManageReqDTO(merchantSn);
            req.setDisableReasonDetail("小微升级成功后，禁用富友原收单机构商户号");
            merchantTradeParamsBiz.disableMerchantAcquirerMerchantId(merchantSn, AcquirerTypeEnum.FU_YOU.getValue(), req);
        } catch (Exception e) {
            log.warn("商户重新入网成功，已经变更交易参数，但是禁用富友交易参数失败, merchantSn:{}", merchantSn, e);
        }
    }

    private void sendMicroUpgradeSuccessMsgToKafka(InternalScheduleMainTaskDO mainTaskDO) {
        MicroUpgradeSuccessDTO microUpgradeSuccessDTO = new MicroUpgradeSuccessDTO();
        microUpgradeSuccessDTO.setMerchantSn(mainTaskDO.getMerchantSn());
        microUpgradeSuccessDTO.setOriginalAcquirer(mainTaskDO.getAcquirer());
        microUpgradeSuccessDTO.setSuccessTimeMillis(System.currentTimeMillis());
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
        try {
            microUpgradeSuccessDTO.setNewAcquirer(mainTaskContextBOInner.getNewAcquirer());
            merchantTradeParamsBiz.getAcquirerMerchantId(mainTaskDO.getMerchantSn(), mainTaskContextBOInner.getNewAcquirer()).ifPresent(microUpgradeSuccessDTO::setNewAcquirerMerchantId);
            microUpgradeSuccessDTO.setOriginalAcquirer(mainTaskContextBOInner.getOldInUseAcquirer());
            microUpgradeSuccessDTO.setOriginalAcquirerMerchantId(mainTaskContextBOInner.getOldInUseAcquirerMerchantId());
            log.info("发送小微升级成功消息到kafka, msg:{}", microUpgradeSuccessDTO);
            aliKafkaTemplate.send(MICRO_UPGRADE_SUCCESS_TOPIC, microUpgradeSuccessDTO);
            applicationEventPublisher.publishEvent(new MicroUpgradeSuccessEvent(mainTaskDO, mainTaskDO.getMerchantSn()));
        } catch (Exception e) {
            log.warn("发送小微升级成功消息到kafka失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("发送小微升级成功消息到kafka失败, merchantSn:" + mainTaskDO.getMerchantSn() + ", message:" + e.getMessage());
        }
    }


    private InternalScheduleSubTaskProcessResultBO handleReContractSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return reContract(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            return queryContractResult(mainTaskDO, subTaskDO);
        }
        throw new ContractBizException("不支持的任务状态");
    }

    /**
     * 目前只有lkl需要主动查询回调结果
     */
    private InternalScheduleSubTaskProcessResultBO queryContractResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (!StringUtils.equals(subTaskDO.getAcquirer(), AcquirerTypeEnum.LKL_V3.getValue())) {
            return InternalScheduleSubTaskProcessResultBO.fail("收单机构" + subTaskDO.getAcquirer() + "不支持");
        }
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), SubTaskResponseDTOInner.class);
        LklV3ContractResultDTO lklV3ContractResultDTO = acquirerFacade.getUniqueAbilityAcquirer(AcquirerTypeEnum.LKL_V3, LklV3AcquirerFacade.class)
                .queryContractResult(subTaskResponseDTOInner.getContractId());
        SubTaskRequestDTOInner subTaskRequestDTOInner = JSON.parseObject(subTaskDO.getRequestMessage(), SubTaskRequestDTOInner.class);
        subTaskRequestDTOInner.setQueryResultReqMap(lklV3ContractResultDTO.getRequestMap());
        subTaskResponseDTOInner.setQueryResultRsqMap(lklV3ContractResultDTO.getResponseMap());
        if (lklV3ContractResultDTO.isNotFinished()) {
            resultBO.updateStatusWaitExternalResult("进件尚未完成，轮询进件结果中");
        } else if (lklV3ContractResultDTO.isContractSuccess()) {
            try {
                subTaskResponseDTOInner.setNewAcquirerMerchantId(lklV3ContractResultDTO.getAcquirerMerchantId());
                subTaskResponseDTOInner.setNewUnionMerchantId(lklV3ContractResultDTO.getUnionMerchantId());
                subTaskResponseDTOInner.setNewTermId(lklV3ContractResultDTO.getTermId());
                subTaskResponseDTOInner.setNewShopId(lklV3ContractResultDTO.getShopId());
                subTaskDO.setResponseMessage(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusSuccess("进件成功");
            } catch (Exception e) {
                resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
                resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusFail(e.getMessage());
            }
        } else if (lklV3ContractResultDTO.isContractFailed()) {
            resultBO.updateStatusFail("进件失败");
        }
        resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
        resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
        return resultBO;
    }

    private InternalScheduleSubTaskProcessResultBO reContract(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String acquirer = subTaskDO.getAcquirer();
        Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        if (!sharedAbilityByAcquirer.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("收单机构" + acquirer + "不支持");
        }
        Map<String, Object> contextMap = buildContractContext(mainTaskDO);
        NewMerchantContractResultRspDTO contractResultRspDTO = sharedAbilityByAcquirer.get().contractToAcquirer(mainTaskDO.getMerchantSn(), contextMap);
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        SubTaskRequestDTOInner subTaskRequestDTOInner = new SubTaskRequestDTOInner(contractResultRspDTO.getRequest());
        SubTaskResponseDTOInner subTaskResponseDTOInner = new SubTaskResponseDTOInner(contractResultRspDTO.getResponse());
        if (contractResultRspDTO.isProcessing()) {
            subTaskResponseDTOInner.setContractId(contractResultRspDTO.getContractId());
            resultBO.updateStatusWaitExternalResult("进件成功，等待回调查询进件结果");
        }  else if (contractResultRspDTO.isSuccess()) {
            try {
                subTaskResponseDTOInner.setNewUnionMerchantId(contractResultRspDTO.getUnionMerchantId());
                subTaskResponseDTOInner.setNewTermId(contractResultRspDTO.getTermId());
                subTaskResponseDTOInner.setNewAcquirerMerchantId(contractResultRspDTO.getAcquirerMerchantId());
                subTaskDO.setResponseMessage(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusSuccess("进件成功");
            } catch (Exception e) {
                resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
                resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
                resultBO.updateStatusFail(e.getMessage());
            }
        } else {
            resultBO.updateStatusFail("进件失败");
        }
        resultBO.setRequestMsg(JSON.toJSONString(subTaskRequestDTOInner));
        resultBO.setResponseMsg(JSON.toJSONString(subTaskResponseDTOInner));
        return resultBO;
    }

    @NotNull
    private Map<String, Object> buildContractContext(InternalScheduleMainTaskDO mainTaskDO) {
        Map<String, Object> contextMap = paramContextBiz.getNetInParamContextByMerchantSn(mainTaskDO.getMerchantSn());
        if (!contextMap.containsKey(ParamContextBiz.KEY_BUSINESS_LICENCE)) {
            throw new ContractBizException("营业执照信息为空");
        }
        Map<String, Object> originalLicenceMap = (Map<String, Object>) contextMap.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
        if (Objects.isNull(mainTaskContextBOInner) || Objects.isNull(mainTaskContextBOInner.getBusinessLicenseReqDTO())) {
            throw new ContractBizException("营业执照信息为空");
        }
        BusinessLicenseUpdateDTO businessLicenseReqDTO = mainTaskContextBOInner.getBusinessLicenseReqDTO();
        originalLicenceMap.put(MerchantBusinessLicence.PHOTO, basePhotoUrlAgain(businessLicenseReqDTO.getPhoto()));
        originalLicenceMap.put(MerchantBusinessLicence.NUMBER, businessLicenseReqDTO.getNumber());
        originalLicenceMap.put(MerchantBusinessLicence.NAME, businessLicenseReqDTO.getName());
        originalLicenceMap.put(MerchantBusinessLicence.VALIDITY, businessLicenseReqDTO.getValidity());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_NAME, businessLicenseReqDTO.getLegalPersonName());
        originalLicenceMap.put(MerchantBusinessLicence.ADDRESS, businessLicenseReqDTO.getLicenseAddress());
        originalLicenceMap.put(MerchantBusinessLicence.TYPE, businessLicenseReqDTO.getLicenseType());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE, businessLicenseReqDTO.getIdType());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, basePhotoUrlAgain(businessLicenseReqDTO.getLegalPersonIdCardFrontPhoto()));
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_BACK_PHOTO, basePhotoUrlAgain(businessLicenseReqDTO.getLegalPersonIdCardBackPhoto()));
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, businessLicenseReqDTO.getLegalPersonIdNumber());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ADDRESS, businessLicenseReqDTO.getLegalPersonIdCardAddress());
        originalLicenceMap.put(MerchantBusinessLicence.LEGAL_PERSON_ID_CARD_ISSUING_AUTHORITY, businessLicenseReqDTO.getLegalPersonIdCardIssuingAuthority());
        originalLicenceMap.put(MerchantBusinessLicence.ID_VALIDITY, businessLicenseReqDTO.getIdValidity());
        log.info("小微升级营业执照数据,merchantSn:{}, 营业执照数据:{}", mainTaskDO.getMerchantSn(), originalLicenceMap);
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, originalLicenceMap);
        updateMerchantBankAccount(contextMap, businessLicenseReqDTO);
        return contextMap;
    }


    /**
     * 重新入网，此时营业执照和账户信息还没有更新入库，需要根据请求中的结算信息更新
     */
    private void updateMerchantBankAccount(Map<String, Object> contextMap, BusinessLicenseUpdateDTO businessLicenseReqDTO) {
        if (contextMap.containsKey(ParamContextBiz.KEY_BANK_ACCOUNT)) {
            Map<String, Object> originalBankAccountMap = (Map<String, Object>) contextMap.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            originalBankAccountMap.remove("change_extra");
            originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.LETTER_OF_AUTHORIZATION, businessLicenseReqDTO.getLetterOfAuthorization());
            if (StringUtils.equals(businessLicenseReqDTO.getLegalPersonIdNumber(),
                    MapUtils.getString(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.IDENTITY))) {
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, basePhotoUrlAgain(businessLicenseReqDTO.getLegalPersonIdCardFrontPhoto()));
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER_ID_BACK_PHOTO, basePhotoUrlAgain(businessLicenseReqDTO.getLegalPersonIdCardBackPhoto()));
                originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.ID_VALIDITY, businessLicenseReqDTO.getIdValidity());
                if (BankAccountTypeEnum.isPersonal(MapUtils.getIntValue(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.TYPE))
                        && !StringUtils.equals(businessLicenseReqDTO.getLegalPersonName(), MapUtils.getString(originalBankAccountMap, com.wosai.upay.core.model.MerchantBankAccount.HOLDER))) {
                    originalBankAccountMap.put(com.wosai.upay.core.model.MerchantBankAccount.HOLDER, businessLicenseReqDTO.getLegalPersonName());

                }
            }
            contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, originalBankAccountMap);
        }
    }

    /**
     * 核心逻辑 更新参数配置
     */
    public void updateParamsConfigWhenReContractSubTaskSuccess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        getLicenceCertificationAcquirerHandler(subTaskDO.getAcquirer()).updateParamsAfterContractSuccess(mainTaskDO, subTaskDO);
    }

    private void updateClearanceProviderWhenSameAcquirer(String acquirer, String merchantId) {
        tradeConfigClient.updateClearanceProviderAndSwitchTime(merchantId, acquirer);
    }

    private AbstractUpdateTradeParamsTemplate getLicenceCertificationAcquirerHandler(String acquirer) {
        Optional<AbstractUpdateTradeParamsTemplate> handlerOpt = applicationContext.getBeansOfType(AbstractUpdateTradeParamsTemplate.class).values().stream()
                .filter(handler -> StringUtils.equals(handler.getAcquirer(), acquirer))
                .findFirst();
        if (!handlerOpt.isPresent()) {
            throw new ContractBizException("收单机构" + acquirer + "不支持");
        }
        return handlerOpt.get();
    }

    private Optional<AbstractUpdateTradeParamsTemplate> getLicenceCertificationAcquirerHandlerOpt(String acquirer) {
        return  applicationContext.getBeansOfType(AbstractUpdateTradeParamsTemplate.class).values().stream()
                .filter(handler -> StringUtils.equals(handler.getAcquirer(), acquirer))
                .findFirst();
    }


    private InternalScheduleSubTaskProcessResultBO handleChangeParamsSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            if (!isReContractSubTasksAllFinished(mainTaskDO.getId())) {
                subTaskDO.setStatusMark(WAIT_ALL_RE_CONTRACT_FINISHED_MARK);
                InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
                resultBO.updateStatusWaitExternalResult("等待重新进件子任务全部执行结束");
                return resultBO;
            }
            return doChangePrams(mainTaskDO,subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            if (StringUtils.equals(WAIT_ALL_RE_CONTRACT_FINISHED_MARK, subTaskDO.getStatusMark())) {
                if (!isReContractSubTasksAllFinished(mainTaskDO.getId())) {
                    subTaskDO.setStatusMark(WAIT_ALL_RE_CONTRACT_FINISHED_MARK);
                    InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
                    resultBO.updateStatusWaitExternalResult("等待重新进件子任务全部执行结束");
                    return resultBO;
                }           
                return doChangePrams(mainTaskDO,subTaskDO);
            } else if (StringUtils.equals(WAIT_FEW_MINUTES_MARK, subTaskDO.getStatusMark())) {
                return doChangePrams(mainTaskDO,subTaskDO);
            } else if (StringUtils.equals(WAIT_CHANGE_ACQUIRER_FINISHED_MARK, subTaskDO.getStatusMark())) {
                return processWaitChangeAcquirer(mainTaskDO, subTaskDO);
            }
        }
        throw new ContractBizException("不支持的任务状态");
    }

    private InternalScheduleSubTaskProcessResultBO processWaitChangeAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String targetAcquirer = subTaskDO.getAcquirer();
        Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getByPrimaryKey(Integer.valueOf(subTaskDO.getContext()));
        if (!changeTaskOpt.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("切换收单机构任务不存在");
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeTaskOpt.get();
        if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.SUCCESS)) {
            recordMainTaskContextNewAcquirer(mainTaskDO, targetAcquirer);
            return InternalScheduleSubTaskProcessResultBO.success("切换成功,目标收单机构: " + targetAcquirer);
        } else if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.FAIL)) {
            return InternalScheduleSubTaskProcessResultBO.fail("切换失败,目标收单机构：" + targetAcquirer + "。失败原因：" + mcAcquirerChangeDO.getMemo());
        }
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("切换任务已经提交,等待切换结果");
    }

    private Timestamp getMinutesLater(Integer waitMinutes) {
        long fiveMinutesLaterMillis = System.currentTimeMillis() + waitMinutes * 60 * 1000;
        return new Timestamp(fiveMinutesLaterMillis);
    }

    private void withdraw(String merchantId) {
        long balance = walletServcie.getBalance(merchantId);
        if (balance > 0) {
            List<Map<String, Object>> withdraws = withdrawService.changeAcquirerWithdraw(merchantId);
            List<String> withdrawIds = withdraws.stream()
                    .map(withdraw -> BeanUtil.getPropString(withdraw, DaoConstants.ID))
                    .collect(Collectors.toList());
            businessLogBiz.sendWithdrawLog(merchantId, balance, "小微升级，强制结算");
            log.info("{} 发起强制提现 {}", merchantId, JSON.toJSONString(withdrawIds));
        }
    }

    /**
     * 商户是否没有关闭交易状态
     * 这里enableScheduledTime为空说明商户交易状态没有关闭
     *
     * @param mainTaskDO 主任务
     * @return 是否已经关闭交易状态 true-没有关闭
     */
    private boolean isMerchantHasNotCloseTradeStatus(InternalScheduleMainTaskDO mainTaskDO) {
        return Objects.isNull(mainTaskDO.getEnableScheduledTime());
    }

    // 测试环境 00:00-23:59 生产环境 01:00-06:59:59
    public boolean isTimeInRange(LocalTime time) {
        LocalTime start = LocalTime.of(applicationApolloConfig.getMicroUpgradeBeginHour(), 0, 0);
        LocalTime end = LocalTime.of(applicationApolloConfig.getMicroUpgradeEndHour(), 59, 59);
        return time.isAfter(start) && time.isBefore(end);
    }

    private InternalScheduleSubTaskProcessResultBO doChangePrams(InternalScheduleMainTaskDO mainTaskDO,InternalScheduleSubTaskDO subTaskDO) {
        String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn()).getId();
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        List<InternalScheduleSubTaskDO> reContractSubTasks = internalScheduleSubTaskDAO.listByMainIds(Lists.newArrayList(mainTaskDO.getId())).stream()
                .filter(t -> StringUtils.equals(t.getTaskType(), RE_CONTRACT_SUB_TASK_TYPE)).collect(Collectors.toList());
        List<InternalScheduleSubTaskDO> successReContractTasks = reContractSubTasks.stream().filter(InternalScheduleSubTaskDO::isProcessSuccess).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successReContractTasks)) {
            resultBO.updateStatusFail("没有重新进件成功的收单机构");
            return resultBO;
        }
        if (!isTimeInRange(LocalTime.now()) && isMerchantHasNotCloseTradeStatus(mainTaskDO)) {
            return InternalScheduleSubTaskProcessResultBO.waitExternalResult("非切参数时间段");
        }
        if (isMerchantHasNotCloseTradeStatus(mainTaskDO)) {
            return sleepAndCloseTradeStatusBeforeUpdateParams(mainTaskDO, subTaskDO, merchantId);
        }
        try {
            withdraw(merchantId);
        } catch (Exception e) {
            log.error("商户强制提现失败，merchantSn:{}", merchantId, e);
            return InternalScheduleSubTaskProcessResultBO.fail("强制提现失败");
        }
        log.info("小微升级,已经关闭商户交易状态，且已经等待{}分钟,已经做了强制提现,现在开始更新交易参数, merchantSn:{}", applicationApolloConfig.getMicroUpgradeWaitMinutes(), mainTaskDO.getMerchantSn());
        Map<String, InternalScheduleSubTaskDO> orderedSuccessContractSubTaskMap = getSortedSuccessContractSubTaskMap(successReContractTasks);
        List<String> reContractAndUpdateParamsSuccessAcquirers = new ArrayList<>();
        Map<String, String> acquirerUpdateParamsFailMsgMap = Maps.newHashMap();
        for (Map.Entry<String, InternalScheduleSubTaskDO> successReContractSubTaskEntry : orderedSuccessContractSubTaskMap.entrySet()) {
            String acquirer = successReContractSubTaskEntry.getKey();
            try {
                updateParamsConfigWhenReContractSubTaskSuccess(mainTaskDO, successReContractSubTaskEntry.getValue());
                reContractAndUpdateParamsSuccessAcquirers.add(acquirer);
            } catch (Exception e) {
                acquirerUpdateParamsFailMsgMap.put(acquirer, e.getMessage());
                log.warn("小微升级,重新入网成功,变更交易参数失败,商户{},收单机构{}", mainTaskDO.getMerchantSn(),acquirer, e);
            }
        }
        setMainTaskContextReContractAndUpdateParamsSuccessAcquirers(mainTaskDO,
                reContractSubTasks.stream().map(InternalScheduleSubTaskDO::getAcquirer).collect(Collectors.toSet()),
                orderedSuccessContractSubTaskMap.keySet(),
                reContractAndUpdateParamsSuccessAcquirers);
        if (CollectionUtils.isEmpty(reContractAndUpdateParamsSuccessAcquirers)) {
            log.error("小微升级商户更新交易参数失败,没有变更交易参数成功的收单机构可供选择,merchantSn:{}, failMsgMap:{}", mainTaskDO.getMerchantSn(), acquirerUpdateParamsFailMsgMap);
            String failReason = acquirerUpdateParamsFailMsgMap.entrySet().stream().map(entry -> entry.getKey() + "：" + entry.getValue()).collect(Collectors.joining("；"));
            return InternalScheduleSubTaskProcessResultBO.fail("没有变更交易参数成功的收单机构可供选择："  + failReason);
        }
        // 如果当前收单机构可以小微升级，重新进件后，直接更新交易侧参数即可。否则需要切收单机构
        String inUseChangeFailReason = mainTaskDO.getAcquirer() + "收单机构不支持小微升级";
        if (reContractAndUpdateParamsSuccessAcquirers.contains(mainTaskDO.getAcquirer())) {
            try {
                reUpdateTradeSideParamsWhenInUseAcquirer(mainTaskDO, subTaskDO, orderedSuccessContractSubTaskMap);
                updateClearanceProviderWhenSameAcquirer(mainTaskDO.getAcquirer(), merchantId);
                recordMainTaskContextNewAcquirer(mainTaskDO, mainTaskDO.getAcquirer());
                return InternalScheduleSubTaskProcessResultBO.success("更新交易参数成功");
            } catch (Exception e) {
                log.warn("小微升级商户更新在用的收单机构交易参数失败,尝试切收单机构,merchantSn:{},inUseAcquirer:{}", mainTaskDO.getMerchantSn(), mainTaskDO.getAcquirer(),e);
                inUseChangeFailReason = e.getMessage();
            }
        }
        return changeAcquirer(mainTaskDO, subTaskDO,
                orderedSuccessContractSubTaskMap.entrySet().stream()
                        .filter(t -> reContractAndUpdateParamsSuccessAcquirers.contains(t.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> existing, LinkedHashMap::new)), inUseChangeFailReason);
    }

    private InternalScheduleSubTaskProcessResultBO sleepAndCloseTradeStatusBeforeUpdateParams(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, String merchantId) {
        log.info("小微升级变更交易参数前关闭商户交易状态，{}分钟后开始更新交易参数, merchantSn:{}", applicationApolloConfig.getMicroUpgradeWaitMinutes(), mainTaskDO.getMerchantSn());
        tradeConfigClient.updateClearanceProviderToSwitch(merchantId);
        mainTaskDO.setEnableScheduledTime(getMinutesLater(applicationApolloConfig.getMicroUpgradeWaitMinutes()));
        subTaskDO.setStatusMark(WAIT_FEW_MINUTES_MARK);
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("已关闭商户交易状态,等待" + applicationApolloConfig.getMicroUpgradeWaitMinutes() + "分钟后更新交易参数");
    }

    private void recordMainTaskContextNewAcquirer(InternalScheduleMainTaskDO mainTaskDO, String acquirer) {
        String context = mainTaskDO.getContext();
        if (StringUtils.isBlank(context)) {
            return;
        }
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        mainTaskContextBOInner.setNewAcquirer(acquirer);
        mainTaskDO.setContext(JSON.toJSONString(mainTaskContextBOInner));
    }

    private void setMainTaskContextReContractAndUpdateParamsSuccessAcquirers(InternalScheduleMainTaskDO mainTaskDO,
                                                                             Set<String> reContractAcquires,
                                                                             Set<String> reContractSuccessAcquires,
                                                                             List<String> reContractAndUpdateParamsSuccessAcquirers) {
        String context = mainTaskDO.getContext();
        if (StringUtils.isBlank(context)) {
            return;
        }
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(context, MainTaskContextBOInner.class);
        if (Objects.isNull(mainTaskContextBOInner)) {
            return;
        }
        mainTaskContextBOInner.setReContractAcquirers(new ArrayList<>(reContractAcquires));
        mainTaskContextBOInner.setSuccessReContractAcquirers(new ArrayList<>(reContractSuccessAcquires));
        mainTaskContextBOInner.setSuccessReContractAndUpdateParamsAcquirers(reContractAndUpdateParamsSuccessAcquirers);
        mainTaskDO.setContext(JSON.toJSONString(mainTaskContextBOInner));
    }

    private Map<String, InternalScheduleSubTaskDO> getSortedSuccessContractSubTaskMap(List<InternalScheduleSubTaskDO> successReContractTasks) {
        return successReContractTasks.stream()
                .collect(Collectors.toMap(InternalScheduleSubTaskDO::getAcquirer, Function.identity(), (k1, k2) -> k1, LinkedHashMap::new))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(entry -> {
                    String acquirer = entry.getKey();
                    if (AcquirerTypeEnum.LKL_V3.getValue().equals(acquirer)) {
                        return 1;
                    } else if (AcquirerTypeEnum.HAI_KE.getValue().equals(acquirer)) {
                        return 2;
                    } else {
                        return 3;
                    }
                }))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (existing, replacement) -> existing,LinkedHashMap::new));
    }


    private InternalScheduleSubTaskProcessResultBO changeAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                                                  Map<String, InternalScheduleSubTaskDO> orderedSuccessContractAndUpdateParamsSubTaskMap,
                                                                  String inUseChangeFailReason) {
        orderedSuccessContractAndUpdateParamsSubTaskMap.remove(mainTaskDO.getAcquirer()); // 排除当前收单机构
        String inUseAcquirerChangeFailReason = "原收单机构：" + mainTaskDO.getAcquirer() + ": " + inUseChangeFailReason;
        if (MapUtils.isEmpty(orderedSuccessContractAndUpdateParamsSubTaskMap)) {
            log.error("小微升级商户没有可切换的收单机构,merchantSn:{}, 原收单机构切换失败原因: {}", mainTaskDO.getMerchantSn(), inUseChangeFailReason);
            return InternalScheduleSubTaskProcessResultBO.fail("原收单机构无法完成小微升级,且没有可切换的收单机构。" + inUseAcquirerChangeFailReason);
        }
        StringBuilder changeFailReason = new StringBuilder();
        for (Map.Entry<String, InternalScheduleSubTaskDO> acquirerContractSubTaskEntry : orderedSuccessContractAndUpdateParamsSubTaskMap.entrySet()) {
            String targetAcquirer = acquirerContractSubTaskEntry.getKey();
            try {
                boolean enableTrade = merchantContractService.isMerchantContractSuccessAndEnableTrade(mainTaskDO.getMerchantSn(), targetAcquirer);
                if (!enableTrade) {
                    log.warn("小微升级切收单机构，目标收单机构未完成授权验证。merchantSn:{}, targetAcquirer:{}", mainTaskDO.getMerchantSn(), targetAcquirer);
                    changeFailReason.append(targetAcquirer).append(":").append("存在微信或者支付宝未完成授权验证").append(";");
                    continue;
                }
                ThreadLocalUtil.setCheckMicroUpgrade(true);
                CheckChangeAcquirerResp checkResult = acquirerService.checkChangeAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer);
                if (!checkResult.isCan_change()) {
                    log.warn("小微升级切换收单机构校验未通过,商户:{},目标收单机构:{}, 原因:{}", mainTaskDO.getMerchantSn(), targetAcquirer, checkResult.getMsg());
                    changeFailReason.append(targetAcquirer).append(":").append(checkResult.getMsg()).append(";");
                    continue;
                }
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                acquirerService.applyChangeAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer, true);
                Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getLastedByMerchantSnAndTargetAcquirer(mainTaskDO.getMerchantSn(), targetAcquirer, timestamp);
                if (!changeTaskOpt.isPresent()) {
                    throw new ContractBizException("切换收单机构任务提交成功，但是没有生成对应的切换任务");
                }
                subTaskDO.setContext(changeTaskOpt.get().getId().toString());
                subTaskDO.setStatusMark(WAIT_CHANGE_ACQUIRER_FINISHED_MARK);
                subTaskDO.setAcquirer(targetAcquirer);
                return InternalScheduleSubTaskProcessResultBO.waitExternalResult("切换收单机构任务已经提交");
            } catch (Exception e) {
                log.warn("小微升级商户切换收单机构失败, 商户:{}, 收单机构:{}", mainTaskDO.getMerchantSn(), targetAcquirer, e);
                changeFailReason.append(targetAcquirer).append(":").append(e.getMessage()).append(";");
            } finally {
                ThreadLocalUtil.removeCheckMicroUpgrade();
            }
        }
        log.error("小微升级未选择到可切换的收单机构, 商户:{}, reason:{}", mainTaskDO.getMerchantSn(), changeFailReason);
        return InternalScheduleSubTaskProcessResultBO.fail("原收单机构无法完成小微升级,且未选择到可切换的收单机构 " +  inUseAcquirerChangeFailReason + ", " + changeFailReason);
    }


    private void reUpdateTradeSideParamsWhenInUseAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, Map<String, InternalScheduleSubTaskDO> orderedSuccessContractSubTaskMap) {
        InternalScheduleSubTaskDO contractSubTask = orderedSuccessContractSubTaskMap.get(mainTaskDO.getAcquirer());
        SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(contractSubTask.getContext(), SubTaskContextBOInner.class);
        List<String> newParamsIds = subTaskContextBOInner.getNewParamsIdList();
        List<MerchantProviderParamsDO> newProviderParamsDOS = merchantProviderParamsDAO.listByIds(newParamsIds)
                .stream()
                .filter(t -> !Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .sorted(Comparator.comparingInt(MerchantProviderParamsDO::getPayway)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newProviderParamsDOS)) {
            log.warn("小微升级,当前收单机构不存在在用的交易参数, merchantSn:{}", mainTaskDO.getMerchantSn());
            throw new ContractBizException("当前收单机构不存在在用的交易参数");
        }
        // 因为是当前在用的收单机构，看到存在status=1的
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
        for (MerchantProviderParamsDO newProviderParamsDO : newProviderParamsDOS) {
            try {
                ThreadLocalUtil.setRefreshTradeParamsToPay(true);
                // status=1肯定是当前在用的移动支付业务
                if (Objects.equals(newProviderParamsDO.getStatus(), UseStatusEnum.IN_USE.getValue())) {
                    refreshDefaultParamsToPay(newProviderParamsDO, merchantInfo, mainTaskDO.getAcquirer());
                }
                // 对于无忧收款，线上，线上收款 根据params的wx_use_type来判断，需要单独再更新merchantAppConfig
                if (Objects.nonNull(newProviderParamsDO.getWxUseType()) &&
                        Sets.newHashSet(WxUseType.ONLINE_PAYMENT.getCode(), WxUseType.SCENARIO_PAYMENT.getCode()).contains(newProviderParamsDO.getWxUseType())) {
                    updateAppConfigInSpecialConditionWithCatchException(newProviderParamsDO, merchantInfo);
                }
            } catch (Exception e) {
                if (Objects.equals(newProviderParamsDO.getPayway(), PaywayEnum.WEIXIN.getValue())
                        || Objects.equals(newProviderParamsDO.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                    log.warn("小微升级更新AT交易参数到支付侧失败,payWay:{}, merchantSn:{}, provider:{}",newProviderParamsDO.getPayway(), subTaskDO.getMerchantSn(), newProviderParamsDO.getProvider(),e);
                    chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级更新AT交易参数到支付侧失败, merchantSn:" + subTaskDO.getMerchantSn()
                            + ", payWay:" + newProviderParamsDO.getPayway() + ", provider" + newProviderParamsDO.getProvider());
                    throw new ContractBizException("更新交易参数失败", e);
                }
                log.warn("更新交易参数失败,payWay:{}, merchantSn:{}",newProviderParamsDO.getPayway(), subTaskDO.getMerchantSn(), e);
            } finally {
                ThreadLocalUtil.removeRefreshTradeParamsToPay();
            }
        }
        subTaskDO.setAcquirer(mainTaskDO.getAcquirer());
    }

    private void refreshDefaultParamsToPay(MerchantProviderParamsDO merchantProviderParamsDO, MerchantInfo merchantInfo, String acquirer) {
        try {
            merchantProviderParamsService.setDefaultMerchantProviderParams(merchantProviderParamsDO.getId(), null, "小微升级更新交易参数");
        } catch (Exception e) {
            if (Sets.newHashSet(PaywayEnum.WEIXIN.getValue(), PaywayEnum.ALIPAY.getValue()).contains(merchantProviderParamsDO.getPayway())) {
                log.warn("小微升级更新AT交易参数失败,开始直接变更交易侧交易参数,merchantSn:{}, provider:{}, payWay:{}, paramsId:{}",
                        merchantProviderParamsDO.getMerchantSn(),merchantProviderParamsDO.getProvider(), merchantProviderParamsDO.getPayway(), merchantProviderParamsDO.getId(), e);
                getLicenceCertificationAcquirerHandler(acquirer).updatePayMerchantConfigAndAppConfigParams(merchantInfo, merchantProviderParamsDO);
            } else {
                log.info("小微升级删除交易侧交易参数, merchantSn:{}, provider:{}, payWay:{}", merchantInfo.getSn(), merchantProviderParamsDO.getProvider(), merchantProviderParamsDO.getPayway());
                deletePaySideParams(merchantInfo.getId(), merchantProviderParamsDO);
            }
        }
    }

    public void updateAppConfigInSpecialConditionWithCatchException(MerchantProviderParamsDO newProviderParamsDO, MerchantInfo merchantInfo) {
        // 如果商户开通了线上收款或者跨城收款，需要单独更新交易参数
        try {
            Map appConfigParams = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantInfo.getId(), newProviderParamsDO.getPayway(), subBizParamsBiz.getOnlinePaymentTradeAppId());
            if (MapUtils.isNotEmpty(appConfigParams) && Objects.equals(MapUtils.getInteger(appConfigParams, CommonModel.PROVIDER), newProviderParamsDO.getProvider())) {
                log.info("小微升级后更新线上收款交易参数到交易侧, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getOnlinePaymentTradeAppId());
                merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(newProviderParamsDO.getId(), null, "小微升级更新交易参数", subBizParamsBiz.getOnlinePaymentTradeAppId());
            }
        } catch (Exception e) {
            log.warn("小微升级后更新线上收款交易参数到交易侧失败, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getOnlinePaymentTradeAppId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级后更新线上收款交易参数到交易侧失败, merchantSn:" + newProviderParamsDO.getMerchantSn());
        }
        try {
            Map appConfigParamsOnline = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantInfo.getId(), newProviderParamsDO.getPayway(), subBizParamsBiz.getCrossCityPaymentTradeAppId());
            if (MapUtils.isNotEmpty(appConfigParamsOnline) && Objects.equals(MapUtils.getInteger(appConfigParamsOnline, CommonModel.PROVIDER), newProviderParamsDO.getProvider())) {
                log.info("小微升级后更新跨城收款交易参数到交易侧, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getCrossCityPaymentTradeAppId());
                merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(newProviderParamsDO.getId(), null, "小微升级更新交易参数", subBizParamsBiz.getCrossCityPaymentTradeAppId());
            }
        } catch (Exception e) {
            log.warn("小微升级后更新跨城收款交易参数到交易侧失败, merchantSn:{}, paramsId:{}, appId:{}", newProviderParamsDO.getMerchantSn(),newProviderParamsDO.getId(), subBizParamsBiz.getCrossCityPaymentTradeAppId(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级后更新跨城收款交易参数到交易侧失败, merchantSn:" + newProviderParamsDO.getMerchantSn());
        }
    }

    private void deletePaySideParams(String merchantId, MerchantProviderParamsDO newProviderParamsDO) {
        merchantTradeParamsBiz.deletePaySideMerchantConfigParams(merchantId, newProviderParamsDO.getPayway(), newProviderParamsDO.getProvider());
        merchantTradeParamsBiz.deletePaySideMerchantAppConfigParams(merchantId, newProviderParamsDO.getPayway(), newProviderParamsDO.getProvider());
    }


    private boolean isReContractSubTasksAllFinished(Long mainId) {
        List<InternalScheduleSubTaskDO> reContractSubTasks = internalScheduleSubTaskDAO.listByMainIds(Lists.newArrayList(mainId)).stream()
                .filter(t -> StringUtils.equals(t.getTaskType(), RE_CONTRACT_SUB_TASK_TYPE)).collect(Collectors.toList());
        long notSuccessReContractTaskNum = reContractSubTasks.stream()
                .filter(t -> !(t.isProcessSuccess() || t.isProcessFail()))
                .count();
        return notSuccessReContractTaskNum == 0;
    }



    private boolean isReContractSubTask(InternalScheduleSubTaskDO subTaskDO) {
        return StringUtils.equals(subTaskDO.getTaskType(), RE_CONTRACT_SUB_TASK_TYPE);
    }

    /**
     * 风控人工审核营业执照认证通过
     *      如果要进件，生成相应的子任务，任务状态变更为可执行
     *      不进件，修改任务状态为可执行
     */
    public void riskManualLicenceAuditPass(String merchantSn) {
        log.info("商户营业执照认证风控人工审核通过, merchantSn: {}", merchantSn);
        Optional<InternalScheduleMainTaskDO> taskOpt = getMerchantLicenceCertificationReadyToScheduleTask(merchantSn);
        if (!taskOpt.isPresent()) {
            log.error("商户风控人工审核通过, 未成功生成营业执照认证任务, merchantSn:{}", merchantSn);
            sleepAndNotifyRiskMicroContractFail(merchantSn, "进件失败，未成功生成营业执照认证任务");
            return;
        }
        InternalScheduleMainTaskDO mainTask = taskOpt.get();
        if (!isUpgradeNeedContractToAcquirer(merchantSn)) {
            handleMainTaskMerchantIsNotMicro(mainTask);
            return;
        }
        handleMainTaskMerchantIsMicro(merchantSn, mainTask);
    }

    private void sleepAndNotifyRiskMicroContractFail(String merchantSn, String reason) {
        try {
            if (isUpgradeNeedContractToAcquirer(merchantSn)) {
                log.info("商户{}需要重新进件,但是生成营业执照认证任务失败，5s后通知风控侧进件失败", merchantSn);
                TimeUnit.SECONDS.sleep(5);
                Map<String, Object> promptMessageMap = getMicroUpgradeCheckFailPromptMessageMap(false, reason);
                riskMerchantBusinessLicenseAuditService.processEntryResult(new RiskEntryResult(merchantSn, false, promptMessageMap, reason));
            }
        } catch (InterruptedException e) {
            log.warn("sleepAndNotifyRiskMicroContractFail error, merchantSn:{}", merchantSn, e);
        }
    }

    /**
     * 对于小微商户，需要生成进件子任务和切参数子任务
     *
     * @param merchantSn 商户号
     * @param mainTask   主任务
     */
    public void handleMainTaskMerchantIsMicro(String merchantSn, InternalScheduleMainTaskDO mainTask) {
        try {
            Tuple2<List<String>, String> reContractAcquirers = listNeedToReContractAcquirers(merchantSn, mainTask.getAcquirer());
            if (CollectionUtils.isEmpty(reContractAcquirers.get_1())) {
                log.error("营业执照升级，获取需要重新进件的收单机构为空, merchantSn:{}, taskId:{}, reason:{}", merchantSn, mainTask.getId(), reContractAcquirers.get_2());
                microUpgradeMainTaskFailed("营业执照升级，获取需要重新进件的收单机构为空，" + reContractAcquirers.get_2(), merchantSn, mainTask);
                return;
            }
            List<InternalScheduleSubTaskDO> scheduleSubTaskDOS = buildAllSubTasks(reContractAcquirers.get_1(), mainTask);
            if (CollectionUtils.isEmpty(scheduleSubTaskDOS) || scheduleSubTaskDOS.size() == 1) {
                log.error("营业执照升级，重新入网子任务为空, merchantSn:{}, taskId:{}", merchantSn, mainTask.getId());
                microUpgradeMainTaskFailed("营业执照升级，构建重新入网子任务为空", merchantSn, mainTask);
                return;
            }
            mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_ENABLE.getValue());
            mainTask.setRemark("风控人工审核通过，小微商户，重新入网切参数");
            internalScheduleMainTaskDAO.updateMainTaskAndInsertSubTasks(mainTask, scheduleSubTaskDOS);
        } catch (Exception e) {
            log.error("小微升级, 构建重新入网子任务失败, merchantSn:{}, taskId:{}", merchantSn, mainTask.getId(), e);
            microUpgradeMainTaskFailed("营业执照升级，构建重新入网子任务异常，原因：" + e.getMessage(), merchantSn, mainTask);
        }
    }

    private void microUpgradeMainTaskFailed(String msg, String merchantSn, InternalScheduleMainTaskDO mainTask) {
        log.info("小微商户营业执照认证任务失败, merchantSn:{}, msg:{}", merchantSn, msg);
        mainTask.updateStatusFailed(msg);
        internalScheduleMainTaskDAO.updateByPrimaryKeySelective(mainTask);
        noticeRiskMicroContractAndUpgradeTaskResult(merchantSn, mainTask,false, msg);
    }

    /**
     * 对于非小微，直接更新营业执照，变更任务状态
     *
     * @param mainTask 主任务
     */
    private void handleMainTaskMerchantIsNotMicro(InternalScheduleMainTaskDO mainTask) {
        updateBusinessLicenseAndSubsidiaryInformation(mainTask, false);
        mainTask.setStatus(InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue());
        mainTask.setRemark("风控人工审核通过,非小微商户直接变更营业执照");
        internalScheduleMainTaskDAO.updateByPrimaryKeySelective(mainTask);
    }

    private int compareStatus(Integer status1, Integer status2) {
        if (status1 == null && status2 == null) return 0;
        if (status1 == null) return -1;
        if (status2 == null) return 1;
        return Integer.compare(status1, status2);
    }

    private List<InternalScheduleSubTaskDO> buildAllSubTasks(List<String> reContractAcquirers, InternalScheduleMainTaskDO mainTask) {
        List<InternalScheduleSubTaskDO> scheduleSubTaskDOS = Lists.newArrayListWithCapacity(reContractAcquirers.size());
        Map<String/*acquirer*/, Map<String/*payWay*/, List<MerchantProviderParamsDO>>> acquirerParams = merchantTradeParamsBiz.getMerchantAcquirerParamsMap(mainTask.getMerchantSn());
        int priority = 0;
        for (String reContractAcquirer : reContractAcquirers) {
            if (!acquirerParams.containsKey(reContractAcquirer)) {
                log.warn("商户缺少交易参数,merchantSn={},acquirer={}", mainTask.getMerchantSn(), reContractAcquirer);
                continue;
            }
            Map<String, List<MerchantProviderParamsDO>> payWayParams = acquirerParams.get(reContractAcquirer);
            if (!payWayParams.containsKey(PaywayEnum.ACQUIRER.getValue().toString())) {
                log.warn("商户缺少收单机构交易参数,merchantSn={},acquirer={}", mainTask.getMerchantSn(), reContractAcquirer);
                continue;
            }
            if (!payWayParams.containsKey(PaywayEnum.WEIXIN.getValue().toString()) || !payWayParams.containsKey(PaywayEnum.ALIPAY.getValue().toString())) {
                log.warn("商户收单机构缺少微信或者支付宝交易参数,merchantSn={},acquirer={}", mainTask.getMerchantSn(), reContractAcquirer);
                continue;
            }
            buildReContractSubTask(mainTask, reContractAcquirer, ++priority, payWayParams).ifPresent(scheduleSubTaskDOS::add);
        }
        scheduleSubTaskDOS.add(buildChangeParamsSubTask(mainTask, ++priority));
        return scheduleSubTaskDOS;
    }

    private Optional<InternalScheduleSubTaskDO> buildReContractSubTask(InternalScheduleMainTaskDO mainTask, String reContractAcquirer, int priority,
                                                                       Map<String, List<MerchantProviderParamsDO>> payWayParams) {
        Optional<AcquirerSharedAbility> acquirerAbilityOpt = acquirerFacade.getSharedAbilityByAcquirer(reContractAcquirer);
        if (!acquirerAbilityOpt.isPresent()) {
            log.warn("收单机构不支持, acquirer:{}", reContractAcquirer);
            return Optional.empty();
        }
        AcquirerSharedAbility acquirerSharedAbility = acquirerAbilityOpt.get();
        InternalScheduleSubTaskDO reContractSubTask = new InternalScheduleSubTaskDO();
        reContractSubTask.setPriority(priority);
        reContractSubTask.setMerchantSn(mainTask.getMerchantSn());
        reContractSubTask.setType(getTaskType().getValue());
        reContractSubTask.setTaskType(RE_CONTRACT_SUB_TASK_TYPE);
        reContractSubTask.setMainTaskId(mainTask.getId());
        reContractSubTask.setAcquirer(reContractAcquirer);
        StringExtensionUtils.ifNotBlank(acquirerSharedAbility.getAcquirerInfo().getProvider(), t -> reContractSubTask.setProvider(Integer.valueOf(t)));
        reContractSubTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.NO.getValue());
        reContractSubTask.setRemark("重新向收单机构进件");
        SubTaskContextBOInner subTaskContextBOInner = new SubTaskContextBOInner();
        // 走到这里 payWayParams必定包含了收单机构，微信，支付宝的参数
        subTaskContextBOInner.setOldPayWayParamsMap(payWayParams);
        String oldUnionMerchantId = acquirerSharedAbility.getUnionMerchantId(
                CollectionUtils.isEmpty(payWayParams.get(PaywayEnum.UNIONPAY.getValue().toString())) ? null : payWayParams.get(PaywayEnum.UNIONPAY.getValue().toString()).get(0),
                payWayParams.get(PaywayEnum.ACQUIRER.getValue().toString()).get(0));
        subTaskContextBOInner.setOldUnionMerchantId(oldUnionMerchantId);
        subTaskContextBOInner.setOldAcquirerMerchantId(payWayParams.get(PaywayEnum.ACQUIRER.getValue().toString()).get(0).getPayMerchantId());
        getLicenceCertificationAcquirerHandlerOpt(reContractAcquirer)
                .ifPresent(handler -> subTaskContextBOInner.setOldMerchantTermNo(handler.getContractTermNo(mainTask.getMerchantSn())));
        reContractSubTask.setContext(JSON.toJSONString(subTaskContextBOInner));
        return Optional.of(reContractSubTask);
    }

    private InternalScheduleSubTaskDO buildChangeParamsSubTask(InternalScheduleMainTaskDO mainTask, int priority) {
        InternalScheduleSubTaskDO changeParamsSubTask = new InternalScheduleSubTaskDO();
        changeParamsSubTask.setType(getTaskType().getValue());
        changeParamsSubTask.setMerchantSn(mainTask.getMerchantSn());
        changeParamsSubTask.setTaskType(CHANGE_PARAMS_SUB_TASK_TYPE);
        changeParamsSubTask.setPriority(priority);
        changeParamsSubTask.setMainTaskId(mainTask.getId());
        changeParamsSubTask.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        changeParamsSubTask.setRemark("等待重新进件子任务完成后，切换参数");
        return changeParamsSubTask;
    }

    /**
     * fuyou的商户，进件之前haike和lkl做授权校验，选择已授权的重新入网
     * haike或者lkl的商户，进件前不做授权校验，进件后，切收单机构做授权校验(不切收单机构不做校验)
     */
    private Tuple2<List<String>, String> listNeedToReContractAcquirers(String merchantSn, String inUseAcquirer) {
        List<String> acquirers = applicationApolloConfig.listEnableMicroLicenseUpgradeReContractedAcquirers();
        List<String> reContractAcquirers = Lists.newArrayList();
        StringBuilder promptMsg = new StringBuilder();
        if (StringUtils.equals(AcquirerTypeEnum.FU_YOU.getValue(), inUseAcquirer)) {
            for (String acquirer : acquirers) {
                boolean merchantContractSuccessAndEnableTrade = merchantContractService.isMerchantContractSuccessAndEnableTrade(merchantSn, acquirer);
                if (merchantContractSuccessAndEnableTrade) {
                    reContractAcquirers.add(acquirer);
                } else {
                    promptMsg.append("收单机构:").append(acquirer).append(" 进件尚未成功或者微信支付宝未完成授权验证; ");
                }
            }
            return new Tuple2<>(reContractAcquirers, CollectionUtils.isEmpty(reContractAcquirers) ?  promptMsg.toString() : null);
        }
        for (String acquirer : acquirers) {
            boolean merchantContractSuccess = merchantContractService.isMerchantContractSuccess(merchantSn, acquirer);
            if (merchantContractSuccess) {
                reContractAcquirers.add(acquirer);
            } else {
                promptMsg.append("收单机构:").append(acquirer).append(" 进件尚未成功; ");
            }
        }
        return new Tuple2<>(reContractAcquirers, CollectionUtils.isEmpty(reContractAcquirers) ?  promptMsg.toString() : null);
    }


    private void noticeRiskMicroContractAndUpgradeTaskResult(String merchantSn, InternalScheduleMainTaskDO mainTaskDO, boolean success, String reason) {
        BusinessLicenseUpdateDTO businessLicenseReqDTO = null;
        try {
            MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
            businessLicenseReqDTO = mainTaskContextBOInner.getBusinessLicenseReqDTO();
        } catch (Exception e) {
            log.warn("小微升级通知风控小微升级结果,解析任务上下文失败, taskId={}", mainTaskDO.getId(), e);
        }
        if (Objects.isNull(businessLicenseReqDTO) || businessLicenseReqDTO.needToNotifyRiskResult()) {
            log.info("通知风控小微升级结果,商户:{},结果:{},reason:{}", merchantSn, success, reason);
            try {
                Map<String, Object> promptMessageMap = getMicroUpgradeCheckFailPromptMessageMap(success, reason);
                riskMerchantBusinessLicenseAuditService.processEntryResult(new RiskEntryResult(merchantSn, success, promptMessageMap, reason));
            } catch (Exception e) {
                chatBotUtil.sendMessageToMicroUpgradeChatBot("商户:" + merchantSn + ",任务" + mainTaskDO.getId() + " 通知风控小微升级结果: " + success + "失败");
                log.warn("商户:{},任务{} 通知风控小微升级结果: {}失败", merchantSn, mainTaskDO.getId(), success, e);
            }
        }
    }

    private Map<String, Object>  getMicroUpgradeCheckFailPromptMessageMap(boolean success, String originalFailReason) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put(RiskEntryResult.APP, originalFailReason);
        messageMap.put(RiskEntryResult.CRM, originalFailReason);
        messageMap.put(RiskEntryResult.SPA, originalFailReason);
        if (success) {
            return messageMap;
        }
        List<ErrorInfoPromptTextRspDTO> promptMessages = null;
        try {
            promptMessages = errorCodeManageService.getAllEndPointPromptMessage(originalFailReason, MICRO_UPGRADE_MSG_PLATFORM);
        } catch (Exception e) {
            log.warn("获取微小微升级校验失败提示信息失败, 是否成功: {}, originalReason:{}", success, originalFailReason, e);
        }
        if (CollectionUtils.isEmpty(promptMessages)) {
            messageMap.put(RiskEntryResult.SPA, "请审核人员按照审核规则正常审核，并将案例反馈给业务管理员。失败原因：" + originalFailReason);
            return messageMap;
        }
        Map<ErrorMsgViewEndpointTypeEnum, String> endpointTypeEnumStringMap = promptMessages.stream()
                .collect(ExtCollectors.toMap(ErrorInfoPromptTextRspDTO::getErrorMsgViewEndpointTypeEnum, ErrorInfoPromptTextRspDTO::getPromptText, (t1, t2) -> t1));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.APP, t -> messageMap.put(RiskEntryResult.APP, t));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.CRM, t -> messageMap.put(RiskEntryResult.CRM, t));
        com.wosai.upay.job.refactor.utils.MapUtils.ifPresent(endpointTypeEnumStringMap, ErrorMsgViewEndpointTypeEnum.SPA, t -> messageMap.put(RiskEntryResult.SPA, t));
        return messageMap;
    }

    private void updateBusinessLicenseAndSubsidiaryInformation(InternalScheduleMainTaskDO mainTask, boolean isMicroUpgrade) {
        try {
            String merchantSn = mainTask.getMerchantSn();
            MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTask.getContext(), MainTaskContextBOInner.class);
            BusinessLicenseUpdateDTO updateReqDTO = mainTaskContextBOInner.getBusinessLicenseReqDTO();
            MerchantBusinessLicenseUpdateVo licenseUpdateVo = buildBusinessLicenseUpdateVo(merchantSn, updateReqDTO);
            if (isMicroUpgrade) {
                successCertificateMicroRecordToRedis(merchantSn);
            }
            boolean needSyncAccountToAcquirer = updateBankCardInfoAndReturnSyncAcquirerFlag(licenseUpdateVo.getMerchant_id(), merchantSn, updateReqDTO);
            bankBusinessLicenseService.updateBusinessLicenseByMerchantId(licenseUpdateVo);
            if (!isMicroUpgrade && needSyncAccountToAcquirer) {
                syncAccountToAcquirer(merchantSn);
            }
            deleteTempBusinessLicenseAndMerchant(licenseUpdateVo.getMerchant_id(), merchantSn);
        } catch (Exception e) {
            log.warn("商户重新入网成功，已经变更交易参数，但是更新营业执照信息出现异常,需要人工介入, merchantSn:{}", mainTask.getMerchantSn(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("商户" + mainTask.getMerchantSn() + "任务"
                    + mainTask.getId() + "重新入网成功，已经变更交易参数，但是更新营业执照信息出现异常,需要人工介入");
        }
    }

    private void syncAccountToAcquirer(String merchantSn) {
        try {
            contractEventService.refreshMerchantBankAccount(merchantSn, "system");
        } catch (Exception e) {
            log.warn("商户同步结算信息到收单机构出现异常, merchantSn:{}", merchantSn, e);
        }
    }


    private void deleteTempBusinessLicenseAndMerchant(String merchantId, String merchantSn) {
        try {
            merchantBusinessLicenseService.delMcMerchantBusinessLicense(merchantId);
            merchantService.delMcMerchant(merchantId);
        } catch (Exception e) {
            log.warn("删除营业执照或者商户中间表错误, merchantSn:{}", merchantSn, e);
        }
    }

    private boolean updateBankCardInfoAndReturnSyncAcquirerFlag(String merchantId, String merchantSn, BusinessLicenseUpdateDTO updateReqDTO) {
        MerchantBankAccount merchantBankAccount = bankBusinessLicenseService.getMerchantBankAccountByMerchantId(merchantId);
        boolean isUpdateMerchantBank = false;
        boolean needSyncAccountToAcquirer = false;
        MerchantBankAccountUpdateVo vo = new MerchantBankAccountUpdateVo();
        vo.setNumber(merchantBankAccount.getNumber());  // core-b校验该字段非空
        //授权函不为空，则把授权函记录到银行卡上
        if (StringUtils.isNotEmpty(updateReqDTO.getLetterOfAuthorization())) {
            isUpdateMerchantBank = true;
            vo.setMerchant_id(merchantId).setLetter_of_authorization(updateReqDTO.getLetterOfAuthorization());
        }
        // 结算人等于法人(必填将法人代表证件照片、有效期同步至间联扫码银行卡表)
        if(merchantBankAccount.getIdentity().equals(updateReqDTO.getLegalPersonIdNumber())) {
            isUpdateMerchantBank = true;
            vo.setMerchant_id(merchantId)
                    .setHolder_id_front_photo(basePhotoUrlAgain(updateReqDTO.getLegalPersonIdCardFrontPhoto()))
                    .setHolder_id_back_photo(basePhotoUrlAgain(updateReqDTO.getLegalPersonIdCardBackPhoto()))
                    .setId_validity(updateReqDTO.getIdValidity());
            if (BankAccountTypeEnum.isPersonal(merchantBankAccount.getType())
                    && !StringUtils.equals(updateReqDTO.getLegalPersonName(), merchantBankAccount.getHolder())) {
                needSyncAccountToAcquirer = true;
                vo.setHolder(updateReqDTO.getLegalPersonName());
            }
        }
        if(isUpdateMerchantBank){
            try {
                bankBusinessLicenseService.updateMerchantBankAccountBasicInfo(vo);
                return needSyncAccountToAcquirer;
            } catch (Exception e) {
                log.warn("营业执照认证,更新银行卡失败 merchantId:{}", merchantId);
                chatBotUtil.sendMessageToMicroUpgradeChatBot("营业执照认证,更新银行卡失败 merchantId:" + merchantId);
                return false;
            }
        }
        return false;
    }


    private String basePhotoUrlAgain(String photoUrl) {
        if (StringUtils.isBlank(photoUrl)) {
            return photoUrl;
        }
        int questionMarkIndex = photoUrl.indexOf("?");
        if (questionMarkIndex != -1) {
            return photoUrl.substring(0, questionMarkIndex);
        }
        return photoUrl;
    }


        private MerchantBusinessLicenseUpdateVo buildBusinessLicenseUpdateVo(String merchantSn, BusinessLicenseUpdateDTO updateReqDTO) {
        MerchantBusinessLicenseUpdateVo licenseUpdateVo = new MerchantBusinessLicenseUpdateVo();
        licenseUpdateVo.setMerchant_id(merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId());
        licenseUpdateVo.setPhoto(basePhotoUrlAgain(updateReqDTO.getPhoto()));
        licenseUpdateVo.setNumber(updateReqDTO.getNumber());
        licenseUpdateVo.setName(updateReqDTO.getName());
        licenseUpdateVo.setValidity(updateReqDTO.getValidity());
        licenseUpdateVo.setLegal_person_name(updateReqDTO.getLegalPersonName());
        licenseUpdateVo.setAddress(updateReqDTO.getLicenseAddress());
        licenseUpdateVo.setType(updateReqDTO.getLicenseType());
        licenseUpdateVo.setLegal_person_id_type(updateReqDTO.getIdType());
        licenseUpdateVo.setLegal_person_id_card_front_photo(basePhotoUrlAgain(updateReqDTO.getLegalPersonIdCardFrontPhoto()));
        licenseUpdateVo.setLegal_person_id_card_back_photo(basePhotoUrlAgain(updateReqDTO.getLegalPersonIdCardBackPhoto()));
        licenseUpdateVo.setLegal_person_id_number(updateReqDTO.getLegalPersonIdNumber());
        licenseUpdateVo.setLegal_person_id_card_address(updateReqDTO.getLegalPersonIdCardAddress());
        licenseUpdateVo.setLegal_person_id_card_issuing_authority(updateReqDTO.getLegalPersonIdCardIssuingAuthority());
        licenseUpdateVo.setId_validity(updateReqDTO.getIdValidity());
        licenseUpdateVo.setVerify_status(VerifyStatusEnum.SUCCESS.getValue());
        return licenseUpdateVo;
    }

    /**
     * 风控人工审核营业执照认证拒绝
     * 主任务状态改为失败
     */
    public void riskManualLicenceAuditReject(String merchantSn) {
        log.info("商户营业执照认证风控人工审核拒绝, merchantSn:{}", merchantSn);
        Optional<InternalScheduleMainTaskDO> taskOpt = getMerchantLicenceCertificationReadyToScheduleTask(merchantSn);
        if (!taskOpt.isPresent()) {
            return;
        }
        InternalScheduleMainTaskDO mainTask = taskOpt.get();
        mainTask.updateStatusFailed("风控人工审核未通过");
        internalScheduleMainTaskDAO.updateByPrimaryKeySelective(mainTask);
    }

    private Optional<InternalScheduleMainTaskDO> getMerchantLicenceCertificationReadyToScheduleTask(String merchantSn) {
        List<InternalScheduleMainTaskDO> mainTasks = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, getTaskType().getValue())
                .stream().filter(t -> Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue())
                        && Objects.equals(t.getEnableScheduledStatus(), ScheduleEnum.SCHEDULE_DISABLE.getValue()))
                .sorted((t1, t2) -> t2.getCtime().compareTo(t1.getCtime())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainTasks)) {
            log.warn("商户不存在待处理的且调度状态等待触发为可调度的营业执照认证任务, merchantSn:{}", merchantSn);
            return Optional.empty();
        }
        if (mainTasks.size() > 1) {
            log.warn("商户存在多个待处理的营业执照认证任务，取最新的一个, merchantSn:{}, mainTaskId = {}", merchantSn, mainTasks.get(0).getId());
        }
        return Optional.of(mainTasks.get(0));
    }

    /**
     * 营业执照升级生成主任务
     *
     * @param merchantSn    商户号
     * @param updateReqDTO  营业执照更新请求
     */
    public void insertMainTaskForUpdateTask(String merchantSn, BusinessLicenseUpdateDTO updateReqDTO) {
        buildAndSaveMainTask(merchantSn, updateReqDTO, "等待风控人工审核", false);
    }

    private InternalScheduleMainTaskDO buildAndSaveMainTask(String merchantSn, BusinessLicenseUpdateDTO updateReqDTO, String remark, boolean forceShowOnSpa) {
        InternalScheduleMainTaskDO mainTask = new InternalScheduleMainTaskDO();
        mainTask.setMerchantSn(merchantSn);
        mainTask.setType(getTaskType().getValue());
        Optional<ContractStatusDO> contractStatus = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatus.isPresent()
                || !Objects.equals(contractStatus.get().getStatus(), ContractStatus.STATUS_SUCCESS)
                || StringUtils.isBlank(contractStatus.get().getAcquirer())) {
            log.warn("商户未进件成功或未关联收单机构, merchantSn:{}", merchantSn);
            throw new ContractBizException("尚未开通间连扫码");
        }
        mainTask.setAcquirer(contractStatus.get().getAcquirer());
        mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_DISABLE.getValue());
        mainTask.setResult("待处理");
        mainTask.setRemark(remark);
        mainTask.setAffectStatusSubTaskNum(1);
        mainTask.setLastScheduledTime(new Timestamp(System.currentTimeMillis()));
        MainTaskContextBOInner mainTaskContextBOInner = new MainTaskContextBOInner(updateReqDTO);
        mainTaskContextBOInner.setOldInUseAcquirer(contractStatus.get().getAcquirer());
        mainTaskContextBOInner.setShowTaskOnSpa(forceShowOnSpa || isUpgradeNeedContractToAcquirer(merchantSn));
        merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, contractStatus.get().getAcquirer()).ifPresent(mainTaskContextBOInner::setOldInUseAcquirerMerchantId);
        mainTask.setContext(JSON.toJSONString(mainTaskContextBOInner));
        forSyncToContractTask(merchantSn, mainTask);
        internalScheduleMainTaskDAO.insertOne(mainTask);
        return mainTask;
    }

    private void forSyncToContractTask(String merchantSn, InternalScheduleMainTaskDO mainTask) {
        try {
            // 仅仅为了spa展示
            Map<String, Object> paramsContextMap = buildContractContext(mainTask);
            MainTaskContextBOInner contextBOInner = JSON.parseObject(mainTask.getContext(), MainTaskContextBOInner.class);
            contextBOInner.setMerchant(MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_MERCHANT));
            contextBOInner.setBankAccount(MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BANK_ACCOUNT));
            contextBOInner.setBankInfo(MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BANK_INFO));
            contextBOInner.setMerchantBusinessLicense(MapUtils.getObject(paramsContextMap, ParamContextBiz.KEY_BUSINESS_LICENCE));
            mainTask.setContext(JSON.toJSONString(contextBOInner));
        } catch (Exception e) {
            log.warn("构建营业执照升级主任务context失败,merchantSn:{}", merchantSn, e);
        }
    }

    public Boolean isUpgradeNeedContractToAcquirer(String merchantSn) {
        return merchantBasicInfoBiz.isMerchantLicenseMicro(merchantSn);
    }


    /**
     * 内部构建营业执照升级任务
     *
     * @param merchantSn 商户号
     * @param updateDTO 营业执照更新请求
     * @return 任务结果
     */
    public InsertLicenceUpdateTaskResultRspDTO internalBuildTask(String merchantSn, BusinessLicenseUpdateDTO updateDTO) {
        try {
            Optional<ContractStatusDO> contractStatusDO = contractStatusDAO.getByMerchantSn(merchantSn);
            if (!contractStatusDO.isPresent() || !Objects.equals(contractStatusDO.get().getStatus(), ContractStatus.STATUS_SUCCESS)) {
                log.error("商户未进件成功,merchantSn:{}", merchantSn);
                return InsertLicenceUpdateTaskResultRspDTO.fail("商户未进件成功");
            }
            String acquirer = contractStatusDO.get().getAcquirer();
            if (!applicationApolloConfig.listEnableMicroLicenseUpgradeReContractedAcquirers().contains(acquirer)
                    && !StringUtils.equals(acquirer, AcquirerTypeEnum.FU_YOU.getValue())) {
                return InsertLicenceUpdateTaskResultRspDTO.fail("商户当前所在收单机构不支持");
            }
            InternalScheduleMainTaskDO mainTask = buildAndSaveMainTask(merchantSn, updateDTO, INTERNAL_MICRO_UPGRADE_REMARK, true);
            Tuple2<List<String>, String> tuple2 = listNeedToReContractAcquirers(merchantSn, mainTask.getAcquirer());
            List<String> reContractAcquirers = tuple2.get_1();
            if (CollectionUtils.isEmpty(reContractAcquirers)) {
                internalScheduleMainTaskDAO.deleteByPrimaryKey(mainTask.getId());
                return InsertLicenceUpdateTaskResultRspDTO.fail(tuple2.get_2());
            }
            List<InternalScheduleSubTaskDO> scheduleSubTaskDOS = buildAllSubTasks(reContractAcquirers, mainTask);
            if (CollectionUtils.isEmpty(scheduleSubTaskDOS)) {
                internalScheduleMainTaskDAO.deleteByPrimaryKey(mainTask.getId());
                return InsertLicenceUpdateTaskResultRspDTO.fail("进件子任务构建为空");
            }
            mainTask.setEnableScheduledStatus(ScheduleEnum.SCHEDULE_ENABLE.getValue());
            internalScheduleMainTaskDAO.updateMainTaskAndInsertSubTasks(mainTask, scheduleSubTaskDOS);
            return InsertLicenceUpdateTaskResultRspDTO.success();
        } catch (Exception e) {
            log.error("内部调度构建营业执照升级任务失败,merchantSn:{}", merchantSn, e);
            return InsertLicenceUpdateTaskResultRspDTO.fail("内部调度构建营业执照升级任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据商户号和收单机构商户号判断该商户是否已经做了小微升级
     *
     * @param merchantSn         商户号
     * @param acquirerMerchantId 收单机构商户号
     * @return true 已做，false 未做
     */
    public boolean isAcquirerMerchantAlreadyUpgrade(String merchantSn, String acquirerMerchantId) {
        List<InternalScheduleMainTaskDO> internalScheduleMainTaskDOS = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, getTaskType().getValue())
                .stream()
                .filter(InternalScheduleMainTaskDO::taskSuccess).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(internalScheduleMainTaskDOS)) {
            return false;
        }
        Optional<MerchantProviderParamsDO> deletedParams = merchantProviderParamsDAO.listDeletedParamsBySn(merchantSn).stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .filter(t -> StringUtils.equals(t.getPayMerchantId(), acquirerMerchantId) || StringUtils.equals(t.getProviderMerchantId(), acquirerMerchantId))
                .findAny();
        return deletedParams.isPresent();
    }

    private boolean isSubTaskContextContainAcquirerMerchant(String subTaskContext, String acquirerMerchantId) {
        if (StringUtils.isBlank(subTaskContext)) {
            return false;
        }
        SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskContext, SubTaskContextBOInner.class);
        if (subTaskContextBOInner == null) {
            return false;
        }
        return StringUtils.equals(subTaskContextBOInner.getOldAcquirerMerchantId(), acquirerMerchantId);
    }

    @Data
    @AllArgsConstructor
    public static class MainTaskContextBOInner {
        public MainTaskContextBOInner(BusinessLicenseUpdateDTO businessLicenseReqDTO) {
            this.businessLicenseReqDTO = businessLicenseReqDTO;
        }

        public MainTaskContextBOInner() {
        }

        private BusinessLicenseUpdateDTO businessLicenseReqDTO;
        private boolean showTaskOnSpa;
        private String oldInUseAcquirer;
        private String oldInUseAcquirerMerchantId;
        private String newAcquirer;
        private String newAcquirerMerchantId;
        private List<String> reContractAcquirers;
        private List<String> successReContractAcquirers;
        private List<String> successReContractAndUpdateParamsAcquirers;
        private Object bankAccount;
        private Object merchant;
        private Object bankInfo;
        private Object merchantBusinessLicense;

        public List<String> getReContractAcquirers() {
            return Objects.isNull(reContractAcquirers) ? new ArrayList<>() : new ArrayList<>(reContractAcquirers);
        }

        public List<String> getSuccessReContractAcquirers() {
            return Objects.isNull(successReContractAcquirers) ? new ArrayList<>() : new ArrayList<>(successReContractAcquirers);
        }

        public List<String> getSuccessReContractAndUpdateParamsAcquirers() {
            return Objects.isNull(successReContractAndUpdateParamsAcquirers) ? new ArrayList<>() : new ArrayList<>(successReContractAndUpdateParamsAcquirers);
        }

        public boolean getShowTaskOnSpa() {
            return showTaskOnSpa;
        }

        public void setShowTaskOnSpa(boolean showTaskOnSpa) {
            this.showTaskOnSpa = showTaskOnSpa;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskContextBOInner {
        private Map<String/*payWay*/, List<MerchantProviderParamsDO>> oldPayWayParamsMap;  // 新的数据格式
        private List<String> newParamsIdList;
        private List<SubBizParamsDO> oldSubBizParams;
        private String oldUnionMerchantId;
        private String oldAcquirerMerchantId;
        private String oldMerchantTermNo;

        public Map<String, List<MerchantProviderParamsDO>> getOldPayWayParamsMap() {
            return Objects.isNull(oldPayWayParamsMap) ? new HashMap<>() : new HashMap<>(oldPayWayParamsMap);
        }

        public List<String> getNewParamsIdList() {
            return Objects.isNull(newParamsIdList) ? new ArrayList<>() : new ArrayList<>(newParamsIdList);
        }

        public List<SubBizParamsDO> getOldSubBizParams() {
            return Objects.isNull(oldSubBizParams) ? new ArrayList<>() : new ArrayList<>(oldSubBizParams);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskRequestDTOInner {
        public SubTaskRequestDTOInner(Map<String, Object> reContractReqMap) {
            this.reContractReqMap = reContractReqMap;
        }

        private Map<String, Object> reContractReqMap;
        private Map<String, Object> queryResultReqMap;

        public Map<String, Object> getReContractReqMap() {
            return Objects.isNull(reContractReqMap) ? new HashMap<>() : new HashMap<>(reContractReqMap);
        }

        public Map<String, Object> getQueryResultReqMap() {
            return Objects.isNull(queryResultReqMap) ? new HashMap<>() : new HashMap<>(queryResultReqMap);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskResponseDTOInner {
        public SubTaskResponseDTOInner(Map<String, Object> reContractRsqMap) {
            this.reContractRsqMap = reContractRsqMap;
        }

        private Map<String, Object> reContractRsqMap;
        private Map<String, Object> queryResultRsqMap;
        private String newUnionMerchantId;
        private String newAcquirerMerchantId;
        private String newTermId;
        private String newShopId;
        private String contractId;

        public Map<String, Object> getReContractRsqMap() {
            return Objects.isNull(reContractRsqMap) ? new HashMap<>() : new HashMap<>(reContractRsqMap);
        }

        public Map<String, Object> getQueryResultRsqMap() {
            return Objects.isNull(queryResultRsqMap) ? new HashMap<>() : new HashMap<>(queryResultRsqMap);
        }
    }
}
