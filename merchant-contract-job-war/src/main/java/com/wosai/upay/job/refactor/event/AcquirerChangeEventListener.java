package com.wosai.upay.job.refactor.event;

import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveConfigBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.enums.MerchantKeepAliveConfigStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 收单机构切换事件监听器
 * 处理两个独立的业务：1.设置线上收款申请单为失败 2.重新创建保活任务
 * 优化后确保两个业务逻辑不会互相影响
 */
@Slf4j
@Component
public class AcquirerChangeEventListener implements ApplicationListener<AcquirerChangeEvent> {

    @Autowired
    private OnlinePaymentBiz onlinePaymentBiz;
    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;
    @Autowired
    private KeepAliveConfigBiz keepAliveConfigBiz;

    @Override
    public void onApplicationEvent(AcquirerChangeEvent event) {
        String merchantSn = event.getMerchantSn();

        // 异步并行处理两个独立的业务，确保互不影响
        handleOnlinePaymentFailure(merchantSn);
        handleKeepAliveTaskCreation(merchantSn);
    }

    /**
     * 异步处理线上收款申请单失败设置
     */
    private void handleOnlinePaymentFailure(String merchantSn) {
        CompletableFuture.runAsync(() -> {
            try {
                onlinePaymentBiz.setFailWhenChangeAcquirer(merchantSn);
            } catch (Exception e) {
                log.error("线上收款申请单失败设置异常，商户: {}", merchantSn, e);
            }
        });
    }

    /**
     * 异步处理保活任务重新创建
     */
    private void handleKeepAliveTaskCreation(String merchantSn) {
        CompletableFuture.runAsync(() -> {
            try {
                KeepAliveConfigResult keepAliveConfigResult = keepAliveConfigBiz.queryKeepAliveConfig(merchantSn);
                if (Objects.nonNull(keepAliveConfigResult) &&
                        MerchantKeepAliveConfigStatusEnum.OPEN.getValue().equals(keepAliveConfigResult.getStatus())) {
                    keepAliveTaskBiz.createKeepAliveTasks(merchantSn,
                            KeepAliveTaskResultBO.createResultFromEvent("AcquirerChangeEventListener", "收单机构切换"));
                }
            } catch (Exception e) {
                log.error("保活任务创建异常，商户: {}", merchantSn, e);
            }
        });
    }
}
