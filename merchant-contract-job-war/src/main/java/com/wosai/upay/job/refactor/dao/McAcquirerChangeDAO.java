package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.McAcquirerChangeDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;

import java.sql.Timestamp;
import java.util.Objects;
import java.util.Optional;

import static com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz.INDIRECT_COMBO_SNAPSHOT;


/**
 * 商户切换收单机构记录表表数据库访问层 {@link McAcquirerChangeDO}
 * 对McAcquirerChangeMapper层做出简单封装 {@link McAcquirerChangeDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class McAcquirerChangeDAO extends AbstractBaseDAO<McAcquirerChangeDO, McAcquirerChangeDynamicMapper> {

    public McAcquirerChangeDAO(SqlSessionFactory sqlSessionFactory, McAcquirerChangeDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 获取最近一次的切换收单机构申请
     *
     * @param merchantSn     商户号
     * @param targetAcquirer 目标收单机构
     * @param gtCreateAt     大于的创建时间，防止拿到历史数据
     * @return 切换记录
     */
    public Optional<McAcquirerChangeDO> getLastedByMerchantSnAndTargetAcquirer(String merchantSn, String targetAcquirer, Timestamp gtCreateAt) {
        LambdaQueryWrapper<McAcquirerChangeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(McAcquirerChangeDO::getMerchantSn, merchantSn)
                .eq(McAcquirerChangeDO::getTargetAcquirer, targetAcquirer);
        return entityMapper.selectList(wrapper).stream().max((o1, o2) -> {
            if (o1.getCreateAt().getTime() == o2.getCreateAt().getTime()) {
                return 0;
            }
            return  o1.getCreateAt().getTime() - o2.getCreateAt().getTime() > 0 ? 1 : -1;
        });
    }

    /**
     * 获取最近一次处于切换流程中的收单机构申请
     *
     * @param merchantSn     商户号
     * @return 切换记录
     */
    public Optional<McAcquirerChangeDO> getProcessChange(String merchantSn) {
        LambdaQueryWrapper<McAcquirerChangeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(McAcquirerChangeDO::getMerchantSn, merchantSn)
                .notIn(McAcquirerChangeDO::getStatus, 19,20);
        return entityMapper.selectList(wrapper).stream().filter(x -> Objects.equals(x.getImmediately(),0)).max((o1, o2) -> {
            if (o1.getCreateAt().getTime() == o2.getCreateAt().getTime()) {
                return 0;
            }
            return  o1.getCreateAt().getTime() - o2.getCreateAt().getTime() > 0 ? 1 : -1;
        });
    }

    public Optional<McAcquirerChangeDO> getLatestHasIndirectComboSnapshotSuccessApply(String merchantSn) {
        LambdaQueryWrapper<McAcquirerChangeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(McAcquirerChangeDO::getMerchantSn, merchantSn)
                .eq(McAcquirerChangeDO::getStatus, 19)
                .like(McAcquirerChangeDO::getExtra, "%" + INDIRECT_COMBO_SNAPSHOT +"%")
                .orderByDesc(McAcquirerChangeDO::getId)
                .last("limit 1");
        return selectOne(wrapper);
    }

}
