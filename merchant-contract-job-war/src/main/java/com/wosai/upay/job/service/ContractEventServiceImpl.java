package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.consumer.DataSyncHandler;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.eventmsg.EventMsg;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.service.McRulesDecisionService;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-09-10
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractEventServiceImpl implements ContractEventService {

    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private BusinessRuleBiz businessRuleBiz;

    @Autowired
    private AgreementBiz agreementBiz;

    @Autowired
    private MutexTaskCheckBiz mutexTaskCheckBiz;

    @Autowired
    private McRulesDecisionService mcRulesDecisionService;

    @Autowired
    private TradeConfigClient tradeConfigClient;

    /**
     * 如果该任务是从结算人证件照片更新提交过来, 不改变银行卡状态
     */
    public static final String AUDIT_PLATFORM = "sp_audit";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractEvent saveContractEvent(String merchantSn, String ruleGroupId, String platform) {
        RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
        if (ruleGroup == null) {
            throw new CommonPubBizException("规则组不存在");
        }

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            if (WosaiStringUtils.isEmpty(ruleGroup.getAcquirer())) {
                throw new CommonPubBizException("商户还未进件，规则组必须指明收单机构");
            }
            // 目前业务开通场景已经不再调用该接口
            ContractGroupRuleVerifyResultBO verifyResultBO = mcRulesDecisionService.checkMerchantEligibilityToAcquirer(merchantSn, ruleGroup.getAcquirer(), NetInSceneEnum.CHANNEL_SWITCHING);
            if (!verifyResultBO.isCheckPass()) {
                throw new CommonPubBizException("商户不符合收单机构 " + ruleGroup.getAcquirer() + " 准入标准");
            }

            contractStatus = new ContractStatus()
                    .setStatus(ContractStatus.STATUS_PENDING)
                    .setMerchant_sn(merchantSn)
                    .setAcquirer(ruleGroup.getAcquirer());
            contractStatusMapper.insertSelective(contractStatus);
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            tradeConfigClient.updateClearProviderByAcquirer(merchantId, ruleGroup.getAcquirer());

        } else {
            String ac = contractStatus.getAcquirer();
            if (WosaiStringUtils.isNotEmpty(ruleGroup.getAcquirer()) && !ac.equals(ruleGroup.getAcquirer())) {
                throw new CommonPubBizException("商户已经进件，规则组不能设定收单机构");
            }
        }

        EventMsg eventMsg = new EventMsg()
                .setPlatform(platform);
        return saveEvent(merchantSn, ruleGroupId, ContractEvent.OPT_TYPE_NET_IN, eventMsg);
    }

    @Override
    public ContractEvent saveContractEventV2(String merchantSn, Long comboId, String platform) {
        NetInRuleGroups ruleGroups = businessRuleBiz.getRuleGroupId(merchantSn);
        EventMsg eventMsg = new EventMsg()
                .setPlatform(platform);
        return saveMultiContractEvent(merchantSn, ruleGroups, eventMsg);
    }

    @Override
    public ContractEvent saveFailEvent(String merchantSn, Long comboId, String platform, String failMsg) {
        NetInRuleGroups ruleGroups = businessRuleBiz.getRuleGroupId(merchantSn);
        EventMsg eventMsg = new EventMsg()
                .setPlatform(platform)
                .setSource(CollectionUtil.hashMap(
                        "fail_memo", WosaiStringUtils.isNotEmpty(failMsg) ? failMsg : "黑名单校验不通过"
                ));
        return saveMultiContractEvent(merchantSn, ruleGroups, eventMsg);

    }

    @Override
    public ContractEvent saveContractEventV3(NewMchNetInReq req) {
        NetInRuleGroups ruleGroups = businessRuleBiz.getRuleGroupId(req.getMerchantSn(), req.getOrganizationId());
        EventMsg eventMsg = new EventMsg()
                .setPlatform(req.getPlatform());
        ContractEvent contractEvent = saveMultiContractEvent(req.getMerchantSn(), ruleGroups, eventMsg);
        agreementBiz.recordAgreementForContract(req.getMerchantSn());
        return contractEvent;
    }

    @Override
    public ContractEvent saveFailEventV2(NewMchNetInReq req) {
        NetInRuleGroups ruleGroups = businessRuleBiz.getRuleGroupId(req.getMerchantSn(), req.getOrganizationId());
        EventMsg eventMsg = new EventMsg()
                .setPlatform(req.getPlatform())
                .setSource(CollectionUtil.hashMap(
                        "fail_memo", WosaiStringUtils.isNotEmpty(req.getFailMsg()) ? req.getFailMsg() : "黑名单校验不通过"
                ));
        ContractEvent contractEvent = saveMultiContractEvent(req.getMerchantSn(), ruleGroups, eventMsg);
        agreementBiz.recordAgreementForContract(req.getMerchantSn());
        return contractEvent;
    }

    private ContractEvent saveMultiContractEvent(String merchantSn, NetInRuleGroups ruleGroups, EventMsg eventMsg) {
        RuleGroup primaryRuleGroup = ruleContext.getRuleGroup(ruleGroups.getPrimaryRuleGroupId());
        if (primaryRuleGroup == null) {
            throw new CommonPubBizException("主通道规则组不存在");
        }
        if (WosaiStringUtils.isNotEmpty(ruleGroups.getSecondaryRuleGroupId())) {
            RuleGroup secondRuleGroup = ruleContext.getRuleGroup(ruleGroups.getSecondaryRuleGroupId());
            if (secondRuleGroup == null) {
                throw new CommonPubBizException("次通道规则组不存在");
            }
        }

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            if (WosaiStringUtils.isEmpty(primaryRuleGroup.getAcquirer())) {
                throw new CommonPubBizException("商户还未进件，规则组必须指明收单机构");
            }

            contractStatus = new ContractStatus()
                    .setStatus(ContractStatus.STATUS_PENDING)
                    .setMerchant_sn(merchantSn)
                    .setAcquirer(primaryRuleGroup.getAcquirer());
            contractStatusMapper.insertSelective(contractStatus);

        } else {
            String ac = contractStatus.getAcquirer();
            if (WosaiStringUtils.isNotEmpty(primaryRuleGroup.getAcquirer()) && !ac.equals(primaryRuleGroup.getAcquirer())) {
                throw new CommonPubBizException("商户已经进件，规则组不能设定收单机构");
            }
        }

        MultiProviderContractEvent event = new MultiProviderContractEvent()
                .setMerchant_sn(merchantSn)
                .setStatus(MultiProviderContractEvent.STATUS_PENDING)
                .setPrimary_group_id(ruleGroups.getPrimaryRuleGroupId())
                .setSecondary_group_id(ruleGroups.getSecondaryRuleGroupId())
                .setEvent_msg(JSON.toJSONString(eventMsg));
        multiEventMapper.insertSelective(event);
        return new ContractEvent()
                .setId(event.getId())
                .setMerchant_sn(merchantSn)
                .setRule_group_id(ruleGroups.getPrimaryRuleGroupId())
                .setEvent_msg(JSON.toJSONString(eventMsg));
    }

    @Override
    public ContractResponse refreshMerchant(String merchantSn, int eventType, String platform) {
        CommonResult result = doRefreshMerchant(merchantSn, eventType, ProviderUtil.switchAcquirerToRuleGroupId(acquirerBiz.getMerchantAcquirer(merchantSn)), platform);
        if (!result.isSuccess()) {
            return new ContractResponse().setMsg(result.getMsg());
        }
        return new ContractResponse()
                .setSuccess(true)
                .setMsg("提交成功，请在进件管理中查看状态");
    }

    @Override
    public ContractResponse refreshMerchantBankAccount(String merchantSn, String platform) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("不存在此商户:" + merchantSn);
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus) || ContractStatus.STATUS_SUCCESS != contractStatus.getStatus()) {
            return new ContractResponse().setMsg("间连扫码未开通成功，暂不支持使用该功能");
        }
        DataSyncHandler dataSyncHandler = new DataSyncHandler();
        EventMsg eventMsg = new EventMsg()
                .setPlatform(platform)
                .setOpt_type(ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS);
        List<String> list = new ArrayList<>();
        list.addAll(dataSyncHandler.merchantBankAccountChangeFields);
        list.addAll(dataSyncHandler.merchantBankAccountChangeFieldsTwo);
        eventMsg.setTable_name(DTSBean.TABLE_MERCHANT_BANK_ACCOUNT_PRE).setMsg(list);
        String acquirer = ProviderUtil.switchAcquirerToRuleGroupId(acquirerBiz.getMerchantAcquirer(merchantSn));
        if (WosaiStringUtils.isEmpty(acquirer)) {
            return new ContractResponse().setMsg("商户所在收单机构为空");
        }
        saveEvent(merchantSn, ProviderUtil.switchAcquirerToRuleGroupId(acquirer), ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS, eventMsg);
        return new ContractResponse()
                .setSuccess(true)
                .setMsg("提交成功，请在进件管理中查看状态");
    }

    @Override
    public ContractResponse rescheduleMultiProviderEvent(String merchantSn) {
        MultiProviderContractEvent multiProviderContractEvent = multiEventMapper.selectLatestMultiEventByMerchantSn(merchantSn);
        if (Objects.isNull(multiProviderContractEvent) || multiProviderContractEvent.getStatus() != MultiProviderContractEvent.STATUS_PENDING || !multiProviderContractEvent.getUpdate_at().equals(ScheduleUtil.PAUSE_DATE)) {
            return new ContractResponse().setSuccess(false).setMsg("未查询到入网事件");
        }
        multiProviderContractEvent.setResult(StringUtils.EMPTY)
                .setUpdate_at(new Date());
        multiEventMapper.updateByPrimaryKeySelective(multiProviderContractEvent);
        return new ContractResponse().setSuccess(true);
    }

    @Override
    public NetInRuleGroups getNetInGroupsByMerchantFeature(MerchantFeature merchantFeature) {
        MerchantFeatureBO merchantFeatureBO = JSON.parseObject(JSON.toJSONString(merchantFeature), MerchantFeatureBO.class);
        return businessRuleBiz.getRuleGroupId(merchantFeatureBO);
    }

    public CommonResult doRefreshMerchant(String merchantSn, int eventType, String acquirer, String platform) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("不存在此商户:" + merchantSn);
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(contractStatus) || ContractStatus.STATUS_SUCCESS != contractStatus.getStatus()) {
            return new CommonResult(CommonResult.BIZ_FAIL, "间连扫码未开通成功，暂不支持使用该功能");
        }
        String ruleGroupId = ProviderUtil.switchAcquirerToRuleGroupId(acquirer);
        if (mutexTaskCheckBiz.checkMutexTaskProcessingForEvent(merchantSn, eventType, ruleGroupId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户当前有进件任务在处理中，请等待处理完成后再点击同步");
        }
        String key = String.format("refreshMerchant-%s-%d-count", merchantSn, eventType);
        Long count = redisLock.count(key, ProviderUtil.REDIS_KEY_EXPIRE_ONE_DAY);
        if (count > 3) {
            return new CommonResult(CommonResult.BIZ_FAIL, "此功能每24小时仅可使用三次,请在进件管理中查看第一次提交的时间");
        }
        DataSyncHandler dataSyncHandler = new DataSyncHandler();
        EventMsg eventMsg = new EventMsg().setPlatform(platform).setOpt_type(eventType);
        if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == eventType) {
            eventMsg.setTable_name(DTSBean.TABLE_MERCHANT).setMsg(dataSyncHandler.merchantChangeToLklFields);
        } else if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == eventType) {
            List<String> list = new ArrayList<>();
            list.addAll(dataSyncHandler.merchantBankAccountChangeFields);
            list.addAll(dataSyncHandler.merchantBankAccountChangeFieldsTwo);
            eventMsg.setTable_name(DTSBean.TABLE_MERCHANT_BANK_ACCOUNT_PRE).setMsg(list);
        } else {
            return new CommonResult(CommonResult.BIZ_FAIL, String.format("暂不支持此类更新:%d", eventType));
        }
        ContractEvent contractEvent = saveEvent(merchantSn, ruleGroupId, eventType, eventMsg);
        return new CommonResult(CommonResult.SUCCESS, "成功", contractEvent);
    }

    private ContractEvent saveEvent(String merchantSn, String ruleGroupId, int eventType, EventMsg eventMsg) {
        ContractEvent contractEvent = new ContractEvent()
                .setMerchant_sn(merchantSn)
                .setEvent_type(eventType)
                .setEvent_msg(JSON.toJSONString(eventMsg))
                .setRule_group_id(ruleGroupId);
        contractEventMapper.insertSelective(contractEvent);
        return contractEvent;
    }

    /**
     * 门店开通间连扫码，校验门店是否可以入指定的收单机构
     *
     * @param merchantSn 商户号
     * @param storeId    门店号
     * @return 校验结果
     */
    @Override
    public CuaCommonResultDTO checkStoreEligibilityToAcquirer(String merchantSn, String storeId) {
        ContractGroupRuleVerifyResultBO contractGroupRuleVerifyResultBO = mcRulesDecisionService.checkStoreEligibilityToAcquirer(merchantSn, storeId, NetInSceneEnum.BUSINESS_OPENING);
        CuaCommonResultDTO cuaCommonResultDTO = new CuaCommonResultDTO();
        cuaCommonResultDTO.setSuccess(contractGroupRuleVerifyResultBO.isCheckPass());
        cuaCommonResultDTO.setMessage(contractGroupRuleVerifyResultBO.getMessage());
        return cuaCommonResultDTO;
    }
}
