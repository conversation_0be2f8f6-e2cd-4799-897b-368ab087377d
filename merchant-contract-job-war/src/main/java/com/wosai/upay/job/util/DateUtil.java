package com.wosai.upay.job.util;

import com.wosai.upay.side.service.GeneralRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Component
public class DateUtil {

    @Autowired
    private GeneralRuleService generalRuleService;

    public static final DateTimeFormatter ISO_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 使用斜杠分隔的日期格式
     */
    public static final DateTimeFormatter SLASH_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");


    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 获取给定日期之前的所有非工作日
     * 此方法用于找出指定日期前的所有非工作日，直到遇到第一个工作日为止
     *
     * @param localDate 输入的日期，作为查找非工作日的起始点
     * @return 一个包含非工作日的列表，从给定日期开始向前数，直到遇到第一个工作日
     */
    public List<LocalDate> getNonWorkingDaysBefore(LocalDate localDate) {
        String date = localDate.format(ISO_DATE_FORMATTER);
        if (generalRuleService.isHoliday(date)) {
            return new ArrayList<>();
        }
        List<LocalDate> result = new ArrayList<>();
        result.add(localDate);
        LocalDate calculate = localDate;

        // 设置最大循环次数，防止出错，至今还没有15天的长假
        int maxIterations = 15;
        int iterations = 0;
        while (iterations < maxIterations) {
            LocalDate beforeDate = calculate.minusDays(1);
            String formattedBeforeDate = beforeDate.format(ISO_DATE_FORMATTER);
            if (generalRuleService.isHoliday(formattedBeforeDate)) {
                result.add(beforeDate);
                calculate = beforeDate;
            } else {
                break;
            }
            iterations++;
        }
        return result;
    }


}
