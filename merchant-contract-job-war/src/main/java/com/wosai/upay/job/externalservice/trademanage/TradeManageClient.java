package com.wosai.upay.job.externalservice.trademanage;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.trade.service.activity.response.QuotaApplyConditionQueryResponse;
import com.wosai.trade.service.request.PageInfo;
import com.wosai.trade.service.result.ListResult;
import com.wosai.upay.job.externalservice.trademanage.service.MyApplyActivityService;
import com.wosai.upay.job.externalservice.trademanage.service.MyQuotaApplyActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@Component
public class TradeManageClient {

    @Autowired
    private MyApplyActivityService myApplyActivityService;
    @Autowired
    private MyQuotaApplyActivityService myQuotaApplyActivityService;

    public Optional<ApplyConditionQueryResponse> getLatestApply(String merchantSn, int status) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", status,
                "page", 1,
                "page_size", 1,
                "order_by", Arrays.asList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<ApplyConditionQueryResponse> applyResponse = myApplyActivityService.conditionQuery(request);
        return WosaiCollectionUtils.isEmpty(applyResponse.getRecords()) ? Optional.empty() : Optional.of(applyResponse.getRecords().get(0));
    }

    public List<ApplyConditionQueryResponse> getEffectApplyByMerchantSn(String merchantSn) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", ActivityConstants.EFFECT,
                "page", 1,
                "page_size", 100,
                "order_by", Collections.singletonList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<ApplyConditionQueryResponse> applyResponse = myApplyActivityService.conditionQuery(request);
        return applyResponse.getRecords();
    }

    public List<QuotaApplyConditionQueryResponse> getEffectQuotaByMerchantSn(String merchantSn) {
        Map request = CollectionUtil.hashMap(
                "sn", merchantSn,
                "status", ActivityConstants.EFFECT,
                "page", 1,
                "page_size", 100,
                "order_by", Collections.singletonList(
                        CollectionUtil.hashMap(
                                "field", "create_at",
                                "order", PageInfo.OrderBy.OrderType.DESC.getValue()
                        )
                )
        );

        ListResult<QuotaApplyConditionQueryResponse> quotaApplyConditionQueryResponseListResult = myQuotaApplyActivityService.conditionQuery(request);
        return quotaApplyConditionQueryResponseListResult.getRecords();
    }


}
