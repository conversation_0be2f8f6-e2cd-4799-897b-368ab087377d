package com.wosai.upay.job.biz;

import com.google.common.collect.Lists;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.refactor.event.ContractTaskEvent;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: ContractTask业务处理
 * <AUTHOR>
 * @Date 2020/11/18 10:42
 */
@Component
@Slf4j
public class ContractTaskBiz {

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private SensorSendBiz sensorSendBiz;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    final List<Integer> insertStatus = Lists.newArrayList(null, TaskStatus.PENDING.getVal(), TaskStatus.FAIL.getVal());

    final List<Integer> updateStatus = Lists.newArrayList(TaskStatus.SUCCESS.getVal(), TaskStatus.FAIL.getVal());

    static final List<String> CONTRACT_TASK_TYPE_LIST = avro.shaded.com.google.common.collect.Lists.newArrayList(
            ProviderUtil.CONTRACT_TYPE_INSERT,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT
    );

    /**
     * 插入contract_task表并根据type和status判断是否发送消息到神策
     *
     * @param insertTask 要被插入的数据
     * @return ContractTask 插入成功返回带有Id的task
     * @Author: zhmh
     * @time: 11:08 2020/11/18
     */
    public Integer insert(ContractTask insertTask) {
        // CUA-10635 商户名称截取40个字符
        insertTask.setMerchant_name(Utils.substring(insertTask.getMerchant_name(), 40));
        log.info("ContractTaskBiz insert {}", insertTask);
        final int row = contractTaskMapper.insert(insertTask);
        //根据type和status异步发送神策消息
        if (Objects.equals(insertTask.getType(), ProviderUtil.CONTRACT_TYPE_ATTACH_UPLOAD) || !insertStatus.contains(insertTask.getStatus())) {
            return row;
        }
        CompletableFuture.runAsync(() -> sendContractTasksMessage(Lists.newArrayList(insertTask)));
        return row;
    }

    /**
     * 插入contract_task表并根据type和status判断是否发送消息到神策
     *
     * @param updateTask 要被跟新的任务
     * @return ContractTask 要被跟新的任务
     * @Author: zhmh
     * @Description:
     * @time: 11:08 2020/11/18
     */
    public ContractTask update(ContractTask updateTask) {
        log.info("ContractTaskBiz update {}", updateTask);
        contractTaskMapper.updateByPrimaryKey(updateTask);
        //修改后的最新值
        final ContractTask newTask = contractTaskMapper.selectByPrimaryKey(updateTask.getId());
        //没有改变状态
        if (Objects.equals(updateTask.getType(), ProviderUtil.CONTRACT_TYPE_ATTACH_UPLOAD) || !updateStatus.contains(updateTask.getStatus())) {
            return newTask;
        }
        //根据type和status异步发送神策消息
        CompletableFuture.runAsync(() -> sendContractTasksMessage(Lists.newArrayList(newTask)));
        // CUA-10967 只有需要处理的任务才需要发送事件
        if (CONTRACT_TASK_TYPE_LIST.contains(newTask.getType())) {
            applicationEventPublisher.publishEvent(new ContractTaskEvent(newTask));
        }
        return newTask;
    }


    /**
     * 根据type和status判断是否发送消息到神策
     *
     * @param contractTasks 批量发送消息到神策
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 14:00 2020/11/9
     */
    public void sendContractTasksMessage(List<ContractTask> contractTasks) {
        if (CollectionUtils.isEmpty(contractTasks)) {
            return;
        }
        sensorSendBiz.sendContractTaskMessage2Sensor(contractTasks);
    }

    /**
     * 查看是否为新的contract task 格式
     *
     * @param task
     * @return true 是  false 不是
     */
    public Boolean checkNewVersionWechatAuthTask(ContractTask task) {
        String result = task.getResult();
        return Objects.nonNull(result) && result.contains("applySubmitInfo");
    }
}
