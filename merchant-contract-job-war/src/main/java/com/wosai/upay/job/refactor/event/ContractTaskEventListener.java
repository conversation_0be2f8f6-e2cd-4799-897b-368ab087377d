package com.wosai.upay.job.refactor.event;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.keepalive.KeepAliveConfigBiz;
import com.wosai.upay.job.biz.keepalive.KeepAliveTaskBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.RuleItem;
import com.wosai.upay.job.model.keepalive.KeepAliveConfigResult;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.monitor.MonitorObject;
import com.wosai.upay.job.refactor.model.bo.KeepAliveTaskResultBO;
import com.wosai.upay.job.refactor.model.enums.MerchantKeepAliveConfigStatusEnum;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 进件任务监听器：用于监听事件的处理结果并且进行打点记录
 * 优化后支持新增商户入网时自动创建保活任务
 */
@Slf4j
@Component
public class ContractTaskEventListener implements ApplicationListener<ContractTaskEvent> {

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private MonitorLog monitorLog;
    @Autowired
    private KeepAliveConfigBiz keepAliveConfigBiz;
    @Autowired
    private KeepAliveTaskBiz keepAliveTaskBiz;
    @Autowired
    private AcquirerChangeDao acquirerChangeDao;

    private static final List<String> CONTRACT_TASK_TYPE_LIST = Lists.newArrayList(
            ProviderUtil.CONTRACT_TYPE_INSERT,
            ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT
    );

    private final Map<String, TaskWarn> TASK_WARN_MAP = CollectionUtil.hashMap(
            AcquirerTypeEnum.LKL_V3.getValue(), new LklTaskWarn(),
            AcquirerTypeEnum.HAI_KE.getValue(), new HaikeTaskWarn(),
            AcquirerTypeEnum.FU_YOU.getValue(), new FuyouTaskWarn(),
            AcquirerTypeEnum.GUOTONG.getValue(), new GuotongTaskWarn()
    );
    private static final Cache<String, String> CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Override
    public void onApplicationEvent(ContractTaskEvent event) {
        ContractTask contractTask = event.getContractTask();
        if (WosaiStringUtils.isEmpty(contractTask.getRule_group_id())) {
            log.warn("ContractTaskEventListener rule_group_id is empty {}", contractTask.getId());
            return;
        }
        if (!CONTRACT_TASK_TYPE_LIST.contains(contractTask.getType()) || !TaskStatus.isFinish(contractTask.getStatus())) {
            return;
        }
        String acquirer = getAcquirerByRuleGroupId(contractTask.getRule_group_id());
        if (WosaiStringUtils.isEmpty(acquirer)) {
            log.error("ContractTaskEventListener acquirer is empty {}", contractTask.getId());
            return;
        }
        TaskWarn taskWarn = TASK_WARN_MAP.get(acquirer);
        if (Objects.isNull(taskWarn)) {
            return;
        }

        // 异步并行处理两个独立的业务，确保互不影响
        handleTaskWarn(contractTask, taskWarn);
        handleKeepAliveTaskCreation(contractTask);
    }

    /**
     * 异步处理任务告警和监控
     */
    private void handleTaskWarn(ContractTask contractTask, TaskWarn taskWarn) {
        CompletableFuture.runAsync(() -> {
            try {
                if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
                    taskWarn.insertWarn(contractTask);
                }
                if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
                    taskWarn.updateBankAccountWarn(contractTask);
                }
            } catch (Exception e) {
                log.error("任务告警处理异常，任务ID: {}", contractTask.getId(), e);
            }
        });
    }

    /**
     * 异步处理保活任务创建
     * 仅在新增商户入网且任务成功时创建保活任务
     */
    private void handleKeepAliveTaskCreation(ContractTask contractTask) {
        CompletableFuture.runAsync(() -> {
            try {
                // 只有新增商户入网且任务成功时才创建保活任务
                if (!ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType()) ||
                        !TaskStatus.SUCCESS.getVal().equals(contractTask.getStatus())) {
                    return;
                }

                String merchantSn = contractTask.getMerchant_sn();
                KeepAliveConfigResult keepAliveConfigResult = keepAliveConfigBiz.queryKeepAliveConfig(merchantSn);
                if (Objects.nonNull(keepAliveConfigResult) &&
                        MerchantKeepAliveConfigStatusEnum.OPEN.getValue().equals(keepAliveConfigResult.getStatus())) {
                    // 这里新增商户入网的任务有可能是切换收单机构触发的，因此需要判断是否存在未完成的切换申请，如果有的话就不在这里触发重新创建保活任务
                    McAcquirerChange mcAcquirerChange = acquirerChangeDao.getLatestUnFinishedApply(merchantSn);
                    if (Objects.isNull(mcAcquirerChange)) {
                        keepAliveTaskBiz.createKeepAliveTasks(merchantSn,
                                KeepAliveTaskResultBO.createResultFromEvent("ContractTaskEventListener", "新增商户入网"));
                    }
                }
            } catch (Exception e) {
                log.error("保活任务创建异常，商户: {}", contractTask.getMerchant_sn(), e);
            }
        });
    }

    String getAcquirerByRuleGroupId(String ruleGroupId) {
        try {
            return CACHE.get(ruleGroupId, () -> doGetAcquirerByRuleGroupId(ruleGroupId));
        } catch (Exception e) {
            log.error("getAcquirerByRuleGroupId error {}", ruleGroupId, e);
            return null;
        }
    }

    private String doGetAcquirerByRuleGroupId(String ruleGroupId) {
        RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
        if (Objects.isNull(ruleGroup)) {
            return null;
        }
        String acquirer = ruleGroup.getAcquirer();
        if (WosaiStringUtils.isEmpty(acquirer) && !ruleGroup.getRules().isEmpty()) {
            for (RuleItem rule : ruleGroup.getRules()) {
                ContractRule contractRule = ruleContext.getContractRule(rule.getRule());
                if (Objects.nonNull(contractRule) && WosaiStringUtils.isNotEmpty(contractRule.getAcquirer())) {
                    acquirer = contractRule.getAcquirer();
                    break;
                }
            }
        }
        return acquirer;
    }

    abstract class TaskWarn {
        protected void warn(String name, ContractTask contractTask) {
            if (TaskStatus.SUCCESS.getVal().equals(contractTask.getStatus())) {
                MonitorObject monitorObject = new MonitorObject()
                        .setSn(contractTask.getMerchant_sn())
                        .setEvent(name)
                        .setStatus(200);
                monitorLog.recordObject(monitorObject);
            } else {
                String message = getFailMessage(contractTask.getResult());
                MonitorObject monitorObject = new MonitorObject()
                        .setSn(contractTask.getMerchant_sn())
                        .setEvent(name)
                        .setStatus(460)
                        .setMessage(message);
                monitorLog.recordObject(monitorObject);
                throw new ContractBizException(message);
            }
        }

        private String getFailMessage(String result) {
            try {
                Map resultMap = JSON.parseObject(result, Map.class);
                Object resultMessage = resultMap.get("result");
                if (Objects.nonNull(resultMessage) && resultMessage instanceof String && WosaiStringUtils.isNotEmpty(resultMessage.toString())) {
                    return resultMessage.toString();
                } else {
                    Object message = resultMap.get("message");
                    if (Objects.nonNull(message) && message instanceof String && WosaiStringUtils.isNotEmpty(message.toString())) {
                        return message.toString();
                    } else {
                        return "失败";
                    }
                }
            } catch (Exception e) {
                log.error("getFailMessage error {}", result, e);
                return "失败";
            }
        }

        abstract void insertWarn(ContractTask contractTask);

        abstract void updateBankAccountWarn(ContractTask contractTask);
    }

    class LklTaskWarn extends TaskWarn {
        @Override
        @Timed(value = "拉卡拉-新增商户入网")
        void insertWarn(ContractTask contractTask) {
            super.warn("拉卡拉-新增商户入网", contractTask);
        }

        @Override
        @Timed(value = "拉卡拉-结算账户变更")
        void updateBankAccountWarn(ContractTask contractTask) {
            super.warn("拉卡拉-结算账户变更", contractTask);
        }
    }

    class HaikeTaskWarn extends TaskWarn {
        @Override
        @Timed(value = "海科-新增商户入网")
        void insertWarn(ContractTask contractTask) {
            super.warn("海科-新增商户入网", contractTask);
        }

        @Override
        @Timed(value = "海科-结算账户变更")
        void updateBankAccountWarn(ContractTask contractTask) {
            super.warn("海科-结算账户变更", contractTask);
        }
    }

    class FuyouTaskWarn extends TaskWarn {
        @Override
        @Timed(value = "富友-新增商户入网")
        void insertWarn(ContractTask contractTask) {
            super.warn("富友-新增商户入网", contractTask);
        }

        @Override
        @Timed(value = "富友-结算账户变更")
        void updateBankAccountWarn(ContractTask contractTask) {
            super.warn("富友-结算账户变更", contractTask);
        }
    }

    class GuotongTaskWarn extends TaskWarn {
        @Override
        @Timed(value = "国通-新增商户入网")
        void insertWarn(ContractTask contractTask) {
            super.warn("国通-新增商户入网", contractTask);
        }

        @Override
        @Timed(value = "国通-结算账户变更")
        void updateBankAccountWarn(ContractTask contractTask) {
            super.warn("国通-结算账户变更", contractTask);
        }
    }
}
