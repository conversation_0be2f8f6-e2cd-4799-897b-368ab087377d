package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.job.refactor.mapper.MerchantKeepaliveConfigDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.KeepAliveOperateInfoBO;
import com.wosai.upay.job.refactor.model.entity.MerchantKeepaliveConfigDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 保活商户名单表数据库访问层 {@link MerchantKeepaliveConfigDO}
 * 对MerchantKeepaliveConfigDynamicMapper层做出简单封装 {@link MerchantKeepaliveConfigDynamicMapper}
 *
 * <AUTHOR>
@Repository
public class MerchantKeepaliveConfigDAO extends AbstractBaseDAO<MerchantKeepaliveConfigDO, MerchantKeepaliveConfigDynamicMapper> {

    public MerchantKeepaliveConfigDAO(SqlSessionFactory sqlSessionFactory, MerchantKeepaliveConfigDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号查询记录
     *
     * @param merchantSn 商户号
     * @return 保活商户配置记录
     */
    public MerchantKeepaliveConfigDO selectByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<MerchantKeepaliveConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantKeepaliveConfigDO::getMerchantSn, merchantSn);
        return entityMapper.selectOne(queryWrapper);
    }

    public void update(MerchantKeepaliveConfigDO merchantKeepaliveConfigDO) {
        int affectedRows = entityMapper.updateById(merchantKeepaliveConfigDO);
        if (affectedRows < 1) {
            throw new ContractBizException("更新保活配置失败，请重试");
        }
    }

    /**
     * 启用保活配置（充血模型方法）
     *
     * @param merchantSn   商户号
     * @param logParamsDto 操作信息
     */
    public void enableKeepAliveConfig(String merchantSn, LogParamsDto logParamsDto) {
        MerchantKeepaliveConfigDO config = selectByMerchantSn(merchantSn);
        KeepAliveOperateInfoBO operateInfoBO = buildOperateInfoBO(logParamsDto);
        if (config != null) {
            // 使用充血模型的业务方法
            config.enableKeepAlive(operateInfoBO);
            update(config);
        } else {
            // 创建新配置
            MerchantKeepaliveConfigDO newConfig = MerchantKeepaliveConfigDO.createNewConfig(merchantSn, operateInfoBO);
            save(newConfig);
        }
    }

    /**
     * 禁用保活配置（充血模型方法）
     *
     * @param merchantSn   商户号
     * @param logParamsDto 操作日志参数
     */
    public void disableKeepAliveConfig(String merchantSn, LogParamsDto logParamsDto) {
        MerchantKeepaliveConfigDO config = selectByMerchantSn(merchantSn);

        if (config == null) {
            throw new ContractBizException("商户保活配置不存在");
        }

        // 使用充血模型的业务方法
        config.disableKeepAlive(buildOperateInfoBO(logParamsDto));
        update(config);
    }

    private KeepAliveOperateInfoBO buildOperateInfoBO(LogParamsDto logParamsDto) {
        return new KeepAliveOperateInfoBO()
                .setUserId(logParamsDto.getUserId())
                .setUserName(logParamsDto.getUserName())
                .setPlatform(logParamsDto.getLogPlatformEnum().getCode())
                .setRemark(logParamsDto.getRemark());
    }

    /**
     * 保存保活配置
     *
     * @param config 保活配置对象
     */
    public void save(MerchantKeepaliveConfigDO config) {
        // 插入操作
        Integer affectedRows = insertOne(config);
        if (affectedRows < 1) {
            throw new ContractBizException("更新保活配置失败，请重试");
        }
    }
}