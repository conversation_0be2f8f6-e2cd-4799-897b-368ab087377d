package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 商户报备获取的银行交易参数存储表(new)表数据库访问层 {@link MerchantProviderParamsDO}
 * 对MerchantProviderParamsMapper层做出简单封装 {@link MerchantProviderParamsDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class MerchantProviderParamsDAO extends AbstractBaseDAO<MerchantProviderParamsDO, MerchantProviderParamsDynamicMapper> {

    public MerchantProviderParamsDAO(SqlSessionFactory sqlSessionFactory, MerchantProviderParamsDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsById(String id) {
        return Optional.ofNullable(entityMapper.selectById(id));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByPayMerchantId(String payMerchantId) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getPayMerchantId, payMerchantId);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getDeleted, 0);
        return selectOne(lambdaQueryWrapper);
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByMerchantSnAndPayMerchantId(String merchantSn, String payMerchantId) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getMerchantSn, merchantSn);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getPayMerchantId, payMerchantId);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getDeleted, 0);
        return selectOne(lambdaQueryWrapper);
    }

    public Map<String, MerchantProviderParamsDO> getMerchantProviderParamsByPayMerchantIdList(List<String> payMerchantIdList) {
        if (WosaiCollectionUtils.isEmpty(payMerchantIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(MerchantProviderParamsDO::getPayMerchantId, payMerchantIdList);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getDeleted, 0);
        List<MerchantProviderParamsDO> merchantProviderParams = entityMapper.selectList(lambdaQueryWrapper);
        return merchantProviderParams.stream().collect(Collectors.toMap(MerchantProviderParamsDO::getPayMerchantId, v -> v));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByProviderMerchantIdAndPayway(String providerMerchantId, Integer payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getProviderMerchantId, providerMerchantId);
        lambdaQueryWrapper.eq(MerchantProviderParamsDO::getPayway, payway);
        return selectOne(lambdaQueryWrapper);
    }


    /**
     * 根据商户号,报备渠道,支付源获取交易参数
     *
     * @param merchantSn 商户号
     * @param channel    报备渠道
     * @param payWay     支付源
     * @return 交易参数
     */
    public Optional<MerchantProviderParamsDO> getMerChantProviderParams(String merchantSn, String channel, Integer payWay) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getChannelNo, channel)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 保存交易参数
     *
     * @param merchantProviderParamsDO 交易参数
     * @return effect rows
     */
    public int saveMerchantParameters(MerchantProviderParamsDO merchantProviderParamsDO) {
        if (Objects.isNull(merchantProviderParamsDO)
                || StringUtils.isBlank(merchantProviderParamsDO.getId())) {
            return 0;
        }
        return entityMapper.insert(merchantProviderParamsDO);
    }

    /**
     * 根据商户号,规则组id,报备规则,银行通道渠道号获取交易参数
     *
     * @param merchantSn   商户号
     * @param ruleGroupId  规则组id
     * @param contractRule 报备规则
     * @param channelNo    银行通道渠道号
     * @return 交易参数
     */
    public Optional<MerchantProviderParamsDO> getMerChantProviderParams(String merchantSn, String ruleGroupId, String contractRule, String channelNo) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getRuleGroupId, ruleGroupId)
                .eq(MerchantProviderParamsDO::getContractRule, contractRule)
                .eq(MerchantProviderParamsDO::getChannelNo, channelNo)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getMerchantProviderParamsByProviderAndPayway(String merchantSn, Integer provider, Integer payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payway)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getInUseProviderParams(String merchantSn, int payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, payway)
                .ne(MerchantProviderParamsDO::getProvider, payway)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getStatus, 1)
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getOnlineMerchantProviderParams(String merchantSn, String contractRule, Integer payway) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getContractRule, contractRule)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            lambdaQueryWrapper.eq(MerchantProviderParamsDO::getWxUseType, WxUseType.ONLINE_PAYMENT.getCode());
        } else {
            lambdaQueryWrapper.in(MerchantProviderParamsDO::getWxUseType, Arrays.asList(WxUseType.ONLINE_PAYMENT.getCode(), WxUseType.SCENARIO_PAYMENT.getCode()));
        }
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    public Optional<MerchantProviderParamsDO> getWeixinOnlineMerchantProviderParamsByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, PaywayEnum.WEIXIN.getValue())
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getWxUseType, WxUseType.ONLINE_PAYMENT.getCode())
                .orderByDesc(MerchantProviderParamsDO::getCtime)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 根据主键更新,注意:null值不参与更新
     *
     * @param merchantProviderParamsDO 交易参数对象
     */
    public void updateByPrimaryKey(MerchantProviderParamsDO merchantProviderParamsDO) {
        if (Objects.isNull(merchantProviderParamsDO) || Objects.isNull(merchantProviderParamsDO.getId())) {
            return;
        }
        entityMapper.updateById(merchantProviderParamsDO);
    }

    /**
     * 根据商户号和配置状态,获取商户交易配置列表
     *
     * @param merchantSn 商户号
     * @param status     配置状态
     * @return 商户交易配置列表
     */
    public List<MerchantProviderParamsDO> listByMerchantSnAndStatus(String merchantSn, Integer status) {
        if (StringUtils.isBlank(merchantSn) || Objects.isNull(status)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getStatus, status)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号,provider,payWay获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param provider   渠道
     * @param payWay     支付源
     * @return 商户交易配置
     */
    public Optional<MerchantProviderParamsDO> getBySnAndProviderAndPayWay(String merchantSn, Integer provider, Integer payWay) {
        if (StringUtils.isBlank(merchantSn) || Objects.isNull(provider)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                // 优先取已使用的配置
                .orderByDesc(MerchantProviderParamsDO::getStatus)
                .last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(lambdaQueryWrapper));
    }

    /**
     * 根据商户号和支付源获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWay     支付源
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSnAndPayWay(String merchantSn, Integer payWay) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据渠道商户号获取交易配置
     *
     * @param providerMerchantId 渠道商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByProviderMerchantId(String providerMerchantId) {
        if (StringUtils.isBlank(providerMerchantId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getProviderMerchantId, providerMerchantId)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSnWithoutDeletedStatus(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号获取已删除的商户交易配置
     *
     * @param merchantSn 商户号
     * @return 商户交易配置
     */
    public List<MerchantProviderParamsDO> listDeletedParamsByMerchantSn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据id列表批量逻辑删除
     *
     * @param ids id列表
     */
    public void logicDeleteByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaUpdateWrapper<MerchantProviderParamsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(MerchantProviderParamsDO::getId, ids)
                .set(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue())
                .set(MerchantProviderParamsDO::getMtime, System.currentTimeMillis());
        entityMapper.update(null, updateWrapper);
    }

    public List<MerchantProviderParamsDO> listByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(MerchantProviderParamsDO::getId, ids)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号和provider逻辑删除
     *
     * @param merchantSn 商户号
     * @param providers  provider列表
     */
    public void logicDeleteBySnAndProviders(String merchantSn, List<String> providers) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(providers)) {
            return;
        }
        LambdaUpdateWrapper<MerchantProviderParamsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getProvider, providers)
                .set(MerchantProviderParamsDO::getMtime, System.currentTimeMillis())
                .set(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        entityMapper.update(null, updateWrapper);
    }

    /**
     * 根据商户号和provider逻辑删除
     *
     * @param payMchId 子商户号
     */
    public int logicDeleteByPayMchId(String payMchId) {
        if (StringUtils.isBlank(payMchId)) {
            return 0;
        }
        LambdaUpdateWrapper<MerchantProviderParamsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MerchantProviderParamsDO::getPayMerchantId, payMchId)
                .set(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        return entityMapper.update(null, updateWrapper);
    }

    public List<MerchantProviderParamsDO> getByMerchantSnAndProviderAndPayWay(String merchantSn, Integer provider, Integer payWay) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取已删除的商户参数
     *
     * @param merchantSn 商户号
     * @return 已删除的商户参数
     */
    public List<MerchantProviderParamsDO> listDeletedParamsBySn(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = buildLambdaQueryWrapper()
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }


    public Optional<MerchantProviderParamsDO> getDeletedSpecifiedMchId(String payMchId) {
        if (StringUtils.isBlank(payMchId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getPayMerchantId, payMchId)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        return selectOne(lambdaQueryWrapper);
    }

    public Optional<MerchantProviderParamsDO> getSpecifiedMchId(String payMchId) {
        if (StringUtils.isBlank(payMchId)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getPayMerchantId, payMchId)
                .orderByDesc(MerchantProviderParamsDO::getCtime);
        return selectOne(lambdaQueryWrapper);
    }

    /**
     * 删除富友子商号除了指定的
     *
     * @param merchantSn
     * @param payMchId
     */
    public void deleteFuYouWechatMchIdExceptSpecifiedOne(String merchantSn, String payMchId) {
        LambdaUpdateWrapper<MerchantProviderParamsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, ProviderEnum.PROVIDER_FUYOU.getValue())
                .eq(MerchantProviderParamsDO::getPayway, PaywayEnum.WEIXIN.getValue())
                .ne(MerchantProviderParamsDO::getPayMerchantId, payMchId)
                .set(MerchantProviderParamsDO::getDeleted, Deleted.DELETED.getValue());
        entityMapper.update(null, updateWrapper);
    }

    public void deleteParamsByProvider(String merchantSn, int provider) {
        LambdaUpdateWrapper<MerchantProviderParamsDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getProvider, provider);
        entityMapper.delete(wrapper);
    }


    /**
     * 根据商户号,payWay集合获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWayList 支付源集合
     * @return 商户交易配置
     */
    public  List<MerchantProviderParamsDO> getUsingBySnAndPayWayList(String merchantSn, List<Integer> payWayList) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getPayway, payWayList)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getStatus, UseStatusEnum.IN_USE.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商户号,payWay集合获取交易从哪火速
     *
     * @param merchantSn 商户号
     * @param payWay 支付源
     * @return 商户交易配置
     */
    public  List<MerchantProviderParamsDO> getParamsBySnAndPayWay(String merchantSn, Integer payWay) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .eq(MerchantProviderParamsDO::getPayway, payWay)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }



    /**
     * 根据商户号,provider,payWay集合获取商户交易配置
     *
     * @param merchantSn 商户号
     * @param payWayList 支付源集合
     * @param provider 通道
     * @return 商户交易配置
     */
    public  List<MerchantProviderParamsDO> getParamsBySnAndPayWayListAndProvider(String merchantSn, List<Integer> payWayList,Integer provider) {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getPayway, payWayList)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .eq(MerchantProviderParamsDO::getProvider, provider);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取保活参数的优化方法
     * 根据商户号、支付方式列表和 Provider 列表获取交易参数，并按创建时间倒序排序
     *
     * @param merchantSn 商户号
     * @param payWayList 支付源集合
     * @param providerList 通道集合
     * @return 按创建时间倒序排序的商户交易配置
     */
    public List<MerchantProviderParamsDO> getKeepAliveParamsSorted(String merchantSn, List<Integer> payWayList, List<Integer> providerList) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(payWayList) || CollectionUtils.isEmpty(providerList)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(MerchantProviderParamsDO::getMerchantSn, merchantSn)
                .in(MerchantProviderParamsDO::getPayway, payWayList)
                .in(MerchantProviderParamsDO::getProvider, providerList)
                .eq(MerchantProviderParamsDO::getDisableStatus, DisableStatusEnum.ACTIVATE.getValue())
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .orderByDesc(MerchantProviderParamsDO::getCtime);

        return entityMapper.selectList(lambdaQueryWrapper);
    }

}
