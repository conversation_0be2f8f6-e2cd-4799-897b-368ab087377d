package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.wosai.upay.job.refactor.model.bo.KeepAliveOperateInfoBO;
import com.wosai.upay.job.refactor.model.enums.MerchantKeepAliveConfigStatusEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 保活商户名单表实体对象
 *
 * <AUTHOR>
@TableName("merchant_keepalive_config")
@Data
@Slf4j
public class MerchantKeepaliveConfigDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 状态 0关闭  1开启
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 操作信息
     */
    @TableField(value = "operate_info")
    private String operateInfo;

    /**
     * 扩展信息
     */
    @TableField(value = "extra")
    private String extra;

    /**
     * 版本号
     */
    @Version
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标识 0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private LocalDateTime mtime;

    // ==================== 充血模型：业务方法 ====================

    /**
     * 启用保活配置
     *
     * @param keepAliveOperateInfoBO 操作日志参数
     */
    public void enableKeepAlive(KeepAliveOperateInfoBO keepAliveOperateInfoBO) {
        if (this.status != null && this.status.equals(MerchantKeepAliveConfigStatusEnum.OPEN.getValue())) {
            throw new ContractBizException("商户已经加入保活计划");
        }

        this.status = MerchantKeepAliveConfigStatusEnum.OPEN.getValue();
        this.operateInfo = JSON.toJSONString(keepAliveOperateInfoBO);
        touchMtime();
    }

    /**
     * 禁用保活配置
     *
     * @param keepAliveOperateInfoBO 操作日志参数
     */
    public void disableKeepAlive(KeepAliveOperateInfoBO keepAliveOperateInfoBO) {
        if (this.status == null || this.status.equals(MerchantKeepAliveConfigStatusEnum.CLOSE.getValue())) {
            throw new ContractBizException("商户已经退出保活计划");
        }

        this.status = MerchantKeepAliveConfigStatusEnum.CLOSE.getValue();
        this.operateInfo = JSON.toJSONString(keepAliveOperateInfoBO);
        touchMtime();
    }

    /**
     * 创建新的保活配置
     *
     * @param merchantSn 商户号
     * @param keepAliveOperateInfoBO 操作日志
     * @return 新的保活配置实例
     */
    public static MerchantKeepaliveConfigDO createNewConfig(String merchantSn, KeepAliveOperateInfoBO keepAliveOperateInfoBO) {
        MerchantKeepaliveConfigDO config = new MerchantKeepaliveConfigDO();
        config.merchantSn = merchantSn;
        config.status = MerchantKeepAliveConfigStatusEnum.OPEN.getValue();
        config.operateInfo = JSON.toJSONString(keepAliveOperateInfoBO);
        config.ctime = LocalDateTime.now();
        return config;
    }

    private void touchMtime() {
        this.mtime = LocalDateTime.now();
    }

    /**
     * 获取操作信息对象
     *
     * @return 操作信息对象
     */
    public KeepAliveOperateInfoBO getOperateInfoBO() {
        if (this.operateInfo == null) {
            return null;
        }
        return JSON.parseObject(this.operateInfo, KeepAliveOperateInfoBO.class);
    }
}