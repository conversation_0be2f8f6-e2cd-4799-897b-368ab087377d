package com.wosai.upay.job.service;
import java.sql.Timestamp;
import java.util.Date;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.FeeEffectiveTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.AcquirerTradeTypeEnum;
import com.shouqianba.cua.enums.core.ClearTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.CuaSupportService;
import com.wosai.trade.service.SceneConfigService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.BlackListBiz;
import com.wosai.upay.job.biz.ChangeToLklBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.acquirer.*;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.model.dto.RuleGroupDto;
import com.wosai.upay.job.model.dto.request.BankAutoChangeToThirdPartyReqDTO;
import com.wosai.upay.job.model.dto.request.ChangeAcquirerReqDTO;
import com.wosai.upay.job.model.dto.request.MerchantFeatureReqDTO;
import com.wosai.upay.job.model.dto.response.AcquirerChangeTaskRspDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.model.enums.NetInSceneEnum;
import com.wosai.upay.job.refactor.model.enums.PersonalCertificateTypeEnum;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.scene.service.activity.response.SceneConfigRecord;
import com.wosai.upay.wallet.service.WalletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_FAIL;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_SUCCESS;

/**
 * <AUTHOR>
 * @date 2019-09-15
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class AcquirerServiceImpl implements AcquirerService {

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    BatchService batchService;

    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private ChangeToLklBiz changeToLklBiz;

    @Autowired
    private WalletService walletServcie;

    @Autowired
    private CuaSupportService cuaSupportService;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    @Lazy
    private ContractEventService contractEventService;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private BlackListBiz blacklistBiz;

    @Resource
    private SubBizParamsDAO subBizParamsDAO;

    @Resource
    private SceneConfigService sceneConfigService;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;

    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private AcquirerChangeDao acquirerChangeDao;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private ContractManagerService contractManagerService;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Autowired
    private McRuleGroupDAO mcRuleGroupDAO;





    @Override
    public String getMerchantAcquirer(String merchantSn) {
        return ProviderUtil.externalAcquirer(acquirerBiz.getMerchantAcquirer(merchantSn));
    }

    @Override
    public String getMerchantAcquirerName(String merchantSn) {
        String acquirer = getMerchantAcquirer(merchantSn);
        return mcAcquirerDAO.getByAcquirer(acquirer).getName();
    }

    @Override
    public ClearParam getClearParam(String merchantSn) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        return getClearParamByAcquirer(merchantSn, acquirer);
    }

    @Override
    public ClearParam getClearParamByAcquirer(String merchantSn, String acquirer) {
        String acquirerMchId = null;
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO)) {
            throw new CommonPubBizException("收单机构不存在");
        }
        MerchantProviderParams params = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()));
        if (Objects.nonNull(params)) {
            acquirerMchId = params.getProvider_merchant_id();
        } else {
            acquirerMchId = acquirerBiz.getAcquirerMchIdFromMerchantConfig(merchantSn, acquirer);
        }
        if (WosaiStringUtils.isEmpty(acquirerMchId)) {
            throw new CommonPubBizException("商户还未报备");
        }

        return new ClearParam()
                .setAcquirer(ProviderUtil.externalAcquirer(acquirer))
                .setAcquirer_mch_id(acquirerMchId);
    }

    @Override
    public String getDirectPayMerchantId(String merchantSn, Integer payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(payway)
                .andPaywayEqualTo(payway);

        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            MerchantProviderParamsDto dto = params.get(0).toProviderParamsModule();
            if (WosaiStringUtils.isNotEmpty(dto.getPay_merchant_id())) {
                return dto.getPay_merchant_id();
            } else {
                Map<String, Object> extra = dto.getExtra();
                for (Map.Entry<String, Object> directParams : extra.entrySet()) {
                    if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
                        return BeanUtil.getPropString(directParams.getValue(), CommonModel.WEIXIN_SUB_MCH_ID);
                    } else {
                        return BeanUtil.getPropString(directParams.getValue(), "mch_id");
                    }
                }

            }
        }
        return null;
    }

    @Override
    public boolean applyChangeAcquirer(String merchantSn, String acquirer) {
        return applyChangeAcquirer(merchantSn, acquirer, Boolean.FALSE);
    }

    @Override
    public boolean applyChangeAcquirer(String merchantSn, String acquirer, Boolean immediately) {
        //默认使用移动支付业务tradeAppId
        final String payTradeAppId = subBizParamsBiz.getPayTradeAppId();
        return applyChangeAcquirer(merchantSn, acquirer, immediately, payTradeAppId);
    }

    /**
     * 是否存在未完成的切换收单机构申请
     *
     * @param merchantSn 商户号
     * @return 是否存在 true-存在
     */
    @Override
    public boolean existedChangeAcquirerNotFinish(String merchantSn) {
        return !Objects.isNull(acquirerChangeDao.getLatestUnFinishedApply(merchantSn));
    }

    @Override
    public McAcquirerChangeDTO queryLatestNotFinishChangeAcquirer(String merchantSn) {
        McAcquirerChange mcAcquirerChange = acquirerChangeDao.getLatestUnFinishedApply(merchantSn);
        if (Objects.isNull(mcAcquirerChange)) {
            return null;
        }
        return new McAcquirerChangeDTO()
                .setId(mcAcquirerChange.getId())
                .setApply_id(mcAcquirerChange.getApply_id())
                .setMerchant_sn(mcAcquirerChange.getMerchant_sn())
                .setMerchant_id(mcAcquirerChange.getMerchant_id())
                .setSource_acquirer(mcAcquirerChange.getSource_acquirer())
                .setTarget_acquirer(mcAcquirerChange.getTarget_acquirer())
                .setStatus(mcAcquirerChange.getStatus())
                .setMemo(mcAcquirerChange.getMemo())
                .setCreate_at(mcAcquirerChange.getCreate_at())
                .setUpdate_at(mcAcquirerChange.getUpdate_at())
                .setImmediately(mcAcquirerChange.getImmediately())
                .setProcess(mcAcquirerChange.getProcess())
                .setExtra(mcAcquirerChange.getExtra());
    }

    @Override
    public McAcquirerChangeDTO queryMcAcquirerChangeApplyByApplyId(String applyId) {
        McAcquirerChange mcAcquirerChange = acquirerChangeDao.getAcquirerChangeByApplyId(applyId);
        if (Objects.isNull(mcAcquirerChange)) {
            return null;
        }
        return new McAcquirerChangeDTO()
                .setId(mcAcquirerChange.getId())
                .setApply_id(mcAcquirerChange.getApply_id())
                .setMerchant_sn(mcAcquirerChange.getMerchant_sn())
                .setMerchant_id(mcAcquirerChange.getMerchant_id())
                .setSource_acquirer(mcAcquirerChange.getSource_acquirer())
                .setTarget_acquirer(mcAcquirerChange.getTarget_acquirer())
                .setStatus(mcAcquirerChange.getStatus())
                .setMemo(mcAcquirerChange.getMemo())
                .setCreate_at(mcAcquirerChange.getCreate_at())
                .setUpdate_at(mcAcquirerChange.getUpdate_at())
                .setImmediately(mcAcquirerChange.getImmediately())
                .setProcess(mcAcquirerChange.getProcess())
                .setExtra(mcAcquirerChange.getExtra());
    }

    @Override
    public boolean applyChangeAcquirer(String merchantSn, String acquirer, Boolean immediately, String tradeAppId) {
        if (handleUnContractMerchant(merchantSn, acquirer)) {
            return true;
        }

        if (Objects.isNull(immediately)) {
            immediately = Boolean.FALSE;
        }

        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(acquirer);
        if (changeBiz == null) {
            throw new CommonInvalidParameterException("不支持收单机构 " + acquirer);
        }
        String sourceAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        if (ThreadLocalUtil.isAcquirerChangeSkipSyncBankAccount()) {
            Map<String, Object> extraMap = Maps.newHashMap();
            extraMap.put(AcquirerChangeSaveDTO.SKIP_SYNC_BANK_ACCOUNT_KEY, true);
            dto.setExtra(JSON.toJSONString(extraMap));
        }
        dto.setMerchantSn(merchantSn);
        dto.setSourceAcquirer(sourceAcquirer);
        dto.setTargetAcquirer(acquirer);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        return changeBiz.applyChangeAcquirer(dto);
    }


    @Override
    public boolean applyChangeAcquirer(ChangeAcquirerRequest request) {
        final String merchantSn = request.getMerchantSn();
        final String acquirer = request.getAcquirer();
        if (handleUnContractMerchant(merchantSn, acquirer)) {
            return true;
        }
        Boolean immediately = request.getImmediately();
        if (Objects.isNull(immediately)) {
            immediately = Boolean.FALSE;
        }

        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(acquirer);
        if (changeBiz == null) {
            throw new CommonInvalidParameterException("不支持收单机构 " + acquirer);
        }
        String sourceAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setSourceAcquirer(sourceAcquirer);
        dto.setTargetAcquirer(acquirer);
        dto.setImmediately(immediately);
        dto.setTradeAppId(request.getTradeAppId());
        dto.setForceChange(Boolean.FALSE);
        dto.setCancelable(request.getCancellable());
        return changeBiz.applyChangeAcquirer(dto);
    }


    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    /**
     * 根据id查询切换收单机构任务
     *
     * @param taskId 任务id
     * @return 切换收单机构任务
     */
    @Override
    public AcquirerChangeTaskRspDTO getAcquirerChangeTaskById(Integer taskId) {
        Optional<McAcquirerChangeDO> changeOpt = mcAcquirerChangeDAO.getByPrimaryKey(taskId);
        if (!changeOpt.isPresent()) {
            return null;
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeOpt.get();
        return BeanCopyUtils.copyProperties(mcAcquirerChangeDO, AcquirerChangeTaskRspDTO.class);
    }

    /**
     * 提交切换收单机构任务
     *
     * @param changeAcquirerReqDTO   切收单机构请求参数
     * @param logParamsDto           日志参数
     * @return 任务id
     */
    @Override
    public Integer submitChangeAcquirerTask(ChangeAcquirerReqDTO changeAcquirerReqDTO,
                                            LogParamsDto logParamsDto) {
        populateChangeReqDefaultField(changeAcquirerReqDTO);
        String targetAcquirer = changeAcquirerReqDTO.getTargetAcquirerTypeEnum().getValue();
        AbstractAcquirerChangeBiz changeBiz = getChangeBiz(targetAcquirer);
        return changeBiz.applyChangeAcquirerAndReturnTaskId(changeAcquirerReqDTO, logParamsDto);
    }

    private void populateChangeReqDefaultField(ChangeAcquirerReqDTO changeAcquirerReqDTO) {
        if (StringUtils.isBlank(changeAcquirerReqDTO.getTradeAppId())) {
            changeAcquirerReqDTO.setTradeAppId(subBizParamsBiz.getPayTradeAppId());
        }
        if (Objects.isNull(changeAcquirerReqDTO.getImmediately())) {
            changeAcquirerReqDTO.setImmediately(false);
        }
        if (Objects.isNull(changeAcquirerReqDTO.getForceChange())) {
            changeAcquirerReqDTO.setForceChange(false);
        }
    }

    /**
     * 此场景是收单机构都入失败，触发重新入网。其实不属于切收单机构的逻辑，只是在这里触发。
     *
     * @param merchantSn 商户号
     * @param acquirer   目标收单机构
     * @return true 重新走进件流程  false 进件成功走切换流程
     */
    private boolean handleUnContractMerchant(String merchantSn, String acquirer) {
        // https://jira.wosai-inc.com/browse/CUA-6590
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            throw new CommonPubBizException("间连扫码未开通，不支持切换收单机构");
        }

        if (contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS && contractStatus.getStatus() != ContractStatus.STATUS_BIZ_FAIL) {
            throw new CommonPubBizException("间连扫码开通中，不支持切换收单机构");
        }

        if (contractStatus.getStatus() == ContractStatus.STATUS_BIZ_FAIL) {
            List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
            if (!indirectAcquirerList.contains(acquirer)) {
                throw new CommonPubBizException("间连扫码未开通成功，不允许切至银行通道");
            }

            ContractGroupRuleVerifyResultBO verifyResultBO = mcRulesDecisionService.checkMerchantEligibilityToAcquirer(merchantSn, acquirer, NetInSceneEnum.CHANNEL_SWITCHING);
            if (!verifyResultBO.isCheckPass()) {
                throw new CommonPubBizException("商户不满足指定通道准入要求：" + verifyResultBO.getMessage());
            }

            blacklistBiz.checkBlackList(merchantSn);
            contractStatusMapper.deleteBySn(merchantSn);
            contractEventService.saveContractEvent(merchantSn, composeAcquirerBiz.getAcquirerDefaultRuleGroup(acquirer), "CRM");
            return true;
        }
        return false;
    }

    private AbstractAcquirerChangeBiz getChangeBiz(String acquirer) {
        try {
            return applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        } catch (Exception e) {
            // 找不到对应的处理类，尝试返回一个 commonBank 的类
            return applicationContext.getBean("commonBank-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        }
    }

    @Override
    public CheckChangeAcquirerResp checkChangeAcquirer(String merchantSn, String acquirer) {
        //默认使用移动支付业务tradeAppId
        try {
            final String payTradeAppId = subBizParamsBiz.getPayTradeAppId();
            return checkChangeAcquirer(merchantSn, acquirer, payTradeAppId);
        } catch (Exception exception) {
            return CheckChangeAcquirerResp.fail(exception.getMessage());
        }
    }

    @Override
    public CheckChangeAcquirerResp checkChangeAcquirer(String merchantSn, String acquirer, String tradeAppId) {
        try {
            AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean(acquirer + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
            if (changeBiz == null) {
                throw new CommonInvalidParameterException("不支持收单机构 " + acquirer);
            }
            String sourceAcquirer = getMerchantAcquirer(merchantSn);

            Map merchant = merchantService.getMerchantBySn(merchantSn);
            if (WosaiMapUtils.isEmpty(merchant)) {
                throw new CommonPubBizException(String.format("商户 %s 不存在", merchantSn));
            }
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            changeBiz.preCheck(merchantSn, merchantId, sourceAcquirer, acquirer, tradeAppId, false);
            return CheckChangeAcquirerResp.pass();
        } catch (Exception e) {
            return CheckChangeAcquirerResp.fail(e.getMessage());
        }
    }

    @Override
    public SyncMchStatusResp syncMchStatusToTargetAcquirer(String merchantSn, int status, String acquirer) {
        return acquirerBiz.syncMchStatusToAcquirer(merchantSn, status, acquirer);
    }


    @Override
    public SyncMchStatusResp syncMchStatusToAcquirer(String merchantSn, int status) {
        return acquirerBiz.syncMchStatusToAcquirer(merchantSn, status);
    }

    @Override
    public SyncMchStatusResp syncMchAndSubMchIdStatus(String merchantSn, int status, String acquirer) {
        return acquirerBiz.syncMchAndSubMchIdStatus(merchantSn, status, acquirer);
    }


    @Override
    public CallBackBean applyChangeAcquirerByApprove(ChangeAcquirerApproveDTO dto) {
        final CallBackBean callBackBean = dto.getCallBackBean();
        String message = "已发起切换收单机构,请耐心等待";
        Integer resultType = AUDIT_EXECUTE_SUCCESS;
        try {
            batchService.changAcquire(dto);
        } catch (Exception exception) {
            log.error("通过审批切换收单机构异常:{}", exception);
            message = ExceptionUtil.getThrowableMsg(exception);
            resultType = AUDIT_EXECUTE_FAIL;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;
    }

    @Override
    public void updateMerchantAcquirer(String merchantSn, String acquirer) {
        acquirerBiz.updateMerchantAcquirer(merchantSn, acquirer);
    }

    @Override
    public void changeToLklCheck(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, "id");
        changeToLklBiz.check(merchantId);
    }

    @Override
    public void changeToLkl(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, "id");
        changeToLklBiz.check(merchantId);
        // 检查是否有余额
        long balance = walletServcie.getBalance(merchantId);
        applyChangeAcquirer(merchantSn, AcquirerTypeEnum.LKL_V3.getValue(), balance <= 0);
    }

    @Override
    public void forceChangeToLkl(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, "id");
        cuaSupportService.cancelAllActivitiesByMerchant(null, merchantSn, "脚本", "通联迁移拉卡拉");
        // 检查是否有余额
        long balance = walletServcie.getBalance(merchantId);
        boolean immediately = balance <= 0;
        AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean("lklV3-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
        String sourceAcquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setSourceAcquirer(sourceAcquirer);
        dto.setTargetAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        dto.setImmediately(immediately);
        dto.setTradeAppId(subBizParamsBiz.getPayTradeAppId());
        dto.setForceChange(Boolean.TRUE);
        changeBiz.applyChangeAcquirer(dto);
    }

    @Override
    public List<Map> getFeeRateByAcquirer(String merchantSn, String acquirer) {
        IAcquirerBiz acquirerBiz = composeAcquirerBiz.getAcquirerBiz(acquirer);
        return acquirerBiz.queryFeeRate(merchantSn);
    }

    /**
     * 银行自动切换到三方收单机构
     *
     * @param reqDTO 请求参数
     * @return 是否成功 true-成功
     */
    @Override
    public boolean bankAutoChangeToThirdParty(BankAutoChangeToThirdPartyReqDTO reqDTO) {
        boolean existedProcessingTask = interScheduleTaskService.isExistedProcessingTask(reqDTO.getMerchantSn(), InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY);
        if (existedProcessingTask) {
            log.warn("商户已存在正在进行的自动切回三方收单机构的任务,商户号: {}", reqDTO.getMerchantSn());
            return false;
        }
        Set<String> traAppIds = listMerchantTraAppIds(reqDTO.getMerchantSn(), reqDTO.getProvider());
        if (CollectionUtils.isEmpty(traAppIds)) {
            log.warn("商户业务方id为空,商户号: {}", reqDTO.getMerchantSn());
            return false;
        }
        reqDTO.setTraAppIds(traAppIds);
        SceneConfigRecord configBySceneErrorCode = sceneConfigService.getConfigBySceneErrorCode(reqDTO.getErrorCode());
        if (Objects.nonNull(configBySceneErrorCode)) {
            reqDTO.setErrorCodeMeaning(configBySceneErrorCode.getToBTipsZh());
        }
        InternalScheduleMainTaskDO mainTaskDO = new InternalScheduleMainTaskDO();
        mainTaskDO.setMerchantSn(reqDTO.getMerchantSn());
        mainTaskDO.setType(InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY.getValue());
        mainTaskDO.setAffectStatusSubTaskNum(1);
        mainTaskDO.setContext(JSON.toJSONString(reqDTO));
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setMerchantSn(reqDTO.getMerchantSn());
        subTaskDO.setType(InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY.getValue());
        log.info("银行通道异常,新增商户自动回切三方任务,商户号: {}", reqDTO.getMerchantSn());
        return interScheduleTaskService.insertTasks(mainTaskDO, Lists.newArrayList(subTaskDO)) > 0;
    }

    private Set<String> listMerchantTraAppIds(String merchantSn, String provider) {
        Set<String> traAppIds = subBizParamsDAO.listByMerchantSnAndProvider(merchantSn, provider).stream().map(SubBizParamsDO::getTradeAppId).collect(Collectors.toSet());
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listByMerchantSnAndStatus(merchantSn, UseStatusEnum.IN_USE.getValue());
        if (CollectionUtils.isNotEmpty(merchantProviderParamsDOS)) {
            traAppIds.add(subBizParamsBiz.getPayTradeAppId());
        }
        return traAppIds;
    }

    /**
     * 校验商户是否可以入指定的groupId(mc_rule_group.group_id) 临时加急需求使用
     *
     * @param merchantFeature  商户特征
     * @param groupId          规则组id(mc_rule_group.group_id)
     * @param acquirerTypeEnum 收单机构
     * @return 校验结果 true-可以入 false-不可以入
     */
    @Override
    public boolean checkEligibilityToGroupId(MerchantFeatureReqDTO merchantFeature, String groupId, AcquirerTypeEnum acquirerTypeEnum) {
        try {
            MerchantFeatureBO merchantFeatureBO = BeanCopyUtils.copyProperties(merchantFeature, MerchantFeatureBO.class);
            merchantFeatureBO.setPersonalCertificateType(PersonalCertificateTypeEnum.ID_CARD.getValue().toString());
            return mcRulesDecisionService.checkEligibilityByUnableRule(merchantFeatureBO, acquirerTypeEnum.getValue()).isCheckPass();
        } catch (Exception e) {
            log.error("校验商户是否可以入指定的groupId异常, merchantFeature: {}, groupId: {}, acquirer: {}", merchantFeature, groupId, acquirerTypeEnum, e);
            return true;
        }
    }

    /**
     * 获取商户所在收单机构的商户状态
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn, String acquirer) {
        return acquirerFacade.getSharedAbilityByAcquirer(acquirer).map(ability -> ability.getAcquirerMerchantStatus(merchantSn))
                .orElse(AcquirerMerchantStatusEnum.NORMAL);
    }

    @Override
    public void initBankAcquireData(InitBankAcquireInfoDTO info) {
        // 获取初始化信息中的各个字段
        final String acquire = info.getAcquire();
        final String provider = info.getProvider();
        final String acquireCHName = info.getAcquireCHName();
        final String mcProviderBeanName = info.getMcProviderBeanName();
        final List<Integer> payWayList = info.getPayWayList();

        // 构建 channel 和 payway 的映射关系
        final Map<String, Integer> channelPaywayMap = buildChannelPaywayMap(acquire, provider, payWayList);
        final Set<String> expectationChannelSets = new HashSet<>(channelPaywayMap.keySet());

        // 并行初始化 mc_acquirer 表和 mc_provider 表
        CompletableFuture<Void> acquirerFuture = CompletableFuture.runAsync(() -> initAcquirer(acquire, provider, acquireCHName));
        CompletableFuture<Void> providerFuture = CompletableFuture.runAsync(() -> initProvider(provider, acquire, acquireCHName, mcProviderBeanName, payWayList));

        // 获取当前有效的 mc_channel 记录
        CompletableFuture<Set<String>> actualChannelSetsFuture = CompletableFuture.supplyAsync(() ->
                mcChannelDAO.listAllEffectiveMcChannel().stream()
                        .filter(mcChannel -> Objects.equals(mcChannel.getAcquirer(), acquire))
                        .map(McChannelDO::getChannel)
                        .collect(Collectors.toSet())
        );

        // 等待 acquirer 和 provider 初始化完成
        CompletableFuture.allOf(acquirerFuture, providerFuture).join();

        try {
            // 等待获取实际存在的 channel 集合
            Set<String> actualChannelSets = actualChannelSetsFuture.get();

            // 找出预期存在的 channel 中但实际不存在的 channel
            final Sets.SetView<String> difference = Sets.difference(expectationChannelSets, actualChannelSets);
            List<CompletableFuture<Void>> createChannelFutures = difference.stream()
                    .filter(Objects::nonNull)
                    .map(channel -> CompletableFuture.runAsync(() ->
                            createMcChannel(channel, channelPaywayMap, provider, acquire, acquireCHName, info.getPayWayChannelNoMap())
                    ))
                    .collect(Collectors.toList());

            // 等待所有 channel 创建完成
            CompletableFuture.allOf(createChannelFutures.toArray(new CompletableFuture[0])).join();

            // 遍历 channelPaywayMap，创建不存在的 contract_rule
            List<CompletableFuture<Void>> createContractRuleFutures = channelPaywayMap.keySet().stream()
                    .map(rule -> CompletableFuture.runAsync(() ->
                            createContractRuleIfNotExists(rule, channelPaywayMap, provider, acquire, acquireCHName)
                    ))
                    .collect(Collectors.toList());

            // 等待所有 contract_rule 创建完成
            CompletableFuture.allOf(createContractRuleFutures.toArray(new CompletableFuture[0])).join();

            // 初始化 rule_group 表
            initRuleGroup(acquire, acquireCHName, channelPaywayMap);
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Error during initialization", e);
        }
    }

    /**
     * 初始化 mc_acquirer 表
     * @param acquire 收单机构标识
     * @param provider 提供商标识
     * @param acquireCHName 收单机构中文名称
     */
    private void initAcquirer(String acquire, String provider, String acquireCHName) {
        McAcquirerDO acquirerDO;
        try {
            acquirerDO = mcAcquirerDAO.getByAcquirer(acquire);
        } catch (com.wosai.upay.merchant.contract.exception.ContractBizException e) {
            acquirerDO = null;
        }
        if (Objects.nonNull(acquirerDO)) {
            return;
        }
        McAcquirerDO newAcquirerDO = new McAcquirerDO();
        newAcquirerDO.setAcquirer(acquire);
        newAcquirerDO.setName(acquireCHName);
        newAcquirerDO.setProvider(provider);
        newAcquirerDO.setTradeType(AcquirerTradeTypeEnum.INDIRECT.getValue());
        newAcquirerDO.setClearType(ClearTypeEnum.DIRECT.getValue());
        newAcquirerDO.setType(AcquirerOrgTypeEnum.BANK.getValue());
        newAcquirerDO.setFeeEffectiveType(FeeEffectiveTypeEnum.SQB.getValue());
        newAcquirerDO.setMetadata(JSONObject.toJSONString(Lists.newArrayList(CollectionUtil.hashMap("provider", provider, "name", acquireCHName))));
        newAcquirerDO.setCreateAt(new Date());
        newAcquirerDO.setUpdateAt(new Date());
        mcAcquirerDAO.insert(newAcquirerDO);
    }

    /**
     * 初始化 mc_provider 表
     * @param provider 提供商标识
     * @param acquire 收单机构标识
     * @param acquireCHName 收单机构中文名称
     * @param mcProviderBeanName 提供商 Bean 名称
     * @param payWayList 支付方式列表
     */
    private void initProvider(String provider, String acquire, String acquireCHName, String mcProviderBeanName, List<Integer> payWayList) {
        Optional<McProviderDO> mcProviderDO = mcProviderDAO.getByProvider(provider);
        if (mcProviderDO.isPresent()) {
            return;
        }
        McProviderDO providerDO = new McProviderDO();
        providerDO.setProvider(provider);
        providerDO.setAcquirer(acquire);
        providerDO.setName(acquireCHName);
        providerDO.setBeanName(mcProviderBeanName);
        List<PaymentMethod> paymentMethodList = payWayList.stream()
                .map(PaymentConfig::getPaymentMethod)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        providerDO.setMetadata(JSONObject.toJSONString(paymentMethodList));
        providerDO.setCreateAt(new Timestamp(new java.util.Date().getTime()));
        providerDO.setUpdateAt(new Timestamp(new java.util.Date().getTime()));
        mcProviderDAO.insert(providerDO);
    }

    /**
     * 构建 channel 和 payway 的映射关系
     * @param acquire 收单机构标识
     * @param provider 提供商标识
     * @param payWayList 支付方式列表
     * @return channel 和 payway 的映射关系
     */
    private Map<String, Integer> buildChannelPaywayMap(String acquire, String provider, List<Integer> payWayList) {
        Map<String, Integer> channelPaywayMap = new HashMap<>();
        channelPaywayMap.put(acquire, PaywayEnum.ACQUIRER.getValue());
        payWayList.forEach(payWay ->
                channelPaywayMap.put(String.format("%s-%s-%s", acquire, provider, payWay), payWay)
        );
        return channelPaywayMap;
    }

    /**
     * 创建 mc_channel 记录
     * @param channel channel 标识
     * @param channelPaywayMap channel 和 payway 的映射关系
     * @param provider 提供商标识
     * @param acquire 收单机构标识
     * @param acquireCHName 收单机构中文名称
     * @param payWayChannelNoMap payway和ChannelNo的映射关系
     */
    private void createMcChannel(String channel,
                                 Map<String, Integer> channelPaywayMap,
                                 String provider,
                                 String acquire,
                                 String acquireCHName,
                                 Map<Integer, String> payWayChannelNoMap) {
        Integer payWay = channelPaywayMap.get(channel);
        McChannelDO mcChannelDO = new McChannelDO();
        mcChannelDO.setChannel(channel);
        PaywayEnum paywayEnum = Arrays.stream(PaywayEnum.values())
                .filter(x -> Objects.equals(x.getValue(), payWay))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid payWay value"));
        mcChannelDO.setName(Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue()) ? acquireCHName : String.format("%s-%s", acquireCHName, paywayEnum.getText()));
        mcChannelDO.setSubject("上海喔噻");
        mcChannelDO.setChannelNo(StringUtils.isBlank(payWayChannelNoMap.get(payWay)) ? "" : payWayChannelNoMap.get(payWay));
        mcChannelDO.setPayway(payWay);
        mcChannelDO.setProvider(provider);
        mcChannelDO.setAcquirer(acquire);
        mcChannelDO.setStatus(1);
        mcChannelDO.setCreateAt(new Date());
        mcChannelDO.setUpdateAt(new Date());
        mcChannelDAO.insertOne(mcChannelDO);
    }

    /**
     * 如果 contract_rule 不存在，则创建新的 contract_rule 记录
     * @param rule 规则标识
     * @param channelPaywayMap channel 和 payway 的映射关系
     * @param provider 提供商标识
     * @param acquire 收单机构标识
     * @param acquireCHName 收单机构中文名称
     */
    private void createContractRuleIfNotExists(String rule, Map<String, Integer> channelPaywayMap, String provider, String acquire, String acquireCHName) {
        final Optional<McContractRuleDO> mcContractRuleDO = mcContractRuleDAO.getDOByRule(rule);
        if(mcContractRuleDO.isPresent()) {
            return;
        }else {
            Integer payWay = channelPaywayMap.get(rule);
            PaywayEnum paywayEnum = Arrays.stream(PaywayEnum.values())
                    .filter(x -> Objects.equals(x.getValue(), payWay))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Invalid payway value"));

            final McContractRuleDO contractRuleDO = new McContractRuleDO();
            contractRuleDO.setRule(rule);
            contractRuleDO.setName(Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue()) ? acquireCHName : String.format("%s-%s", acquireCHName, paywayEnum.getText()));
            contractRuleDO.setPayway(payWay);
            contractRuleDO.setProvider(provider);
            contractRuleDO.setAcquirer(acquire);
            contractRuleDO.setChannel(rule);
            contractRuleDO.setStatus(1);
            contractRuleDO.setType(Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue()) ? 1 : 2);
            contractRuleDO.setRetry(10);
            contractRuleDO.setIsDefault(Boolean.FALSE);
            contractRuleDO.setIsInsert(Boolean.TRUE);
            contractRuleDO.setIsInsertInfluPtask(Boolean.TRUE);
            contractRuleDO.setIsUpdate(Boolean.FALSE);
            contractRuleDO.setIsUpdateInfluPtask(Boolean.FALSE);
            contractRuleDO.setCreateAt(new Date());
            contractRuleDO.setUpdateAt(new Date());
            mcContractRuleDAO.insert(contractRuleDO);
        }
    }

    /**
     * 初始化 rule_group 表
     * @param acquire 收单机构标识
     * @param acquireCHName 收单机构中文名称
     * @param channelPaywayMap channel 和 payway 的映射关系
     */
    private void initRuleGroup(String acquire, String acquireCHName, Map<String, Integer> channelPaywayMap) {
        final String ruleGroup = mcRuleGroupDAO.getDefaultRuleGroup(acquire);
        if(StringUtils.isNotBlank(ruleGroup)) {
            return;
        }
        final McRuleGroupDO mcRuleGroupDO = new McRuleGroupDO();
        mcRuleGroupDO.setGroupId(acquire);
        mcRuleGroupDO.setName(acquireCHName + "规则组");
        mcRuleGroupDO.setVendor("核心服务");
        mcRuleGroupDO.setVendorApp("新增进件");
        mcRuleGroupDO.setStatus(1);
        mcRuleGroupDO.setDefaultStatus(1);
        List<RuleGroupDto.Rule> rules = new ArrayList<>();
        channelPaywayMap.forEach((rule, payWay) -> {
            RuleGroupDto.Rule ruleDTO = new RuleGroupDto.Rule();
            if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue())) {
                ruleDTO.setContract_rule(rule);
            } else {
                ruleDTO.setDepend_on(acquire);
                ruleDTO.setContract_rule(rule);
            }
            rules.add(ruleDTO);
        });
        mcRuleGroupDO.setRules(JSONObject.toJSONString(rules));
        mcRuleGroupDO.setCreateAt(new Timestamp(new java.util.Date().getTime()));
        mcRuleGroupDO.setUpdateAt(new Timestamp(new java.util.Date().getTime()));
        mcRuleGroupDO.setAcquirer(acquire);
        mcRuleGroupDAO.insertOne(mcRuleGroupDO);
        // RuleContext.refreshRule 方法刷新规则
        contractManagerService.refreshRuleContext();

    }

    @Override
    public void setImmediately(String merchantSn) {
        final Optional<McAcquirerChangeDO> processChange = mcAcquirerChangeDAO.getProcessChange(merchantSn);
        if(!processChange.isPresent()) {
            return;
        }
        final McAcquirerChangeDO mcAcquirerChangeDO = processChange.get();
        final McAcquirerChangeDO newChangeDO = new McAcquirerChangeDO() {{
            setId(mcAcquirerChangeDO.getId());
            setImmediately(1);
        }};
        mcAcquirerChangeDAO.updateByPrimaryKeySelective(newChangeDO);
    }
}
