package com.wosai.upay.job;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.database.instrumentation.springboot.EnableDataSourceTranslate;
import com.wosai.databus.event.merchant.contract.MerchantContractStatusChangeEvent;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@ImportResource("classpath:spring/*.xml")
@EnableScheduling
@MapperScan({"com.wosai.upay.job.mapper", "com.wosai.upay.job.refactor.mapper"})
@EnableDataSourceTranslate
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableApolloConfig(value = {"memo", "application"})
@EnableRetry
public class Application {

    public static void main(String[] args) {
        // 随便new一个AbstractEvent的子类，在项目启动之前将AbstractEvent里面的static代码块执行完毕
        MerchantContractStatusChangeEvent event = new MerchantContractStatusChangeEvent();
        ApplicationContext applicationContext = SpringApplication.run(Application.class, args);
        //捕捉kill命令
        MySignalHandler.install("TERM", applicationContext);
        //捕捉ctrl+c命令
        MySignalHandler.install("INT", applicationContext);
    }


}

