# 商户筛选规则系统设计文档

## 概述

商户筛选规则系统是一个基于规则引擎的商户校验系统，支持灵活的规则配置和实时更新。系统采用策略模式和责任链模式设计，确保良好的扩展性和可维护性。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[Apollo配置中心] --> B[配置监听器]
    B --> C[规则引擎]
    C --> D[规则执行器]
    D --> E[具体规则实现]
    
    F[商户信息] --> C
    C --> G[校验结果]
    
    subgraph "规则类型"
        F1[支付源行业活动规则]
        F2[智慧积分活动规则]
        F3[定时DO规则]
        F4[自定义共享黑名单规则]
        F5[商户分账规则]
        F6[其他业务规则]
    end
    
    E --> F1
    E --> F2
    E --> F3
    E --> F4
    E --> F5
    E --> F6
```

### 核心组件

1. **规则引擎 (RuleEngine)**
   - 核心执行引擎
   - 按优先级执行规则
   - 聚合执行结果
   - 直接从Apollo获取配置

2. **规则执行器 (RuleExecutor)**
   - 具体规则的执行逻辑
   - 支持不同类型的规则实现
   - 提供扩展接口

3. **配置监听器 (ConfigListener)**
   - 监听Apollo配置变化
   - 触发规则配置更新

## 组件和接口设计

### 核心接口

#### 1. 规则接口 (MerchantRule)
```java
public interface MerchantRule {
    /**
     * 规则执行
     * @param context 商户上下文信息
     * @return 规则执行结果
     */
    RuleResult execute(MerchantContext context);
    
    /**
     * 获取规则类型
     */
    String getRuleType();
    
    /**
     * 获取规则优先级
     */
    int getPriority();
    
    /**
     * 是否需要校验
     */
    boolean needValidation();
}
```

#### 2. 规则引擎接口 (RuleEngine)
```java
public interface RuleEngine {
    /**
     * 执行所有适用的规则
     * @param context 商户上下文
     * @param scenario 执行场景
     * @return 聚合执行结果
     */
    ValidationResult validate(MerchantContext context, String scenario);
    
    /**
     * 刷新规则配置
     */
    void refreshRules();
}
```

### 数据模型

#### 1. 规则配置 (RuleConfig)
```java
public class RuleConfig {
    private String keepAliveCheckRuleTypeEnum;           // 规则类型
    private String description;        // 条件说明
    private boolean needValidation;    // 是否需要校验
    private int priority;              // 优先级
    private Map<String, Object> params; // 规则参数
}
```

#### 2. 商户上下文 (MerchantContext)
```java
public class MerchantContext {
    private String merchantId;         // 商户ID
    private String merchantType;       // 商户类型
    private Map<String, Object> attributes; // 商户属性
    private String currentStatus;      // 当前状态
    private List<String> activities;   // 参与的活动
    private Map<String, Object> businessData; // 业务数据
}
```

#### 3. 规则执行结果 (RuleResult)
```java
public class RuleResult {
    private boolean passed;            // 是否通过
    private String keepAliveCheckRuleTypeEnum;           // 规则类型
    private String message;            // 结果消息
    private String errorCode;          // 错误码
    private Map<String, Object> details; // 详细信息
}
```

#### 4. 校验结果 (ValidationResult)
```java
public class ValidationResult {
    private boolean success;           // 整体是否成功
    private List<RuleResult> ruleResults; // 各规则执行结果
    private String failureReason;      // 失败原因
    private String blockedBy;          // 被哪个规则阻止
}
```

## 数据模型

### Apollo配置结构
```json
{
  "merchant.validation.rules.open_circle": [
    {
      "keepAliveCheckRuleTypeEnum": "PAYMENT_SOURCE_ACTIVITY",
      "description": "商户参与支付源行业活动且在活动中",
      "needValidation": true,
      "priority": 1,
      "params": {
        "activityTypes": ["PAYMENT_SOURCE_INDUSTRY"]
      }
    },
    {
      "keepAliveCheckRuleTypeEnum": "SMART_POINTS_ACTIVITY", 
      "description": "智慧积分活动规则",
      "needValidation": true,
      "priority": 2
    }
  ],
  "merchant.validation.rules.pre_execute_circle": [
    {
      "keepAliveCheckRuleTypeEnum": "TIMING_DO_RULE",
      "description": "定时DO规则",
      "needValidation": true,
      "priority": 1
    }
  ]
}
```

## 错误处理

### 错误类型
1. **配置错误**
   - 规则配置格式错误
   - 必要参数缺失
   - 规则类型不支持

2. **执行错误**
   - 规则执行异常
   - 数据访问失败
   - 超时错误

3. **系统错误**
   - Apollo连接失败
   - 内存不足
   - 并发冲突

### 错误处理策略
- 配置错误：记录日志，使用默认配置或跳过该规则
- 执行错误：记录详细错误信息，返回校验失败
- 系统错误：降级处理，使用缓存配置

## 测试策略

### 单元测试
- 各个规则实现的单元测试
- 规则引擎核心逻辑测试
- 配置管理器功能测试

### 集成测试
- Apollo配置集成测试
- 规则引擎端到端测试
- 并发场景测试

### 性能测试
- 大量规则执行性能测试
- 高并发场景压力测试
- 内存使用情况测试

## 扩展性设计

### 新规则类型扩展
1. 实现 `MerchantRule` 接口
2. 注册到规则工厂
3. 配置Apollo规则参数

### 新场景支持
1. 在Apollo中添加新场景配置
2. 更新场景枚举
3. 配置场景特定的规则

### 监控和告警
- 规则执行耗时监控
- 规则命中率统计
- 配置更新成功率监控
- 异常规则执行告警