package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ContractReconsiderTask {
    private Long id;

    private String merchant_sn;

    private String merchant_name;

    private Long sub_task_id;

    private Long p_task_id;

    private String contract_id;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String event_context;

    private String result;


}