package com.wosai.upay.job.model;

/**
 * @Description:报备对外状态
 * <AUTHOR>
 * Date 2019/10/28 11:48 上午
 **/
public enum ContractStatusCode {

    NO_TASK("0000", "该商户无进件任务"),

    //报备处理中
    PENDING_TASK("1111", "申请任务待处理"),
    UPGRADE_TASK_CONTRACTING("2222", "该商户升级任务处理中"),
    PENDING_EVENT("3333", "进件事件待处理"),
    UPGRADE_WAIT_FOR_AUTH("10011", "待授权"),
    UPGRADE_MICRO_WAIT_FOR_CONFIRM("10009", "待联系人确认"),
    UPGRADE_PUB_WAIT_FOR_CONFIRM("10010", "企业营业执照，联系人姓名不等于法人姓名时，提交商户申请，查询结果返回待联系人确认"),
    UPGRADE_APPLY_FREEZE("10012", "微信返回申请单冻结"),

    //最终状态
    UPGRADE_TASK_FAIL("10013", "微信升级认证审失败"),

    UPGRADE_TASK_SUCCESS("8888", "升级任务处理成功"),

    //未知状态
    UNKNOWN_STATUS_CODE("9999", "未知状态"),

    PHOTO_NOT_SUBMIT("10014","异地开户，待商家提交店铺照片");
    private String code;
    private String msg;

    ContractStatusCode(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static ContractStatusCode toStatusCode(String code) {
        if (code == null) {
            return null;
        }
        for (ContractStatusCode statusCode : ContractStatusCode.values()) {
            if (statusCode.getCode().equals(code)) {
                return statusCode;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
