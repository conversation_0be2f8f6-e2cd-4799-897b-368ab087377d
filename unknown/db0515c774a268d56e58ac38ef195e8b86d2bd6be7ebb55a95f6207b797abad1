package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.dto.request.AcquirerSupportSettlementRepDTO;
import com.wosai.upay.job.model.dto.response.AcquirerSupportSettlementRspDTO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 收单机构支持的账户结算类型服务
 *
 * <AUTHOR>
 * @date 2024/3/7 17:05
 */
@JsonRpcService("/rpc/acquirer/support/settlement")
@Validated
public interface AcquirerSupportSettlementService {

    /**
     * 根据营业执照类型获取收单机构支持的账户结算类型
     *
     * @param licenseType 营业执照类型
     * @return 收单机构支持的账户结算类型
     */
    List<AcquirerSupportSettlementRspDTO> listByLicenseType(Integer licenseType);

    /**
     * 更新收单机构支持结算类型
     *
     * @param acquirerSupportSettlementRepDTOs 收单机构支持结算类型请求dto
     * @return effect rows
     */
    Integer updateAcquirerSupportSettlement(List<AcquirerSupportSettlementRepDTO> acquirerSupportSettlementRepDTOs);
}
