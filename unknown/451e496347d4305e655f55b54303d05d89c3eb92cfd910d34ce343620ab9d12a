package com.wosai.upay.job.Constants;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.wosai.data.util.CollectionUtil;

/**
 * <AUTHOR>
 * @date 2020-11-24
 */
public class BlueSeaConstant {

    //活动状态

    /**
     * 已提交,等待定时任务处理
     */
    public static final int PENDING = 0;

    /**
     * 已升级M3
     */
    public static final int M3 = 2;

    /**
     * 已发起授权
     */
    public static final int OPERATION_APPLY = 3;

    /**
     * 已升级M4
     */
    public static final int M4 = 4;

    /**
     * 创建支付宝门店申请
     */
    public static final int SHOP_CREATED_APPLY = 5;

    /**
     * 支付宝门店创建成功
     */
    public static final int SHOP_CREATED = 6;

    /**
     * 已三绑定
     */
    public static final int DEVICE_BIND = 8;

    /**
     * 活动申请 前置条件已经完成
     */
    public static final int WAIT_APPLY = 9;

    /**
     * 提交报名
     */
    public static final int ACTIVITY_CREATE = 10;

    /**
     * 提交修改
     */
    public static final int ACTIVITY_MODIFY = 11;

    /**
     * 报名成功
     */
    public static final int ACTIVITY_CREATE_SUCCESS = 12;

    /**
     * 交易参数切换成功
     */
    public static final int SUCCESS = 19;

    /**
     * 失败
     */
    public static final int FAIL = 20;



    //活动类型
    /**
     * 1新蓝海
     */
    public static final int blueSeaType = 1;

    /**
     * 2狂欢月
     */
    public static final int carnivalMonthType = 2;

    /**
     * 3快消
     */
    public static final int kx = 3;

    /**
     * 终端刷新
     */
    public static final int terminalFresh = 4;
    /**
     * 高校活动
     */
    public static final  int  UNIVERSITY_ACTIVITY=5;

    /**
     * 支付宝教培活动
     */
    public static final  int  ALI_EDU_ACTIVITY = 6;


    /**
     * 审批备注后缀
     */
    public static final String suffix = ",请联系客服";

    /**
     * 老板支付宝账号
     */
    public static final String ALIACCOUNT = "aliAccount";

    /**
     * 审批id
     */
    public static final String TEMPLATE_ID = "templateId";

    /**
     * 开通M4业务错误提示
     */
    public static final String OPERATION_APPLY_MEMO = "授权失败，请检查支付宝账号是否正确，重新提交审批";

    /**
     * 升级M4 及 活动报名 用到的常量
     */
    public static class AlipayOpenSpOperationApply {
        /**
         * ACCOUNT_BIND：账号绑定，仅对于间连商户
         */
        public static final String OPERATE_TYPE_ACCOUNT_BIND = "ACCOUNT_BIND";
        /**
         * OPERATION_AUTH：代运营授权，支持间连及直连商户
         */
        public static final String OPERATE_TYPE_OPERATION_AUTH = "OPERATION_AUTH";
        /**
         * 操作类型为账号绑定时，填OPENAPI_BIND_DEFAULT
         */
        public static final String ACCESS_PRODUCT_CODE_OPENAPI_BIND_DEFAULT = "OPENAPI_BIND_DEFAULT";

        /**
         * 操作类型为代运营授权时，填OPENAPI_AUTH_DEFAULT。
         */
        public static final String ACCESS_PRODUCT_CODE_OPENAPI_AUTH_DEFAULT = "OPENAPI_AUTH_DEFAULT";

        /**
         * biz_scene 直连餐饮
         */
        public static final String BLUE_SEA_FOOD_APPLY = "BLUE_SEA_FOOD_APPLY";

        /**
         * biz_scene 间连餐饮
         */
        public static final String BLUE_SEA_FOOD_INDIRECT_APPLY = "BLUE_SEA_FOOD_INDIRECT_APPLY";

        /**
         * 直连快消
         */
        public static final String BLUE_SEA_FMCG_APPLY = "BLUE_SEA_FMCG_APPLY";

        /**
         * 间连快消
         */
        public static final String BLUE_SEA_FMCG_INDIRECT_APPLY = "BLUE_SEA_FMCG_INDIRECT_APPLY";
    }


    public static final List<String> blueSeaTerminalVenderAppAppId = Arrays.asList("2019121800002436", "2019090300001975");
    public static final List<String> blueSeaPcTerminalVenderAppAppid = Arrays.asList("2017030600000083", "2019032900021744", "2017030600000083", "2019091800002007");
    public static final Map<String, String> SUPPLY_ID = CollectionUtil.hashMap("SM", "2088521507558680", "ML", "201811191100464233", "QT", "201810251600319950", "QC", "201812271300540049");
    public static final String SUPPLY_KEY = "blueSea_supply";
    public static final String BLUESEA_TERMINAL_VENDER_APP_APPID = "bluesea_terminal_vender_app_APPid";
    public static final String BLUESEA_PCTERMINAL_VENDER_APP_APPID = "bluesea_pc_terminal_vender_app_APPid";

    public static final String REASON_NOVENDOR = "当前没有指定报名新蓝海活动的终端";
    public static final String REASON_NODEVICE = "商户当前没有绑定的蜻蜓终端";
    public static final String REASON_BINDFAIL = "当前所有终端均绑定失败";
    public static final String REASON_NOCONTEXT = "查询不到可提交新蓝海报名商户报名信息";
    public static final String REASON_UPLOADFAIL = "图片上传失败";
    public static final String REASON_REPETIVENESS = "该商户已成功报名此活动,或该商户活动报名中，暂不可重复提交";
    public static final String REASON_NOSUCCESSTASK = "该商户没有报名成功的支付宝活动";
    public static final String REASON_NOMERCHANT = "商户不存在";
    public static final String REASON_SUCCESS = "成功";
    public static final String REASON_NOLICENSE = "该商户没有营业执照，请上传营业执照后再报名支付宝活动";
    public static final String REASON_NOTOBACOO = "烟草行业商户需要上传烟草行业许可证";
    public static final String REASON_NOSHOP = "生活百货行业商户需要上传行业许可证";
    public static final String REASON_REFRESHTERMINAL = "终端刷新失败，请联系客服提交技术工单";
    public static final String TERMINAL_SUCCESS = "成功（设备绑定成功,交易参数切换成功）";
    public static final String TERMINAL_SUCCESS_NONEEDUPDATE = "成功（设备绑定成功，无需切换交易参数）";
    public static final String TERMINAL_FAIL = "失败（设备绑定失败，请联系客服提交技术工单）";
    public static final String TERMINAL_FAIL_UPDATE = "失败（交易参数切换失败，请联系客服提交技术工单）";
    public static final String TERMINAL_PC = "关联pc终端";
    public static final String CATERING_SERVICE = "餐饮服务许可证";
    public static final String FOOD_HYGIENE = "食品卫生许可证";
    public static final String FOOD_MANAGEMENT = "食品经营许可证";
    public static final String FOOD_CIRCULATION = "食品流通许可证";
    public static final String FOOD_PRODUCTION = "食品生产许可证";

    /**
     * 终端sn
     */
    public static final String FINGER_PRINT = "device_fingerprint";
    /**
     * 终端suppplier_id
     */
    public static final String SUPPLIER_ID = "supplier_id";

    /**
     * 新蓝海活动
     */
    public static class AlipayActivityStatus {
        /**
         * 新蓝海报名消息推送申请单状态，报名成功。
         */
        public static final String STATUS_PASS = "PASS";
        /**
         * 新蓝海报名消息推送申请单状态，报名失败。
         */
        public static final String STATUS_FAIL = "FAIL";
        /**
         * 新蓝海报名消息推送申请单状态，审核中。
         */
        public static final String STATUS_AUDITING = "AUDITING";
    }

    /**
     * 查询服务商代运营操作结果
     */
    public static class HandleStatus {
        /**
         * 新蓝海报名消息推送申请单状态，审核中。
         */
        public static final String HANDLE_STATUS_SUCCESS = "SUCCESS";

        /**
         * 新蓝海报名消息推送申请单状态，审核中。
         */
        public static final String HANDLE_STATUS_PROCESSS = "PROCESS";
    }


    public static final List<String> codeList = Lists.newArrayList("ADDRESS_PROVINCE_CODE_ILLEGAL", "ADDRESS_CITY_CODE_ILLEGAL", "ADDRESS_DISTRICT_CODE_ILLEGAL");
    public static final String BLUESEA_TAG = "a2185803-936f-4fe3-bc67-f90f9b276597";
    public static final String KX_TAG = "878e7c02-12af-4cd0-809f-107b47c64953";
    public static final String BLUESEA_TAG_OPEN = "支付宝新蓝海活动";
    public static final String KX_TAG_OPEN = "支付宝快消活动";
    public static final String BLUESEA_TAG_CLOSE = "支付宝新蓝海活动结束";

    public static final String TIME_OUT = "商户24小时内未在支付宝App确认授权，请重新发起审批";
}
