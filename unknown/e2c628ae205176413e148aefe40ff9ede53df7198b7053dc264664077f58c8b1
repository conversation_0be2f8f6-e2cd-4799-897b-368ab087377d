package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * AcquirerDto
 *
 * <AUTHOR>
 * @date 2019-07-17 10:12
 */
@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcquirerDto implements Serializable {
    private static final long serialVersionUID = -903533092215024604L;
    private Integer id;

    private String acquirer;

    private String name;

    private Integer clear_type;

    private Date create_at;

    private Date update_at;

    private List<Provider> metadata;

    @Data
    public static class Provider {
        private String provider;
        private String name;
        private List<ProviderDto> provider_dtos;

    }
}
