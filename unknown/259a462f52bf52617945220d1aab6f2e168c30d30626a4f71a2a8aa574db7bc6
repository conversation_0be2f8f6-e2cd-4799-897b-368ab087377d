  package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


  /**
   * @Description: 华夏返回状态对应的描述
   * <AUTHOR>
   * @Date 2021/7/29 09:29
   */
  @Getter
  @AllArgsConstructor
  public enum HxbStatusEnum {
      WAIT(1, "待提交"),
      PROCESS(2, "处理中"),
      SUCCESS(3, "已完成"),
      REJECT(5, "退回"),
              ;

      private Integer code;
      private String message;


      public static String getMessage(Integer code) {
          final HxbStatusEnum[] descriptions = values();
          for (HxbStatusEnum description : descriptions) {
              if (Objects.equals(description.getCode(), code)) {
                  return description.getMessage();
              }
          }
          return null;
      }

      public static void main(String[] args) {
          System.out.println(getMessage(2));
      }
  }