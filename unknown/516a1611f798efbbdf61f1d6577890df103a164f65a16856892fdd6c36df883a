package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 客服作业平台状态枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum CustomerPlatformStatusEnum {
    //开始状态
    PENDING(0,"审核中"),

    //中间状态
    PROGRESSING(1,"审核中"),

    //等待微信授权
    WAIT_FOR_AUTH(2,"审核中"),

    //限于对公商户入网使用
    PAY_FOR_WAIT(3,"审核中"),

    //结束状态
    SUCCESS(5,"审核成功"),

    FAIL(6,"审核失败"),
    ;

    private Integer code;
    private String message;

    public static String getMessage(Integer code) {
        final CustomerPlatformStatusEnum[] subMerchantIdUseTypes = values();
        for (CustomerPlatformStatusEnum statusEnum : subMerchantIdUseTypes) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum.getMessage();
            }
        }
        return null;
    }
}