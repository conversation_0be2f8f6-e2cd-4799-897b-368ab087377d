package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 电子合同人工审核回调
 * <AUTHOR>
 * @Date 2024/12/26 16:39
 */
@Data
public class EcApplyPushDTO {
        @JsonProperty("order_no")
        private String orderNo;

        @JsonProperty("org_id")
        private int orgId;

        @JsonProperty("ec_apply_id")
        private long ecApplyId;

        /**
         * WAIT 待审核
         * PASS 审核通过 REFUSE 审核拒绝
         */
        @JsonProperty("audit_status")
        private String auditStatus;

        /**
         * 人工审核结果说明
         */
        @JsonProperty("audit_desc")
        private String auditDesc;

        /**
         * 签约H5地址	审核通过时返回
         */
        @JsonProperty("sign_h5_url")
        private String signH5Url;


        /**
         * 签约H5地址过期时间
         */
        @JsonProperty("sign_h5_url_exp_tm")
        private String signH5UrlExpTm;

}
