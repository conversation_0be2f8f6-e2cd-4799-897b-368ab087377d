package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

/**
 * @Description:
 * <AUTHOR>
 * Date 2019/10/16 5:14 下午
 **/
@JsonRpcService("/rpc/authEvent")
@Validated
public interface MerchantAuthEventService {

    /**
     * @return
     * <AUTHOR>
     * @Description:插入商户需要升级的数据总线事件
     * @time 4:42 下午
     **/
    void insertAuthEvent(@NotEmpty String merchantId, String weixinMerchantId, boolean needAuth);


    /**
     * @param mchId 微信子商户号
     * <AUTHOR>
     * @Description:判断改微信子商户号是否已授权 true 是
     * 依据报备服务存储的参数表信息  或是报备参数表没有 也会返回false 不会去查询微信实际接口
     * @time 4:44 下午
     **/
    boolean getWeixinMchIdAuthStatus(@NotEmpty String mchId);

    /**
     * @param merchantSn 收钱吧商户号
     * <AUTHOR>
     * @Description:判断该商户 当前在用的微信子商户号 是否已授权
     * 当前在用微信子商户号 取报备服务的参数表中的
     * @time 4:45 下午
     **/
    boolean getAuthStatus(@NotEmpty String merchantSn);
}
