package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 收单机构终端任务类型
 * <AUTHOR>
 * @Date: 2022/3/1 3:15 下午
 */
@Getter
@AllArgsConstructor
public enum ProviderTerminalTaskTypeEnum {

    BOUND_ALL_TERMINAL(1, "新增子商户号绑定所有终端"),
    BOUND_ALL_SUB_MCH(2, "新增终端绑定所有子商户号"),
    UNBIND_TERMINAL_ALL_SUB_MCH(3, "解绑当前终端对应的所有子商户号"),
    BIND_STORE_ALL_SUB_MCH(4, "新增门店级别终端绑定所有子商户号"),
    ;


    private int type;
    private String message;


}
