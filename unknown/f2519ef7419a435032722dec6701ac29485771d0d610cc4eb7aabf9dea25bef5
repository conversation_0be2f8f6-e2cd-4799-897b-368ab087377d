package com.wosai.upay.job.Constants;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
public class OnlinePaymentConstant {

    /**
     * 开通状态
     */
    public static class OpenStatus {
        /**
         * 未开通
         */
        public static final Integer NOT_OPEN = 0;

        /**
         * 开通中
         */
        public static final Integer OPENING = 1;

        /**
         * 开通成功
         */
        public static final Integer OPEN_SUCCESS = 2;

        /**
         * 开通失败
         */
        public static final Integer OPEN_FAIL = 3;
    }

    /**
     * 状态常量
     */
    public static class ApplyStatus {
        /**
         * 已提交
         */
        public static final Integer PENDING = 0;

        /**
         * 申请中
         */
        public static final Integer APPLYING = 1;

        /**
         * 申请成功
         */
        public static final Integer SUCCESS = 2;

        /**
         *申请失败
         */
        public static final Integer FAIL = 3;
    }

    /**
     * 过程状态常量
     */
    public static class ApplyProcessStatus {
        /**
         * 未处理
         */
        public static final Integer PENDING = 0;

        /**
         * 报备成功
         */
        public static final Integer CONTRACT_SUCCESS = 11;

        /**
         * 待子商户号授权
         */
        public static final Integer WAIT_FOR_AUTH = 12;

        /**
         * 等待审核
         */
        public static final Integer WAIT_FOR_AUDIT = 13;

        /**
         * 报备失败
         */
        public static final Integer CONTRACT_FAIL = 21;

        /**
         * 提交微信授权任务失败
         */
        public static final Integer SUBMIT_AUTH_FAIL = 22;

        /**
         * 查询微信子商户号授权失败
         */
        public static final Integer QUERY_AUTH_FAIL = 23;

        /**
         * 交易参数切换失败
         */
        public static final Integer CHANGE_PARAMS_FAIL = 24;
        /**
         * 支付宝审核失败
         */
        public static final Integer AUDIT_FAIL = 25;


        /**
         *申请成功
         */
        public static final Integer SUCCESS = 33;
    }

    public static final Integer AUTH_TIMEOUT_DAY = 7;
}
