package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * Created by lihebin on 2018/7/10.
 */
@JsonRpcService("/rpc/configSupport")
@Validated
public interface ConfigSupportService {


    /**
     * app自定义关注公众号白名单接口
     * @param params
     * @return
     */
    Map getMerchantModuleWhite(Map params);




    /**
     * 获取商户城市
     * @param merchantSn
     * @return
     */
    String getMerchantCity(String merchantSn);


}
