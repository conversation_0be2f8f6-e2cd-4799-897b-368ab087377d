package com.wosai.upay.job.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/04/27
 */
@Data
public class IcbcParams {

    /**
     * 收钱吧商户号
     */
    @NotBlank(message = "merchant_sn 不能为空")
    private String merchant_sn;

    /**
     * 工商银行银行子商户号
     */
    @NotBlank(message = "provider_mch_id 不能为空")
    private String provider_mch_id;

    /**
     * 微信子商户号
     */
    @NotBlank(message = "weixin_sub_mch_id 不能为空")
    private String weixin_sub_mch_id;

    /**
     * 支付宝子商户号
     */
    @NotBlank(message = "alipay_sub_mch_id 不能为空")
    private String alipay_sub_mch_id;

    /**
     * 云闪付子商户号
     */
    @NotBlank(message = "union_open_sub_mch_id 不能为空")
    private String union_open_sub_mch_id;

    /**
     * 不知道什么东西，交易要用
     */
    @NotBlank(message = "app_id 不能为空")
    private String app_id;

    /**
     * 费率
     */
    @NotBlank(message = "fee_rate 不能为空")
    private String fee_rate;


    /**
     * 证件号结尾4位
     */
    @NotBlank(message = "identify_end 不能为空")
    private String identify_end;

    /**
     * 银行卡持有人
     */
    @NotBlank(message = "holder_name 不能为空")
    private String holder_name;

    /**
     * 商户私钥
     */
    private String private_key;

    /**
     * 商户公钥
     */
    private String public_key;

    /**
     * 是否上送微信Subappid
     */
    private Boolean should_send_sub_app_id;

    /**
     * 是否立即切换通道
     */
    private Boolean change_immediately;


    /**
     * 操作人
     */
    @NotBlank(message = "操作人不为空")
    private String operate;


    public IcbcParams setMerchant_sn(String merchant_sn) {
        this.merchant_sn = merchant_sn.trim();
        return this;
    }

    public IcbcParams setProvider_mch_id(String provider_mch_id) {
        this.provider_mch_id = provider_mch_id.trim();
        return this;
    }

    public IcbcParams setWeixin_sub_mch_id(String weixin_sub_mch_id) {
        this.weixin_sub_mch_id = weixin_sub_mch_id.trim();
        return this;
    }

    public IcbcParams setAlipay_sub_mch_id(String alipay_sub_mch_id) {
        this.alipay_sub_mch_id = alipay_sub_mch_id.trim();
        return this;
    }

    public IcbcParams setUnion_open_sub_mch_id(String union_open_sub_mch_id) {
        this.union_open_sub_mch_id = union_open_sub_mch_id.trim();
        return this;
    }

    public IcbcParams setApp_id(String app_id) {
        this.app_id = app_id.trim();
        return this;
    }

    public IcbcParams setFee_rate(String fee_rate) {
        this.fee_rate = fee_rate.trim();
        return this;
    }

    public IcbcParams setIdentify_end(String identify_end) {
        this.identify_end = identify_end.trim();
        return this;
    }

    public IcbcParams setHolder_name(String holder_name) {
        this.holder_name = holder_name.trim();
        return this;
    }
}
