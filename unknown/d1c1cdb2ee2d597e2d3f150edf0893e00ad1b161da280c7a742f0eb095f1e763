package com.wosai.upay.job.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

@Data
public class QueryIsPostalCardExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "账户类型", width = 20)
    private String accType;


    @Excel(name = "账户名称", width = 20)
    private String accountName;

    @Excel(name = "账户号码", width = 20)
    private String accountId;


    @Excel(name = "查询结果", replace = {"邮政_false", "邮储银行_true"}, width = 20)
    private Boolean result;

    @Excel(name = "错误信息", width = 200)
    private String errorMessage;

    @Override
    public int getRowNum() {
        return 0;
    }

    @Override
    public void setRowNum(int i) {

    }

    @Override
    public String getErrorMsg() {
        return null;
    }

    @Override
    public void setErrorMsg(String s) {

    }
}
