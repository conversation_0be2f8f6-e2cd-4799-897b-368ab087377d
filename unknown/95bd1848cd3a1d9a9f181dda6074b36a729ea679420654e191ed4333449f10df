package com.wosai.upay.job.model.acquirer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020-04-23
 */
@Data
@NoArgsConstructor
public class ProcessDetail {

    private int status;

    private String memo;

    private long update_at;

    public ProcessDetail(int status, String memo, long update_at) {
        this.status = status;
        this.memo = memo;
        this.update_at = update_at;
    }
}
