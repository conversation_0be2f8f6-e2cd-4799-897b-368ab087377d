package com.wosai.upay.job.model.dto.response;

import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 错误信息提示文案响应DTO
 *
 * <AUTHOR>
 * @date 2024/9/2 16:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErrorInfoPromptTextRspDTO {

    /**
     * 文案要被哪端(crm,SP,app,consumer ErrorMsgViewEndpointTypeEnum)看到
     */
    private ErrorMsgViewEndpointTypeEnum errorMsgViewEndpointTypeEnum;

    /**
     * 提示文案
     */
    private String promptText;

}
