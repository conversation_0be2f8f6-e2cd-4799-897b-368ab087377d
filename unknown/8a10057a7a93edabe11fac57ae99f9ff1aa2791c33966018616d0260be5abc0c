package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.job.enume.PlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Data
public class UnionPayOpenStatusQueryReq {

    @NotBlank(message = "商户ID不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    @NotNull(message = "来源平台不能为空")
    private PlatformEnum platform;

    private String acquirer;
}
