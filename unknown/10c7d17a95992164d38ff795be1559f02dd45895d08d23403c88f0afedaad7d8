package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jerry
 * @date: 2019/6/10 11:06
 * @Description:监听变更字段阿波罗配置 后期 增加监听表，增加信息变更模块定义 这个对象需要重新定义
 */
@Data
@Accessors(chain = true)
public class DataChangeConfig {

    private List<String> merchant = new ArrayList<>();
    private List<String> basicBank = new ArrayList<>();
    private List<String> account = new ArrayList<>();
    private List<String> feeRate = new ArrayList<>();


    @Override
    public String toString() {
        return "DataChangeConfig{" +
                "merchant=" + merchant +
                ", basicBank=" + basicBank +
                ", account=" + account +
                ", feeRate=" + feeRate +
                '}';
    }
}
