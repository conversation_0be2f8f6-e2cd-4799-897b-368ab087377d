package com.wosai.upay.job.model;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * @Description: 初始化银行收单机构信息
 * <AUTHOR>
 * @Date 2025/2/20 17:30
 */
@Data
public class InitBankAcquireInfoDTO {
    /**
     * 收单机构英文
     */
    @NotBlank(message = "收单机构不能为空")
    private String acquire;


    /**
     * 收单机构中文
     */
    @NotBlank(message = "收单机构中文不能为空")
    private String acquireCHName;

    /**
     * provider
     */
    @NotBlank(message = "provider不能为空")
    private String provider;

    /**
     * mcProviderBeanName
     */
    @NotBlank(message = "mc_provider 表 BeanName不能为空")
    private String mcProviderBeanName;


    /**
     * 支持的支付方式
     */
    @NotEmpty(message = "支付方式不能为空")
    private List<Integer> payWayList;


    /**
     * payway和ChannelNo的映射关系
     */
    @NotEmpty(message = "payway和ChannelNo的映射关系不能为空")
    private Map<Integer,String> payWayChannelNoMap;


}
