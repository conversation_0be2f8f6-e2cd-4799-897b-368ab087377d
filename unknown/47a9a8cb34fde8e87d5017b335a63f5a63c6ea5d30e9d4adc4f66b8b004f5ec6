package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ChannelActivity {
    private Long id;

    private String audit_id;

    private Long apply_id;

    private String mch_id;

    private String merchant_sn;

    private String merchant_id;

    private Integer type;

    private Integer status;

    private String form_body;

    private String out_uid;
    private String result;

    private Date create_at;

    private Date update_at;

}