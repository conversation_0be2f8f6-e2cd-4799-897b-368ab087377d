package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/8/4 11:08
 */
@Getter
@AllArgsConstructor
public enum PsbcProcessStatusEnum {

    PENDING(1, "未处理"),
    CONTRACT_APPLYING(10, "进件中"),
    CONTRACT_SUCCESS(20, "进件成功"),
    WX_AUTH_SUCCESS(30, "商家微信认证成功"),
    APPLYING_CHANGE_ACQUIRE(40, "发起切换收单机构"),
    SUCCESS(50, "申请成功"),
    FAIL(99, "申请失败"),
            ;

    private Integer code;
    private String desc;


    public static String getDesc(Integer code) {
        final PsbcProcessStatusEnum[] processStatusEnums = values();
        for (PsbcProcessStatusEnum processStatus : processStatusEnums) {
            if (Objects.equals(processStatus.getCode(), code)) {
                return processStatus.getDesc();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(getDesc(10));
    }
}
