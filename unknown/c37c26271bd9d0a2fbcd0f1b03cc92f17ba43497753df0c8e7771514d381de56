package com.wosai.upay.job.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 对象属性dto
 *
 * <AUTHOR>
 * @date 2024/3/13 15:29
 */
@Data
@ApiModel("对象属性dto")
public class ObjectPropertyDTO {

    /**
     * 对象属性值
     */
    @ApiModelProperty("对象属性值")
    private String objectPropertyValue;

    /**
     * 对象属性值的含义
     */
    @ApiModelProperty("对象属性值的含义")
    private String objectPropertyMeaning;

}
