package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.upay.job.validation.group.QueryMerchantProviderParamsList;
import com.wosai.upay.job.validation.group.UpdateMerchantProviderParams;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantProviderParamsDto implements Serializable {

    private static final long serialVersionUID = -7026559183683651446L;

    @NotBlank(message = "id 不能为空", groups = UpdateMerchantProviderParams.class)
    private String id;

    @NotBlank(message = "merchant_sn 商户号不能为空", groups = QueryMerchantProviderParamsList.class)
    private String merchant_sn;

    private String out_merchant_sn;

    private String channel_no;

    private String parent_merchant_id;

    private Integer provider;

    private String provider_merchant_id;

    private Integer payway;

    private Integer params_config_status;

    private String pay_merchant_id;

    private String weixin_sub_appid;

    private String weixin_subscribe_appid;

    private String weixin_sub_mini_appid;

    private String weixin_receipt_appid;

    private Integer status;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    private String contract_rule;

    private String rule_group_id;

    private Integer update_status;

    private Map<String, Object> extra;

    private Integer auth_status;

    private String wx_settlement_id;

    private Integer wx_use_type;

    private Integer disable_status;

}
