package com.wosai.upay.job.model;

import java.util.Arrays;
import java.util.List;

/**
 * Created by lih<PERSON><PERSON> on 2018/10/15.
 */
public class MerchantProviderContract {
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String OPT_TYPE = "opt_type";
    public static final String PROVIDER = "provider";
    public static final String PROCESS_STATUS = "process_status";
    public static final String SYNC_STATUS = "sync_status";
    public static final String RET_MSG = "ret_msg";

    public static final int OPT_TYPE_MERCHANT_CONTRACT = 0;                     //商户入网
    public static final int OPT_TYPE_UPDATE_BANK_ACCOUNT = 1;                   //更新商户银行账户信息
    public static final int OPT_TYPE_UPDATE_MERCHANT_FEERATE = 2;               //更新商户费率信息
    public static final int OPT_TYPE_UPDATE_MERCHANT_BASIC = 3;               //更新商户费率信息
    public static final int OPT_TYPE_UPDATE_MERCHANT_STATUS = 4;



    public static final int PROCESS_STATUS_WAITING = 0;
    public static final int PROCESS_STATUS_FINISH = 1;
    public static final int SYNC_STATUS_FAIL = 0;
    public static final int SYNC_STATUS_SUCCESS = 1;

    public static final String PROVIDER_LKL = "1002";

    public static final int VERIFY_STATUS_WAIT= 0;

}
