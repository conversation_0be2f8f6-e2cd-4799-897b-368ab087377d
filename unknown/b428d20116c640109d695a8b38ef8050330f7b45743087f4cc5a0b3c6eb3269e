package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 小微商户升级状态枚举
 * <AUTHOR>
 * @Date 2024/01/23
 */
@Getter
@AllArgsConstructor
public enum  MerchantUpgradeEnum {

    CHECK_FAIL("4", "校验失败"),
    INIT("0", "初始"),
    WAITING_SUBMIT("1", "待提交切通道"),
    CHANGE_ACQUIRER("2", "查询切通道结果"),
    SUCCESS("5", "成功"),
    FAIL("6", "失败");

    private String code;
    private String message;

}
