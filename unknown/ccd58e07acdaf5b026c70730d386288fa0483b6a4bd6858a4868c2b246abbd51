package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * @Author: jerry
 * @date: 2019/8/8 14:57
 * @Description:重新报备
 */
@Data
@Accessors(chain = true)
public class ReContract {

    @NotBlank(message = "规则不能为空")
    private String rule;
    @NotEmpty(message = "备注不能为空")
    private String remark;
    @NotBlank(message = "商户号不能为空")
    private String merchant_sn;

    private String userName;
    private String userId;
}
