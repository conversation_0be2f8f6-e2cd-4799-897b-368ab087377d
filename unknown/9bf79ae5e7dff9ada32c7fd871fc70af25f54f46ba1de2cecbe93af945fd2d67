package com.wosai.upay.job.service;


import com.googlecode.jsonrpc4j.JsonRpcService;

/**
 * 内部key value表Service层
 *
 * <AUTHOR>
 */
@JsonRpcService("/rpc/internal/key-value")
public interface InternalKeyValueService {

    /**
     * 新增一条键值对,存在则更新
     *
     * @param key   键
     * @param value 值
     * @return effect rows
     */
    int insertOrUpdateKeyValue(String key, String value);

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    String getValueByKey(String key);
}
