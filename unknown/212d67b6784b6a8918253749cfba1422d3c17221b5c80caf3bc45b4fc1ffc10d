package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2021/1/6
 */
@JsonRpcService("/rpc/directStatus")
@Validated
public interface DirectStatusService {

    int getDirectStatus(@NotEmpty(message = "商户号不能为空") String merchantSn, @NotEmpty(message = "应用标识不能为空") String devCode);
}
