package com.wosai.upay.job.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * @Description: 根据商户和获取入网子任务
 * <AUTHOR>
 * @Date 2021/1/22 10:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ContractSubTaskReq {

    @NotNull(message = "商户号不为空")
    private String merchantSn;

    private Integer payway;

    private Integer taskType;

    private Integer status;

    private String channel;

    private String contractRule;

    private String ruleGroupId;

}
