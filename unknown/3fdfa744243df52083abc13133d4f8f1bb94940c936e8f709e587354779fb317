package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 变更审核推送请求对象
 * <AUTHOR>
 * https://fundwx.fuiou.com/doc/#/scanentry/api_openBusiness?id=_592-%e5%8f%98%e6%9b%b4%e5%ae%a1%e6%a0%b8%e6%8e%a8%e9%80%81%e6%8e%a5%e5%8f%a3
 */
@Data
public class ChangeAuditPushRequest {

    /**
     * 变更单号
     * 必填，长度：20
     */
    @JsonProperty("modifyNo")
    private String modifyNo;

    /**
     * 商户名称
     * 必填，长度：20
     */
    @JsonProperty("insNameCn")
    private String insNameCn;

    /**
     * 富友商户号
     * 必填，长度：15
     */
    @JsonProperty("mchntCd")
    private String mchntCd;

    /**
     * 变更类型：
     * YL：银联二维码
     * LA：分账
     * PA：扫码预授权
     * IM：分期业务变更
     * M2：关闭商户
     * SF：商户开通
     * RZ：入账信息
     * BS：商户基本信息变更
     * SM：扫码常规变更
     * SD：收单常规变更
     * XZ：新增终端
     * ZD：终端替换
     * CJ：终端撤销
     * TZ：D0 业务
     * JS：结算方式变更
     * A2：支付宝特殊费率
     * SP：微信特殊渠道申请
     * WK：外卡开通
     */
    @JsonProperty("modifyType")
    private String modifyType;

    /**
     * 变更类型描述
     */
    @JsonProperty("modifyTypeDesc")
    private String modifyTypeDesc;

    /**
     * 变更提交时间
     * 必填，长度：32
     */
    @JsonProperty("submitDate")
    private String submitDate;

    /**
     * 变更处理时间
     * 必填，长度：32
     */
    @JsonProperty("dealDate")
    private String dealDate;

    /**
     * 审核结果
     * 1: 成功；0: 失败
     */
    @JsonProperty("auditResult")
    private String auditResult;

    /**
     * 变更详情
     */
    @JsonProperty("resultDesc")
    private String resultDesc;

    /**
     * 变更单备注
     */
    @JsonProperty("reserved")
    private String reserved;

    /**
     * 商户入网方式：
     * 0：手机版
     * 1：电脑版（平台）
     * 2：收单进件接口
     * 4：公众号
     * 5：聚合接口
     * 6：四方平台
     * 7：简易版公众号
     * 8：自助进件版
     * 9：富友通app
     * a：软件使用商户
     * f：富掌柜app
     * w：富掌柜pc
     */
    @JsonProperty("srcFlag")
    private String srcFlag;

    /**
     * 目标卡是否当前入账卡
     * 0或空: 不是；1: 是
     * 当modifyType为RZ: 入账信息时才返回
     */
    @JsonProperty("isEffectiveAcnt")
    private String isEffectiveAcnt;

    /**
     * 签名
     * 必填，长度：32
     */
    @JsonProperty("sign")
    private String sign;
}
