package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 小微商户升级个体
 * <AUTHOR>
 * @date 2024-01-23
 */
@Data
@Accessors(chain = true)
public class MerchantUpgradeRequest {

    /**
     * 商户sn
     */
    private String merchant_sn;

    /**
     * 商户id
     */
    private String merchant_id;

    /**
     * 工单类型
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态
     */
    private String message;

    /**
     * 目标通道
     */
    private String target_acquirer;



}
