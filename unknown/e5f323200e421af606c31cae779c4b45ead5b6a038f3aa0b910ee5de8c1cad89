package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
public class CheckStoreDiffProvinceReq {

    @JsonProperty("merchant_id")
    @NotEmpty(message = "merchant_id 不能为空")
    private String merchantId;


    @NotEmpty(message = "district_code 不能为空")
    @JsonProperty("district_code")
    private String districtCode;
}
