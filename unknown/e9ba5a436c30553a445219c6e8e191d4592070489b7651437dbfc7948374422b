package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/4/14
 * @Description:
 */

@Data
@Accessors(chain = true)
public class MerchantLakalaContract {
    private  int id;
    private String merchant_sn;
    private String contract_id;
    private  int opt_type;
    private int create_status;
    private  int callback_status;
    private  String request_params;
    private  String extra;
    private long ctime;
    private long mtime;
    private int deleted;
}
