package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
@Data
@Accessors(chain = true)
public class NewMchNetInReq {

    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    @NotBlank(message = "平台不能为空")
    private String platform;

    private String organizationId;

    /**
     * 如果入网命中黑名单，这个值可能会存在
     */
    private String failMsg;
}
