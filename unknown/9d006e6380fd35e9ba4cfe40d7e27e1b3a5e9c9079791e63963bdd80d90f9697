package com.wosai.upay.job.Constants;

/**
 * 切换收单机构
 * <AUTHOR>
 * @date 2020-04-23
 */
public class AcquirerChangeStatus {

    /**
     * 已提交
     */
    public static final int PENDING = 1;

    /**
     * 报备中或者同步银行卡，成功流转到 3
     */
    public static final int CONTRACTING = 2;

    /**
     * 报备成功
     */
    public static final int CONTRACTED = 3;


    /**
     * 同步费率中，成功流转到 13
     */
    public static final int SYNC_FEE = 12;

    /**
     * 同步费率成功
     */
    public static final int SYNC_FEE_SUCCESS = 13;

    /**
     * 同步收单机构状态成功
     */
    public static final int CHANGE_ACQUIRER_STATUS_SUCCESS = 4;


    /**
     * 已关闭交易权限
     */
//    public static final int CLOSE_PAY = 5;

    /**
     * 已发起提现
     */
    public static final int WITHDRAW = 7;

    /**
     * 切换交易参数
     */
    public static final int CHANGE_PARAMS = 9;

    /**
     * 打开交易权限
     */
//    public static final int OPEN_PAY = 11;

    /**
     * 成功
     */
    public static final int SUCCESS = 19;

    /**
     * 失败
     */
    public static final int FAIL = 20;


}
