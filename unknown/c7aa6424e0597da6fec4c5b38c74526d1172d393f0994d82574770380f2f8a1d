package com.wosai.upay.job.enume;

import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 小微商户升级状态类型枚举
 * <AUTHOR>
 * @Date 2024/01/23
 */
@Getter
@AllArgsConstructor
public enum MerchantUpgradeTypeEnum {

    CHANGE_ACQUIRER(0, "切通道任务"),
    AUTH_FAIL(1, "有备用通道但是未授权子商户号"),
    CHECK_FAIL(2, "无备用通道");

    private Integer code;
    private String message;


}
