package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import org.springframework.validation.annotation.Validated;

/**
 * Created by lihebin on 2018/7/10.
 */
@JsonRpcService("/rpc/contractLz")
@Validated
public interface ContractWeixinLzService {



    /**
     * <AUTHOR>
     * @Description:（特殊流程）绿洲报名审核失败 调用该接口 切换微信交易参数至普通渠道(3600201329)
     * 切换失败抛出异常  只支持36TB4213315 (36T01224211存量苏州喔噻渠道)--》36002013293渠道切换
     **/
    void changeLzParamToNormal(@NotEmpty(message = "商户号不能为空") String merchantSn, String feeRate);

    void changeNormalToLz(@NotEmpty(message = "商户号不能为空") String merchantSn);
}
