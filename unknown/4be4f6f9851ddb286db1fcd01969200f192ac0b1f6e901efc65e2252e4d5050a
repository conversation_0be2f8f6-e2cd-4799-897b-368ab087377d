package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: jerry
 * @date: 2019/3/29 16:08
 * @Description:支付源
 */
@Data
@Accessors(chain = true)
public class PaySource {
    private String channelName;
    private String channelNo;
    private Integer payWay;
    private Integer defaultChannel;
    private Integer changeConfig;
    private Integer statusInfluPTask;



}
