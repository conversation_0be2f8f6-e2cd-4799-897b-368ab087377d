package com.wosai.upay.job.model.dto.crm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.job.service.WithKeyValue;
import lombok.Data;

/**
 * crm信息管理平台提交的字段信息
 *
 * <AUTHOR>
 * @date 2025/2/10 17:49
 */
@Data
public class CrmFormFieldInfoDTO implements WithKeyValue {

    /**
     * 字段key
     */
    @JsonProperty("field_key")
    private String fieldKey;

    /**
     * 字段值
     */
    @JsonProperty("field_value")
    private String fieldValue;

    /**
     * 字段code
     */
    @JsonProperty("field_code")
    private String fieldCode;

    /**
     * 字段名称
     */
    @JsonProperty("field_name")
    private String fieldName;

    /**
     * 字段主类型
     */
    @JsonProperty("field_main_type")
    private String fieldMainType;

    @Override
    public String getKey() {
        return fieldKey;
    }

    @Override
    public String getValue() {
        return fieldValue;
    }
}
