package com.wosai.upay.job.Constants;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2023/8/1 上午9:40
 */
public class ZftMerchantConstant {
    /**
     * 状态常量
     */
    public static class Status {
        /**
         * 直付通商户待提交
         */
        public static final Integer PENDING = 0;

        /**
         * 直付通商户申请中
         */
        public static final Integer APPLYING = 10;

        /**
         * 待签约
         */
        public static final Integer CONFIRMING = 20;

        /**
         *直付通商户申请成功
         */
        public static final Integer SUCCESS = 30;


        /**
         *直付通商户申请失败
         */
        public static final Integer FAIL = 40;
    }


    /**
     * Extra字段的key值,仅限参考后续拓展以实际代码为准
     */
    public static class Extra {
        /**
         * 申请单id
         */
        public static final String ORDERID = "orderId";
    }


    /**
     * 直付通结果提示
     */
    public static class Result {
        public static final String  ZFT_AUDIT_FAIL = "直付通审核失败";

        public static final String  ZFT_AUDIT_SUCCESS = "直付通审核成功";

        public static final String  ZFT_QUERY_FAIL = "直付通查询失败";

        public static final String  ZFT_APPLY_FAIL = "直付通申请失败";

    }




}
