  package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


  /**
   * @Description: 平安入网状态对应的描述
   * <AUTHOR>
   * @Date 2024/10/29 09:29
   */
  @Getter
  @AllArgsConstructor
  public enum PabContractStatusEnumV2 {
      WAIT("0", "审核中"),
      APPROVED("1", "审核中"),
      PENDING_WITH_COMPLETION("2", "审核中-补件"),
      FAIL("3", "审核拒绝"),
      DONE("4", "审核通过"),
              ;

      private String code;
      private String message;


      public static String getMessage(String code) {
          final PabContractStatusEnumV2[] descriptions = values();
          for (PabContractStatusEnumV2 description : descriptions) {
              if (Objects.equals(description.getCode(), code)) {
                  return description.getMessage();
              }
          }
          return null;
      }

      public static void main(String[] args) {
          System.out.println(getMessage("2"));
      }
  }