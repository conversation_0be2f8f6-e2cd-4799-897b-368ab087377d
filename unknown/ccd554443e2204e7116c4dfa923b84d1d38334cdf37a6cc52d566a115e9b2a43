package com.wosai.upay.job.model;

import com.wosai.upay.job.enume.PlatformEnum;
import com.wosai.upay.job.validation.In;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
@Data
public class OnlinePaymentFailMessageQueryReq {

    @NotBlank(message = "商户号不能为空")
    private String merchantSn;

    @NotNull(message = "支付源错误")
    @In(values = {2, 3}, message = "支付源错误")
    private Integer payway;

    @NotEmpty(message = "平台不能为空")
    private List<PlatformEnum> platform;
}
