package com.wosai.upay.job.Constants;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2023/7/31 下午3:24
 */
public class PayLaterConstant {


    /**
     * 状态常量
     */
    public static class Status {
        /**
         * 支付宝内部审核
         */
        public static final Integer ALI_APPLYING = 0;

        /**
         * 芝麻先享平台审核
         */
        public static final Integer ZHIMA_APPLYING = 10;

        /**
         * 创建蚂蚁门店中
         */
        public static final Integer ANT_SHOP_APPLYING = 50;

        /**
         * 申请成功
         */
        public static final Integer SUCCESS = 20;

        /**
         *支付宝内部审核失败
         */
        public static final Integer ALI_FAIL = 30;

        /**
         * 创建蚂蚁门店失败
         */
        public static final Integer ANT_SHOP_FAIL = 60;


        /**
         *芝麻先享平台审核失败
         */
        public static final Integer ZHIMA_FAIL = 40;
    }



    /**
     * 状态常量
     */
    public static class SubStatus {
        /**
         * 直付通商户申请中
         */
        public static final Integer ZFT_APPLYING = 0;

        /**
         * 待确认
         */
        public static final Integer CONFIRMING = 10;

        /**
         * 芝麻商户审核中
         */
        public static final Integer ZHIMA_APPLYING = 20;

        /**
         * 创建蚂蚁门店中
         */
        public static final Integer ANT_SHOP_APPLYING = 50;

        /**
         *申请成功
         */
        public static final Integer SUCCESS = 30;


        /**
         *申请失败
         */
        public static final Integer FAIL = 40;
    }

    /**
     * 过程状态常量
     */
    public static class ProcessStatus {
        /**
         * 直付通商户待提交
         */
        public static final Integer PENDING = 0;

        /**
         * 直付通商户申请中
         */
        public static final Integer ZFT_APPLYING = 10;

        /**
         * 待签约
         */
        public static final Integer CONFIRMING = 20;

        /**
         *直付通商户创建成功
         */
        public static final Integer ZFT_SUCCESS = 30;

        /**
         * 蚂蚁门店申请中
         */
        public static final Integer ANT_SHOP_APPLYING = 40;


        /**
         *蚂蚁门店创建成功
         */
        public static final Integer ANT_SHOP_SUCCESS = 50;

        /**
         *芝麻商户审核中
         */
        public static final Integer ZHIMA_APPLYING = 60;

        /**
         *申请成功
         */
        public static final Integer ZHIMA_SUCCESS = 70;


        /**
         *申请失败
         */
        public static final Integer FAIL = 80;
    }


    /**
     * Extra字段的key值,仅限参考后续拓展以实际代码为准
     */
    public static class Extra {


        /**
         * 当前商户费率
         */
        public static final String FEERATE = "feeRate";

        /**
         * 记录spa中所需要的状态流转
         */
        public static final String SPA_STAGE = "spaStage";

        /**
         * 流程状态
         */
        public static final String PROCESS_STATUS = "processStatus";

        /**
         * 蚂蚁门店Id
         */
        public static final String ANT_SHOP_ID = "antShopId";

        /**
         * 蚂蚁门店申请单Id
         */
        public static final String ANT_SHOP_ORDER_ID = "antShopOrderId";


        /**
         * 创建芝麻参数的业务传参(biz_content)用于费率更新的时候保持原有参数,避免触发审批
         */
        public static final String BIZ_CONTENT = "biz_content";

        /**
         * 回调通知信息
         */
        public static final String NOTIFY = "notify";



    }


    /**
     * 先享后付结果提示
     */
    public static class Result {
        public static final String  QUERY_ZFT_AUDIT_FAIL = "查询直付通审核结果异常";
        public static final String  ZHIMA_AUDIT_FAIL = "芝麻审核结果异常";

        public static final String ANT_SHOP_APPLYING = "尊敬的收钱吧商户，您的资料已审核通过，请使用申请的支付宝账号登录支付宝完成授权\n手机端授权路径：打开手机支付宝>点击消息>找到商家平台待办申请待确认>查看并确认";
        public static final String SUCCESS = "尊敬的收钱吧商户，您申请的签单易功能已开通成功，您可前往APP首页-签单易-专属收款码下载并打印，后续顾客使用专属门店码付款时可享受先下单后付费权益。";

    }

}
