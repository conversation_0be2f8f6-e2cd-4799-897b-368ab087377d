package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskReq;
import com.wosai.upay.job.model.MchAuthInfo;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: lishuangqiang
 * @Date: 2019/4/1 14:54
 * @Description:记录和查询子任务
 */
@JsonRpcService("/rpc/subtaskResult")
@Validated
public interface SubtaskResultService {

    ContractSubTask getSubtasksByPtaskIdAndChannel(@NotNull(message = "p_task_id不能为null") Long p_task_id, @NotBlank(message = "channel不能为空") String channel);

    List<ContractSubTask> getSubtasksByMerchantAndPayway(@NotBlank(message = "merchant_sn不能为空") String merchant_sn, @NotNull(message = "p_task_id不能为null") Long p_task_id);

    ContractSubTask selectByPrimaryKey(@NotNull(message = "id不能为null") Long id);

    /**
     * 通过taskId查找商家认证申请信息
     *
     * @param takId
     * @return
     */
    MchAuthInfo getAuthApplyByTaskId(@NotNull(message = "taskid不能为null") Long takId);


    ContractSubTask getAcquireSubTask(@NotNull(message = "p_task_id不能为null") Long pTaskId);

    /**
     * @Author: zhmh
     * @Description: 获取商户号下, 指定支付方式的所有进件任务
     * @time: 14:21 2021/1/22
     */
    List<ContractSubTask> getContractSubTask(@Validated ContractSubTaskReq req);

    /**
     * 查询 收单机构 报备记录 [会向收单机构实时同步一次]
     * @param merchantSn    商户号
     * @param contractRule  收单机构
     * @return
     */
    ContractSubTask getProviderMerchantContractResult(String merchantSn, String contractRule);

}