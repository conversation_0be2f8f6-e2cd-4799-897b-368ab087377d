package com.wosai.upay.job.model;

import com.wosai.upay.merchant.contract.model.provider.ChannelParam;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/3/17 5:11 下午
 */
@Data
@Accessors(chain = true)
public class ContractSubTaskAndParam {
    /**
     * 子任务
     */
    private ContractSubTask contractSubTask;
    /**
     * 收单机构参数
     */
    private ChannelParam channelParam;
}
