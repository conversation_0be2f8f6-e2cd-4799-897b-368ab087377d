package com.wosai.upay.job.model;

import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 收单机构终端与支付源商户号绑定关系
 * <AUTHOR>
 * @Date 2023/4/7 16:04
 **/

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Getter
public class ProviderTerminalBindConfigResp extends ProviderTerminalBindConfig {

    private String acquirer;

    private String acquirerName;

    private Long createAtTimestamp;

    private String associateType;

    private String associateSn;

    private Integer disableStatus;

    private String disableReason;

}