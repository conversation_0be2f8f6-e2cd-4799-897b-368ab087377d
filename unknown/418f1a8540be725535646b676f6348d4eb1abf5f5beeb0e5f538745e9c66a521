package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropSize;
import org.springframework.validation.annotation.Validated;

import java.util.Map;


/**
 * Created by lihebin on 2018/9/7.
 */
@JsonRpcService("/rpc/tradeParams")
@Validated
@Deprecated
public interface MerchantConfigParamsService {

    /**
     * 切换商户通道通用接口
     * merchant_id
     * payway
     * provider
     * agent_name
     * provider_merchant_id
     * alipay_merchant_id
     * weixin_merchant_id
     * weixin_app_id
     * weixin_sub_appsecret
     * weixin_mini_app_id
     * weixin_mini_sub_appsecret
     *
     * @param params
     * @return
     */
    boolean updateMerchantConfigProviderCommon(@PropNotEmpty.List({
            @PropNotEmpty(value = "merchant_id", message = "{value} 商户Id不能为空"),
            @PropNotEmpty(value = "payway", message = "{value} 支付方式不能为空"),
            @PropNotEmpty(value = "provider", message = "{value} provider不能为空"),
            @PropNotEmpty(value = "agent_name", message = "{value} agent_name不能为空")
    })
                                               @PropSize.List({
                                                       @PropSize(value = "fee_rate", max = 45, message = "{value}费率不可超过{max}字符")
                                               })
                                                       Map params);


    /**
     * 商户信息授权码
     * 测试使用
     *
     * @param info
     * @param codeName
     * @return
     */
    String authorizationCodeUrl(Map info, String codeName) throws Exception;

}
