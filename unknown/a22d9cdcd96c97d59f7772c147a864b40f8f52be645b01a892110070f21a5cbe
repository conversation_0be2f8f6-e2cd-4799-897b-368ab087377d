package com.wosai.upay.job.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: bluesea terminal_info
 * <AUTHOR>
 * @Date 2020/12/3 1:54 PM
 **/
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TerminalInfo {
    @JSONField(name = "device_fingerprint")
    private String deviceFingerprint;

    private int bindStatus;

    private String bindMsg;

    private int queryStatus;

    private String queryMsg;

    private int retry = 0;

    public boolean isQuerySuccess() {
        return queryStatus >= 200 && queryStatus < 300;
    }

    //报警
    public boolean isQueryBusinessFail() {
        return queryStatus >= 300 && queryStatus < 500;
    }

    //要重试
    public boolean isQuerySystemFail() {
        return queryStatus == 500;
    }


    public boolean isBindSuccess() {
        return bindStatus >= 200 && bindStatus < 300;
    }

    //报警
    public boolean isBindBusinessFail() {
        return bindStatus >= 300 && bindStatus < 500;
    }

    //要重试
    public boolean isBindSystemFail() {
        return bindStatus == 500;
    }

}