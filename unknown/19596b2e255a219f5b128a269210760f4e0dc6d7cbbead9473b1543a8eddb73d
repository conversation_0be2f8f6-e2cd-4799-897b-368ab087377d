package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 *
 * 费率模板相关
 * <AUTHOR>
 */
@JsonRpcService("/rpc/feeRate")
@Validated
public interface FeeRateService{

    /**
     * 根据富友模板获取费率信息
     *
     * @param templateCd   富友模板id
     * @return 枚举映射列表
     */
    String getFeeRateByTemplate(@NotBlank(message = "富友模板不能为空") String templateCd);

}
