package com.wosai.upay.job.model.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收单机构支持结算类型请求dto
 *
 * <AUTHOR>
 * @date 2024/3/28 15:29
 */
@Data
@ApiModel("收单机构支持结算类型")
public class AcquirerSupportSettlementRepDTO {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Integer id;
    /**
     * 收单机构
     */
    @ApiModelProperty("收单机构")
    private String acquirer;
    /**
     * 营业执照类型 对应merchant_business_license.type
     */
    @ApiModelProperty("营业执照类型")
    private Integer businessLicenseType;
    /**
     * 法人对私支持类型   0-不支持 1-支持
     */
    @ApiModelProperty("法人对私支持类型 0-不支持 1-支持")
    private Integer legalPrivateSupportType;
    /**
     * 非法人对私支持类型 0-不支持 1-支持
     */
    @ApiModelProperty("非法人对私支持类型 0-不支持 1-支持")
    private Integer nonLegalPrivateSupportType;
    /**
     * 普通对公支持类型   0-不支持 1-支持
     */
    @ApiModelProperty("普通对公支持类型 0-不支持 1-支持")
    private Integer commonPublicSupportType;
    /**
     * 其他对公支持类型   0-不支持 1-支持
     */
    @ApiModelProperty("其他对公支持类型 0-不支持 1-支持")
    private Integer otherPublicSupportType;
}
