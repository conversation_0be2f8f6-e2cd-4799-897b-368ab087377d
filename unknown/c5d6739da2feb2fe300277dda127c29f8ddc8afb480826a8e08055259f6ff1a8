  package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


  /**
   * @Description: 平安入网状态对应的描述
   * <AUTHOR>
   * @Date  2024/10/29 09:29
   */
  @Getter
  @AllArgsConstructor
  public enum PabSubmitStatusEnum {
      WAIT("0", "未完成"),
      DONE("1", "已完成"),

              ;

      private String code;
      private String message;


      public static String getMessage(String code) {
          final PabSubmitStatusEnum[] descriptions = values();
          for (PabSubmitStatusEnum description : descriptions) {
              if (Objects.equals(description.getCode(), code)) {
                  return description.getMessage();
              }
          }
          return null;
      }

      public static void main(String[] args) {
          System.out.println(getMessage("1"));
      }
  }