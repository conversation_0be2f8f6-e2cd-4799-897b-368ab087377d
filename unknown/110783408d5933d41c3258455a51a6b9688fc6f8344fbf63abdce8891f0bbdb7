package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.upay.job.model.ChannelActivity;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonRpcService("/rpc/weixinFeeRateActivity")
@Validated
public interface WeixinFeeRateActivityService {

    /**
     * 是否完成使命认证
     *
     * @param mchId
     * @return
     */
    boolean isAuth(String mchId);

    /**
     * 提交费率活动申请
     *
     * @param auditId
     * @param info
     * @return
     */
    CommonResult addApply(String auditId, Map<String, Object> info);

    /**
     * 提交或更新资料到微信
     *
     * @param r
     * @param params
     * @throws Exception
     */
    void submitOrUpdateToWechat(ChannelActivity r, MerchantProviderParamsDto params) throws Exception;

    /**
     * 查询申请状态
     *
     * @param r
     * @param params
     * @throws Exception
     */
    void queryStatus(ChannelActivity r, MerchantProviderParamsDto params) throws Exception;

    /**
     * 将状态改为已提交
     *
     * @param r
     */
    void statusToSubmit(ChannelActivity r);

    /**
     * 对微信审核通过的申请切换交易参数
     *
     * @param startTime
     * @param limit
     */
    void changeParams(Long startTime, Integer limit);

    /**
     * 对微信审核通过的申请切换交易参数
     *
     * @param id
     */
    void changeOneParams(Long id);

    /**
     * 微信超时未授权将任务变成失败
     * @param r
     */
    void setFail(ChannelActivity r);

    /**
     * <AUTHOR>
     * @Description: 取消商户高校食堂活动
     * @time 11:05
     */
    CallBackBean cancelSchoolCanteen(String merchantSn, CallBackBean callBackBean,String auditSn);

    /**
     * 通过 mchId 获取活动报名
     * @param mchId
     * @return
     */
    ChannelActivity getSuccessActivityByMchId(String mchId);
}
