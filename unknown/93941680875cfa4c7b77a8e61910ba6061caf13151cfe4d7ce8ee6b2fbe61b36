package com.wosai.upay.job.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.model.dto.WeixinSubAppidDto;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5
 */
@Data
public class WeixinSubAppidAddReq {

    @NotEmpty(message = "商户号不能为空")
    @JsonProperty("merchant_sn")
    private String merchantSn;

    @Valid
    @JsonProperty("appid_dto")
    private WeixinSubAppidDto appidDto;

    /**
     * 支付目录
     */
    @JsonProperty("pay_auth_path")
    private List<String> payAuthPath;
}
