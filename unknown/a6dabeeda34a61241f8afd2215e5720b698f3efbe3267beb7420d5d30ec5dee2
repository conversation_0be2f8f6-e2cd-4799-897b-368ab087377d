package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@Data
@Accessors(chain = true)
public class DirectStatus {

    /**
     * 待处理
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 1;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 2;

    /**
     * 业务失败
     */
    public static final int STATUS_BIZ_FAIL = 3;

    /**
     * 等待账户验证
     */
    public static final int STATUS_WAIT_FOR_VERIFY = 11;
    /**
     * 等待签约
     */
    public static final int STATUS_WAIT_FOR_SIGN = 12;
    /**
     * 等待授权
     */
    public static final int STATUS_IN_OPENING_PERMISSION = 13;

    private Long id;

    private String merchant_sn;

    private String dev_code;

    private Integer status;

    private Date create_at;

    private Date update_at;

}
