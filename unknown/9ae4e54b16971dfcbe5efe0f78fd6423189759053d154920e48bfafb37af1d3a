package com.wosai.upay.job.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnionPayOpenStatusQueryResp {

    public static final Integer NOT_OPEN = 1;
    public static final Integer SUCCESS_OPEN = 2;
    public static final Integer FAIL_OPEN = 3;

    /**
     * 开通状态 1待开通 2开通成功 3开通失败
     */
    private Integer status;
    /**
     * 需要展示的文案
     */
    private String message;
    /**
     * 是否允许重试
     */
    private Boolean retry;
    /**
     * 是否是兜底文案
     */
    private Boolean fallBack;
}
