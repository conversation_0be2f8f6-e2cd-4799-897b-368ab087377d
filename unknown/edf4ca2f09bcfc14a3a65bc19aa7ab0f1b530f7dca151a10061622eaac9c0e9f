package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractEvent;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * @Auther: lishuangqiang
 * @Date: 2019/3/29 09:14
 * @Description:记录自助入网事件
 */
@JsonRpcService("/rpc/selfHelpNetInEvent")
@Validated
public interface SelfHelpNetInEventService {
    Long saveSelfHelpNetInEvent(@NotBlank(message="merchantSn不能为空") String merchantSn, int optType, Map<String, Object> message, List dataList, Map source);

    ContractEvent selectSelfHelpNetInEventBymerchantSnAndtaskId(String merchantSn, int taskId);

    int updateByPrimaryKeySelective(ContractEvent record);
}
