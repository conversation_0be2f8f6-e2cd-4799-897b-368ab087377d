package com.wosai.upay.job.Constants;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
public class TradeConstants {

    public static final int PAYWAY_ACQUIRER = 0;
    public static final int PAYWAY_ALIPAY = 1;
    public static final int PAYWAY_ALIPAY2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BAIFUBAO = 4;
    public static final int PAYWAY_JDWALLET = 5;
    public static final int PAYWAY_QQWALLET = 6;
    public static final int PAYWAY_APPLEPAY = 7;
    public static final int PAYWAY_LAKALAWALLET = 8;
    public static final int PAYWAY_CMCC = 9; //和支付
    public static final int PAYWAY_UNIONPAY = 17;// 云闪付
    public static final int PAYWAY_BESTPAY = 18;// 翼支付
    public static final int PAYWAY_WEIXIN_HK = 19;// 微信香港本地支付
    public static final int PAYWAY_ALIPAY_INTL = 20;// 支付宝国际版
    public static final int PAYWAY_BANKCARD = 21;// 银行卡
    public static final int PAYWAY_DECP = 23;// 数字货币
    public static final int PAYWAY_GIFT_CARD = 101;// 礼品卡

    public static final int PROVIDER_CIBBANK = 1001;
    public static final int PROVIDER_LAKALA = 1002;
    public static final int PROVIDER_CITICBANK = 1003;
    public static final int PROVIDER_CIBGZBANK = 1008;
    public static final int PROVIDER_LKLWANMA = 1011;
    public static final int PROVIDER_NUCC = 1013;
    public static final int PROVIDER_UNIONPAY = 1014;
    public static final int PROVIDER_CIBSHBANK = 1015;
    public static final int PROVIDER_DIRECT_UNIONPAY = 1016;
    public static final int PROVIDER_UNIONPAY_OPEN = 1017; //银联开放平台
    public static final int PROVIDER_CHINAUMS = 1018; //银联商务
    public static final int PROVIDER_UNIONPAY_ONLINE = 1019; //银联网银支付
    public static final int PROVIDER_TONGLIAN = 1020; //银联网银支付
    public static final int PROVIDER_WEIXIN = 3; //微信直连
    public static final int PROVIDER_ALIPAY = 2; //支付宝直连
    public static final int PROVIDER_BESTPAY = 18; //翼支付直连

    public static final int SUB_PAYWAY_BARCODE = 1; //b2c
    public static final int SUB_PAYWAY_QRCODE = 2;//c2b
    public static final int SUB_PAYWAY_WAP = 3; //wap
    public static final int SUB_PAYWAY_MINI = 4; //mini
    public static final int SUB_PAYWAY_APP = 5; //app支付
    public static final int SUB_PAYWAY_H5 = 6; //h5支付


    public static final int STATUS_USED = 0;
    public static final int STATUS_NOT_USED = 1;

    public static final String SUB_PAYWAY_TRADE_NAME = "BSC/CSB";
    public static final String SUB_PAYWAY_WAP_NAME = "WAP";
    public static final String SUB_PAYWAY_MINI_NAME = "小程序支付";
    public static final String SUB_PAYWAY_APP_NAME = "APP支付";
    public static final String SUB_PAYWAY_H5_NAME = "H5支付";

    public static final String OLD_FEE_RATE = "old_fee_rate";
    public static final String OLD_AGENT_NAME = "old_agent_name";
}
