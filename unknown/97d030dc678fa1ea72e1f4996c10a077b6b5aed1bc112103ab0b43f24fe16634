package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskAndParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;

/**
 * @Description: ums回调接口 merchant-contract调用
 * <AUTHOR>
 * @Date: 2021/3/16 11:24 上午
 */
@JsonRpcService("/rpc/umsCallBack")
@Validated
public interface UmsCallBackService {

    /**
     * 查询任务信息
     *
     * @param contractId
     * @return
     */
    ContractSubTask queryContractSubTaskByContractId(@NotEmpty(message = "contractId不能为空") String contractId);

    /**
     * 查询任务信息 包括收单机构参数
     *
     * @param contractId
     * @return
     */
    ContractSubTaskAndParam getContractSubTaskAndParam(@NotEmpty(message = "contractId不能为空") String contractId);

    /**
     * 银联商务回调 接口处理
     *
     * @param contractId
     * @param contractResponse
     * @return
     */
    Boolean umsCallBackHandleTask(@NotEmpty(message = "contractId不能为空") String contractId, @NotEmpty(message = "银联商务返回信息不能为空") ContractResponse contractResponse);


}
