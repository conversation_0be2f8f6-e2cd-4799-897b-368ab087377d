package com.wosai.upay.job.enume;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @Description: 子商户号用途枚举
 * <AUTHOR>
 * @Date 2021/1/19 15:40
 */
@Getter
@AllArgsConstructor
public enum WxUseType {
    NORMAL(1, "一般用途"),
    SCHOOL_CANTEEN(2, "微信校园食堂活动"),
    OFFLINE_EDU_TRAIN(3, "微信线下教培活动"),
    ONLINE_PAYMENT(4, "线上收款"),
    SCENARIO_PAYMENT(5, "场景支付")
    ;
    private Integer code;
    private String message;

    public static String getMessage(Integer code) {
        final WxUseType[] subMerchantIdUseTypes = values();
        for (WxUseType subMerchantIdUseType : subMerchantIdUseTypes) {
            if (Objects.equals(subMerchantIdUseType.getCode(), code)) {
                return subMerchantIdUseType.getMessage();
            }
        }
        return null;
    }
}