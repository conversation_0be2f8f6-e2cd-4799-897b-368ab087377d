package com.wosai.upay.job.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.upay.job.validation.group.AddContractRule;
import com.wosai.upay.job.validation.group.UpdateContractRule;
import lombok.Data;
import lombok.ToString;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * ContractRuleDto
 *
 * <AUTHOR>
 * @date 2019-07-16 17:16
 */
@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContractRuleDto implements Serializable {
    private static final long serialVersionUID = -8464953968874209605L;
    private Integer id;

    @NotBlank(message = "规则唯一标识 rule 不能为空", groups = UpdateContractRule.class)
    private String rule;

    @NotBlank(message = "name 规则名称不能为空", groups = AddContractRule.class)
    private String name;

    private Integer payway;

    private String provider;

    private String acquirer;

    @NotBlank(message = "channel 报备渠道不能为空", groups = AddContractRule.class)
    private String channel;

    @Max(message = "0 表示禁用, 1表示启用, 请填写合理值", groups = AddContractRule.class, value = 1)
    @Min(message = "0 表示禁用, 1表示启用, 请填写合理值", groups = AddContractRule.class, value = 0)
    private Integer status;

    @NotNull(message = "type 报备类型不能为空", groups = AddContractRule.class)
    private Integer type;

    private Integer retry;

    private Boolean is_default;

    private Boolean is_insert;

    private Boolean is_insert_influ_ptask;

    private Boolean is_update;

    private Boolean is_update_influ_ptask;

    private Date create_at;

    private Date update_at;

    private Map<String, Object> metadata;
}
