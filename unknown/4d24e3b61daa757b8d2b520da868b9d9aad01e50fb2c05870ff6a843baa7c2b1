package com.wosai.upay.job.model.dto.request;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 切收单机构请求参数
 *
 * <AUTHOR>
 * @date 2025/1/8 17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangeAcquirerReqDTO {

    /**
     * 商户号
     */
    @NotBlank(message = "日志参数不能为空")
    private String merchantSn;

    /**
     * 目标收单机构
     */
    @NotNull(message = "目标收单机构不能为空")
    private AcquirerTypeEnum targetAcquirerTypeEnum;

    /**
     * 是否立即切换收单机构 true-立刻切换,false-夜晚切换 默认false
     */
    private Boolean immediately;

    /**
     * 业务方 默认移动支付业务
     */
    private String tradeAppId;

    /**
     * 强制切换收单机构 true-强制切换,false-校验业务规则 默认false
     */
    private Boolean forceChange;

    private ChangeAcquirerReqDTO(Builder builder) {
        this.merchantSn = builder.merchantSn;
        this.targetAcquirerTypeEnum = builder.targetAcquirerTypeEnum;
        this.immediately = builder.immediately;
        this.tradeAppId = builder.tradeAppId;
        this.forceChange = builder.forceChange;
    }

    public static class Builder {
        private final String merchantSn;
        private final AcquirerTypeEnum targetAcquirerTypeEnum;
        private Boolean immediately;
        private String tradeAppId;
        private Boolean forceChange;

        public Builder(String merchantSn, AcquirerTypeEnum targetAcquirerTypeEnum) {
            if (merchantSn == null || merchantSn.isEmpty()) {
                throw new ContractBizException("merchantSn must not be null or empty");
            }
            if (targetAcquirerTypeEnum == null) {
                throw new ContractBizException("targetAcquirerTypeEnum must not be null");
            }
            this.merchantSn = merchantSn;
            this.targetAcquirerTypeEnum = targetAcquirerTypeEnum;
        }

        public Builder immediately(Boolean immediately) {
            this.immediately = immediately;
            return this;
        }

        public Builder tradeAppId(String tradeAppId) {
            this.tradeAppId = tradeAppId;
            return this;
        }

        public Builder forceChange(Boolean forceChange) {
            this.forceChange = forceChange;
            return this;
        }

        public ChangeAcquirerReqDTO build() {
            return new ChangeAcquirerReqDTO(this);
        }
    }
}
