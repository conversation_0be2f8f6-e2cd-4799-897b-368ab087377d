package com.wosai.upay.job.model.dto.crm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * crm信息管理，提交返回结果
 *
 * <AUTHOR>
 * @date 2025/2/10 17:57
 */
@Data
public class SubmitResultForCrmInfoManageDTO {

    /**
     * 待提交
     */
    public static final int SUBMIT_CHECK_RESULT_PENDING = 0;

    /**
     * 审核中
     */
    public static final int SUBMIT_CHECK_RESULT_AUDITING = 1;

    public static final int SUBMIT_CHECK_RESULT_AUDIT_SUCCESS = 2;

    public static final int SUBMIT_CHECK_RESULT_AUDIT_FAIL = 3;

    public static final int SUBMIT_CHECK_RESULT_SUCCESS = 1;

    private Integer status;

    @JsonProperty("fail_code")
    private Integer failCode;

    @JsonProperty("fail_msg")
    private String failMsg;

    @JsonProperty("app_sub_status_text")
    private String appSubStatusText;

    public static SubmitResultForCrmInfoManageDTO fail(String failMsg) {
        SubmitResultForCrmInfoManageDTO submitResultForCrmInfoManageDTO = new SubmitResultForCrmInfoManageDTO();
        submitResultForCrmInfoManageDTO.setStatus(SUBMIT_CHECK_RESULT_AUDIT_FAIL);
        submitResultForCrmInfoManageDTO.setFailMsg(failMsg);
        return submitResultForCrmInfoManageDTO;
    }

    public static SubmitResultForCrmInfoManageDTO success() {
        SubmitResultForCrmInfoManageDTO submitResultForCrmInfoManageDTO = new SubmitResultForCrmInfoManageDTO();
        submitResultForCrmInfoManageDTO.setStatus(SUBMIT_CHECK_RESULT_SUCCESS);
        return submitResultForCrmInfoManageDTO;
    }
}
