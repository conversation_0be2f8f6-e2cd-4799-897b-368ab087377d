package com.wosai.upay.job.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
@Data
@Accessors(chain = true)
public class AppModuleResponse {

    public static final String SUCCESS_OPEN = "开通成功";
    public static final String FAIL_OPEN = "开通失败";
    public static final String WAIT_OPEN = "在线开通";
    public static final String PROCESS_OPEN = "审核中";

    /**
     * 是否可见
     */
    private boolean visible;
    /**
     * 显示的文案
     */
    private String letters;
    /**
     * 跳转页面链接
     */
    private String subpage;
}
