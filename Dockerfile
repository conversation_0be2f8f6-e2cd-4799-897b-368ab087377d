FROM jfrog.wosai-inc.com/docker-virtual-prod/hera:1.9.0

EXPOSE 8080
WORKDIR /app/bin

#ADD sources.list /etc/apt/sources.list
ADD arthas-boot.jar /app/bin/arthas-boot.jar
ADD arthas.sh /app/bin/arthas.sh
#ADD merchant-contract-job-war/target/merchant-contract-job.jar /app/bin/merchant-contract-job.jar
COPY merchant-contract-job-war/target/merchant-contract-job.jar ./app.jar

CMD ["java", "-jar", "/app/bin/app.jar"]


