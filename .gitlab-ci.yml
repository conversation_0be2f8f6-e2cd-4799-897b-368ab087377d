# 引用git.wosai-inc.com/do/gitlab-ci这个项目中的java-docker.yml作为CI模板
include:
  project: "do/gitlab-ci"
  file: "/maven.yml"

# 以下是选填项
variables:
#  MAVEN_CLI_OPTS: "-Dmaven.test.skip=True sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=**************************************** -Dsonar.core.codeCoveragePlugin=jacoco -Dsonar.exclusions=**/exception/*,**/model/**,**/mapper/**,**/avro/**,**/config/**,**/enume/**,**/monitor/**"
  MAVEN_CLI_OPTS: "-Dmaven.test.skip=True"
  JFROG_MVNC_OPTS: "--exclude-patterns=*war.jar"