version: '2'
services:
  merchant-contract-job:
    build: .
    image: registry.wosai-inc.com/merchant-contract-job:$tag
    restart: always
    container_name: merchant-contract-job
    environment:
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx3G -XX:-UseGCOverheadLimit -Dspring.profiles.active=prod {% if inventory_hostname == 'merchant-job-001' %}-Dlog.warn.enable=true{% endif %} -DlogDir=/app/log
    ports:
      - "11157:8080"
    volumes:
      - /app/log/merchant-contract-job:/app/log
      - /app/log/merchant-contract-job/wechat_applet_code:/app/log/wechat_applet_code
      - /opt/data:/opt/data
      - /opt/settings:/opt/settings
    network_mode: bridge
    extra_hosts:
      - "merchant-contract-internal.shouqianba.com:***********"
      - "upay-v2-internal.shouqianba.com:************"
      - "notice-service.internal.shouqianba.com:***********"
      - "internal.app.push.shouqianba.com:***********"
      - "internal.backstage.shouqianba.com:***********"
      - "business-log.internal.shouqianba.com:***********"
      - "opr-merchant-activity.internal.shouqianba.com:***********"